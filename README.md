# Google DeepMind Research Engineer Interview Prep

This repository is a focused preparation hub for a Research Engineer interview at Google DeepMind. It consolidates hands-on algorithm practice, systems-oriented data structures, and core ML/AI fundamentals commonly assessed in technical interviews.

## What's inside
- `data-structures-algorithms/`: Curated implementations and practice problems across arrays/strings, graphs, trees, DP, greedy, heaps, tries, and more. Includes a comprehensive Algorithms Cheat Sheet at `data-structures-algorithms/algorithms-cheat-sheet.md`. See the detailed study plan and checklists in `data-structures-algorithms/README.md`.
- `ml-fundamentals/`: Structured notes and references for foundational ML topics (including RL). See `ml-fundamentals/README.md` and topic guides.

## How to use
1. Start with `data-structures-algorithms/algorithms-cheat-sheet.md` for a quick, self-contained review of patterns and templates. Then follow `data-structures-algorithms/README.md` for the prioritized roadmap (including the emergency prep checklist).
2. Use the implementations as templates for solving variants; practice writing solutions from scratch and validating time/space complexity.
3. Review `ml-fundamentals/` for conceptual depth, with emphasis on topics relevant to DeepMind research engineering.

## During Interview

**Soft skills and attitude**

- Confident technical skills
- But humble and easy to work with

**Interview process**

1. **Before giving the problem**
    - Don’t talk non-coding question related issues too long at beginning, save for later
2. **Before solution**
    - don’t rush to code
        - clarify corner cases
        - boundary
        - requirements
        - assumption
3. **Working on solution**
    - Identify with existing patterns, **recursion**? backtracking? Stack, queue? tree, graph, prefix sum?
    - Build consensus by discussion, then move to coding (pseudocode may not be required)

## Environment
This project uses Python with a modern dependency workflow (via `pyproject.toml`). If you use [uv](https://docs.astral.sh/uv/):

```bash
uv sync
uv run python -V
```

## Goal
Build speed, clarity, and depth across algorithms, data structures, and ML fundamentals to perform strongly in research engineering interviews at Google DeepMind.
