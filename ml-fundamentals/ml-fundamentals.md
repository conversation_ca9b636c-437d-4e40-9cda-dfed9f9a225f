# Common ML Fundamentals Interview Questions (Google DeepMind Research Engineer)

A structured list you can annotate, reorder, or expand with notes and sample answers.

---

## Fundamentals

- **Types of Learning**  
  **Q:** What are the differences between supervised, unsupervised, and reinforcement learning? (Explain each and provide an example use case.)  
  **A:** Supervised: learn f(x)->y from labeled pairs (x,y); minimize predictive loss (e.g., cross-entropy/MSE) on i.i.d. data; examples: image classification, spam detection, translation. Unsupervised: discover structure in x without labels—objectives include density modeling (MLE/VAEs), representation/contrastive learning, and clustering; examples: PCA, k-means, self-supervised pretraining. Reinforcement learning: sequential decision-making in an MDP; an agent interacts with an environment, receives scalar rewards, and maximizes expected return; challenges include exploration–exploitation, credit assignment, and off-policy distribution shift; examples: AlphaGo/Atari, robotics, recommender bandits.

- **Bias vs. Variance**  
  **Q:** How would you describe the bias–variance trade-off in machine learning? (Why is this trade-off important for model generalization?)  
  **A:** Bias measures how far predictions deviate from true values on average (underfitting); variance measures prediction sensitivity to training set changes (overfitting). Decomposition: E[(f̂(x) - f(x))²] = Bias² + Variance + Irreducible Error. High bias = oversimplified model (linear on nonlinear data); high variance = overcomplex model (memorizes noise). Trade-off: increasing model complexity typically reduces bias but increases variance. Generalization requires balancing both—minimizing total expected error, not just training error. Managed via regularization, cross-validation, ensemble methods, and proper model selection. 

- **Overfitting and Underfitting**  
  **Q:** What is overfitting versus underfitting in a model, and how can you identify each?  
  **A:** Overfitting: low train loss, high val/test loss; memorizes noise. Signs: large train–val gap, spiky metrics, sensitivity to perturbations. Underfitting: high train and val loss; model too simple. Diagnose with learning curves. Fix overfitting: more data/augmentation, L2/weight decay, dropout, simpler model, early stopping, fewer steps. Fix underfitting: more capacity/width/depth, train longer, richer features, reduce regularization, better optimization.

- **Regularization (L1 vs L2)**  
  **Q:** What is regularization in machine learning and how do L1 and L2 regularization differ? (When might you prefer one over the other?)  
  **A:** Regularization adds penalties to discourage complexity. L2 (ridge/weight decay) shrinks weights smoothly toward zero—no sparsity; with correlated features, it distributes the penalty evenly across them, handling multicollinearity well by shrinking all correlated weights together without favoring one. Mathematically, L2's quadratic penalty (∑βᵢ²) creates equal pressure on all coefficients, so when features x₁ and x₂ are highly correlated, L2 will shrink both β₁ and β₂ proportionally, maintaining their relationship while reducing magnitude.

  L1 (lasso) drives exact zeros—feature selection; with correlated features, it tends to arbitrarily select one from the group and set the others to zero, leading to instability and high variance in which features are selected across different datasets or random seeds. This occurs because L1's linear penalty (∑|βᵢ|) creates "corners" in the constraint region—when the optimization hits these corners, it zeros out coefficients. With perfect correlation, L1 can achieve the same loss by using any single feature from the correlated group, making the selection arbitrary and dependent on numerical precision or initialization.

  Example: If features "house_size_sqft" and "house_size_sqm" are perfectly correlated (sqm = sqft/10.76), L2 will assign weights like β₁=0.8, β₂=0.74, maintaining their relationship. L1 might select β₁=1.5, β₂=0 in one run but β₁=0, β₂=16.1 in another, despite identical predictive power. This makes L1 less stable in the presence of multicollinearity, while L2 provides more consistent shrinkage. Elastic Net (α·L1 + (1-α)·L2) mixes both to achieve sparsity while maintaining stability in correlated settings. With adaptive optimizers, prefer decoupled weight decay (AdamW) over L2 penalty.

- **Gradient Descent Variants**  
  **Q:** Explain the difference between batch gradient descent, stochastic gradient descent, and mini-batch gradient descent. (What are the pros and cons of each approach?)  
  **A:** Batch GD: full dataset per step—stable but slow/expensive. SGD (1 example): very noisy, explores well; needs small lr, noisy convergence. Mini-batch (e.g., 32–4096): hardware‑efficient, good bias–variance trade‑off; default in practice. Large batches often require learning‑rate scaling and warmup: with the linear scaling rule, increase the base lr roughly in proportion to batch size (e.g., 2× batch → ~2× lr), then use a short warmup (≈0.5–5% of total steps) to ramp lr from 0 to target to avoid early instability. Alternatives include sqrt‑scaling when training is brittle and one‑cycle/cosine schedules after warmup. Very large batches can hurt generalization; mitigate with added noise/regularization (e.g., dropout, label smoothing), tuned weight decay, SWA/SWAD, or reduce effective batch via gradient accumulation.

- **Feature Scaling / Normalization**  
  **Q:** When is feature scaling required, and how does it affect optimization dynamics (learning rate sensitivity, conditioning) and regularization?  
  **A:** I scale features whenever input dimensions are on different units or orders of magnitude, or when the optimizer assumes reasonably isotropic curvature. Standardizing to zero mean and unit variance improves conditioning, so a single learning rate works better and gradients are less erratic. This typically accelerates convergence and reduces sensitivity to initialization. It also makes L1/L2 penalties comparable across coefficients, which yields more meaningful regularization. I skip scaling only for inherently scale‑invariant models (e.g., trees) or when the pipeline explicitly handles units.

- **Bayesian vs Frequentist (practical)**  
  **Q:** Contrast the paradigms and discuss when you'd prefer each (e.g., small-n, hierarchical priors, regularization-as-prior), including implications for calibration and uncertainty estimates.  
  **A:** In practice, I reach for Bayesian methods when I have informative priors, small datasets, hierarchical structure, or I need calibrated uncertainty. Priors act like regularizers (e.g., Gaussian ≈ L2, Laplace ≈ L1) and hierarchical priors pool strength across related groups. Frequentist estimators are my default when data are plentiful, computation must be minimal, or I only need point estimates and standard errors. Bayesian posteriors tend to give better‑calibrated probabilities out of the box, while frequentist approaches often require post‑hoc calibration.

- **Convex vs Non-Convex Optimization**  
  **Q:** Why does convexity matter for ML? (Global optima guarantees, efficient algorithms.) Give convex vs non-convex examples and practical implications for initialization and optimization strategy.  
  **A:** Convexity matters because any local minimum is global and we have strong convergence guarantees with simple algorithms. Linear/logistic regression with convex regularizers are classic examples. Deep networks are non‑convex; they have many basins, so initialization, normalization, learning‑rate schedules, and restarts matter. In non‑convex settings I rely on good initializations, robust optimizers (AdamW/SGD+momentum), and monitoring to escape bad regions.

- **CNNs vs RNNs**  
  **Q:** How do convolutional neural networks (CNNs) differ from recurrent neural networks (RNNs)? (Compare their architectures and typical applications—e.g., image processing vs sequential data.)  
  **A:** CNNs exploit locality and weight sharing via convolutions, which makes them excel on grid‑like data such as images and audio spectrograms. RNNs process sequences step by step with shared state, so they naturally model temporal dependencies in text or speech. In practice, Transformers have replaced many RNN uses for long sequences, while CNNs remain strong for vision and local pattern extraction.

- **Dropout**  
  **Q:** What is dropout in neural networks and why is it used during training? (Explain how it works and its effect on model performance.)  
  **A:** Randomly zero activations with probability p during training; scale at inference. Acts as regularizer via noise and approximate model averaging; reduces co‑adaptation. Effective in MLP/CNNs; less used with LayerNorm+Residuals and large datasets. Interactions: keep p modest with BatchNorm; consider stochastic depth/DropPath in very deep nets.

- **Batch Normalization**  
  **Q:** Why is batch normalization used in deep neural network training? (Discuss what problem it solves and how it improves training stability.)  
  **A:** Normalizes layer activations using batch mean/var, then learned scale/shift. Improves conditioning and gradient flow, enables higher learning rates, and adds regularization. Train uses batch stats; inference uses moving averages. Pitfalls: very small batches, sequence models—prefer GroupNorm/LayerNorm there.
- **Transfer Learning**  
  **Q:** What is transfer learning, and when would you apply it? (Give an example of using pre-trained models or feature reuse to accelerate training on a new task.)  
  **A:** Reuse a model pre-trained on a source corpus (e.g., ImageNet, self-supervised LMs) for a target task. Low data: freeze backbone and train a small head. More data/shift: fine-tune some/all layers with discriminative lrs and gradual unfreezing. Benefits: faster convergence, better sample efficiency; watch domain shift and forgetting.
- **Attention & Transformers**  
  **Q:** What is an attention mechanism in the context of neural networks, and how did the Transformer architecture leverage it? (Outline why Transformers were a breakthrough for sequence modeling compared to older RNN-based models.)  
  **A:** Attention scores relevance between tokens (queries–keys–values); self-attention builds contextual representations with O(n²) interactions. Transformers stack multi-head attention + MLPs with residuals and normalization, replacing recurrence—enabling parallelism, long-range dependencies, and superior scaling.

- **Initialization & Residuals**  
  **Q:** Xavier/He initialization, residual connections, and deep signal propagation; how they mitigate vanishing/exploding gradients.  
  **A:** Xavier/Glorot preserves variance for tanh; He init for ReLU‑like activations (fan‑in/out scaling). Residual connections provide identity paths for gradient flow, allowing very deep nets. Combine with normalization (e.g., pre‑LayerNorm) and careful gain to avoid vanishing/exploding.

- **Weight Decay vs L2 (AdamW)**  
  **Q:** Why decoupled weight decay differs from L2 regularization in adaptive optimizers; when it matters.  
  **A:** In SGD, L2 penalty ≡ weight decay. In Adam, coupling the L2 term to the gradient causes scale‑dependent updates; AdamW decouples decay from the gradient, improving optimization and generalization. Do not decay biases/normalization gains.

- **Normalization Choices**  
  **Q:** BatchNorm vs LayerNorm vs GroupNorm vs WeightNorm—trade-offs and where to use each.  
  **A:** BatchNorm: best for CNNs with decent batch sizes; fastest, but batch‑dependent. LayerNorm: per‑token normalization—works for Transformers/RNNs and tiny batches. GroupNorm: per‑sample channel groups—robust for small‑batch CNNs. WeightNorm: reparameterization—rarely used now.

- **Learning Rate Scheduling**  
  **Q:** Compare step decay, cosine annealing, and warmup; when each is most effective.  
  **A:** Step decay: simple milestones for plateauing loss. Cosine annealing: smooth decay; strong late‑phase convergence; often with restarts. Warmup: ramp lr to avoid early instability (Adam/large batch/BN). One‑cycle or cosine+warmup are strong defaults.

- **Gradient Clipping**  
  **Q:** What is gradient clipping and when should you use it in neural network training? (Explain the difference between norm clipping and value clipping, and discuss how gradient clipping interacts with different optimizers like SGD versus Adam.)  
  **A:** Gradient clipping is a technique to prevent exploding gradients by bounding the magnitude of gradients during backpropagation. It's essential when gradients can grow uncontrollably large, which destabilizes training and leads to parameter updates that are too aggressive.

  **When it helps:** Gradient clipping is crucial for RNNs and Transformers where gradients can explode due to repeated matrix multiplications through time steps or deep attention layers. It's also important when using large learning rates, training very deep networks, or working with sequences of varying lengths where gradient magnitudes can vary dramatically.

  **Norm vs Value Clipping:** Norm clipping (preferred) rescales the entire gradient vector when its L2 norm exceeds a threshold τ: if ||g|| > τ, then g ← g * (τ/||g||). This preserves the gradient direction while controlling magnitude. Value clipping (cruder) independently clips each gradient component to [-τ, τ], which can distort the gradient direction and harm convergence.

  **Optimizer Interactions:** With SGD, clipped gradients directly scale the parameter updates. With adaptive optimizers like Adam, clipping interacts with the momentum and scaling terms—the clipped gradients feed into Adam's running averages, so you typically need smaller τ values (e.g., 1.0-5.0) compared to SGD (10.0-100.0). Set τ based on model scale: larger models often need larger clipping thresholds. Monitor gradient norms during training to tune τ appropriately—too small hurts convergence, too large allows instability.

- **Numerical Stability (Softmax / Log-Sum-Exp)**  
  **Q:** Why and how do you implement numerically stable softmax cross-entropy (e.g., log-sum-exp trick, max-shift)? What failures can occur in production if you do not?  
  **A:** **The Problem:** Naive softmax computation σ(x)ᵢ = exp(xᵢ)/∑ⱼexp(xⱼ) suffers from numerical instability. Large positive logits cause exp(xᵢ) to overflow to infinity, while large negative logits make exp(xᵢ) underflow to zero, leading to 0/0 or ∞/∞ forms. Even moderate logits like x = [1000, 999] cause overflow in float32.

  **Log-Sum-Exp Trick:** For stable computation of log(∑ᵢexp(xᵢ)), use the identity:
  log(∑ᵢexp(xᵢ)) = c + log(∑ᵢexp(xᵢ - c))
  where c = max(xᵢ) is the max-shift. This ensures the largest exponent is exp(0) = 1, preventing overflow, while other terms are ≤ 1, preventing underflow.

  **Stable Cross-Entropy:** For cross-entropy loss L = -log(σ(x)ᵧ) = -xᵧ + log(∑ⱼexp(xⱼ)), apply LSE:
  L = -xᵧ + c + log(∑ⱼexp(xⱼ - c)) where c = max(x)
  
  **Implementation:**
  ```python
  def stable_softmax_cross_entropy(logits, target_idx):
      c = np.max(logits)  # max-shift
      shifted = logits - c
      log_sum_exp = c + np.log(np.sum(np.exp(shifted)))
      return -logits[target_idx] + log_sum_exp
  ```

  **Production Failures Without Stability:**
  - **NaN gradients** from 0/0 forms break optimization entirely
  - **Infinite losses** corrupt loss averaging and metrics
  - **Model divergence** from exploding gradients in backprop
  - **Silent failures** where model stops learning due to saturated probabilities
  - **Inconsistent behavior** across different hardware/precision settings
  
  Modern frameworks like PyTorch/TensorFlow implement this automatically in functions like `F.cross_entropy()`, but custom implementations need explicit numerical care.

- **Activation Functions**  
  **Q:** Compare ReLU, GELU, and SiLU/Swish. When do you prefer each, and how do you handle issues like "dead" neurons or saturation?  
  **A:**
  - **ReLU (max(0, x)) — piecewise-linear, zero for x<0, slope 1 for x>0.**
    - **Shape/properties:** Non-saturating on the positive side, sparse activations, cheap. Risks "dead ReLUs" (units stuck at 0) when pre-activations shift negative.
    - **Use:** Strong default in CNN/MLP. Pair with He/Kaiming init and normalization. If many dead units appear, prefer a leaky variant.
  - **GELU (x·Φ(x)) — smooth, slightly non-monotonic around 0.**
    - **Shape/properties:** Soft probabilistic gating; keeps small negatives with small magnitude; smoother gradients than ReLU.
    - **Use:** Common in Transformers/LLMs (e.g., BERT/GPT-era). Good with LayerNorm+Residuals; robust at scale.
  - **SiLU/Swish (x·σ(x)) — smooth, non-monotonic; like a softened ReLU.**
    - **Shape/properties:** Retains small negative values; smoother than ReLU, similar to GELU; slightly costlier than ReLU.
    - **Use:** Strong in modern CNNs (e.g., EfficientNet) and smaller models; also used in detection backbones (e.g., SiLU in YOLO variants).

  **Handling dead neurons / saturation:**
  - Use **LeakyReLU/PReLU (max(αx, x))** to keep a gradient on x<0; PReLU learns α.
  - Prefer **He/Kaiming initialization**, appropriate learning rates, and **normalization** to stabilize pre-activation distributions.
  - Avoid heavily **saturating activations** (sigmoid/tanh) in deep hidden layers unless architecturally required (e.g., RNN gates).

  **Quick reference (other common activations):**
  - **LeakyReLU/PReLU:** Piecewise linear with negative slope; fixes dead ReLUs; great general-purpose swap for ReLU.
  - **ELU/SELU:** Smooth with negative saturation; zero-centered outputs; SELU requires specific init/dropout for self-normalizing nets.
  - **Tanh:** Bounded [-1,1], zero-centered but saturates; used inside recurrent units; avoid for deep feedforward hidden layers.
  - **Sigmoid:** Bounded [0,1], saturating; use for binary outputs or gates, not hidden layers.
  - **Softplus (log(1+e^x)):** Smooth ReLU; use when you need strictly positive outputs with smooth gradients.
  - **Linear (identity):** Use for regression heads or residual/skip connections.

- **Universal Approximator Caveats**  
  **Q:** Why the universal approximation theorem does not imply practical learnability; discuss sample complexity, optimization landscape, and inductive biases.  
  **A:** The theorem only says a sufficiently wide network can represent the function; it says nothing about how much data you need, whether gradient descent will find the right parameters, or whether the model’s inductive bias matches the task. In practice, sample complexity, optimization dynamics, and architecture priors determine learnability.

- **GANs**
  **Q:** What are Generative Adversarial Networks (GANs)? How do they work, and what are some common applications?

  **A:** GANs are a framework where two neural networks compete in a minimax game to learn data distributions.

  **Follow-up: Can you explain the minimax game in more detail?**
  The generator G tries to map random noise z to realistic data G(z), while the discriminator D tries to distinguish between real data and generated samples. The objective is:
  ```
  min_G max_D V(D,G) = E[log D(x)] + E[log(1 - D(G(z)))]
  ```
  The generator wants to minimize this (fool the discriminator), while the discriminator wants to maximize it (correctly classify real vs fake).

  **Follow-up: What are the main training challenges?**
  - **Mode collapse**: Generator produces limited variety, ignoring parts of the data distribution
  - **Training instability**: Oscillations, non-convergence due to the adversarial dynamics
  - **Vanishing gradients**: When discriminator becomes too good, generator gets poor learning signal

  **Follow-up: How do we address these issues?**
  - **Loss variations**: Non-saturating loss (maximize log D(G(z))), WGAN with gradient penalty, hinge loss
  - **Regularization**: Spectral normization, gradient penalty, consistency regularization
  - **Training tricks**: Two time-scale update rule (TTUR), different learning rates for G and D
  - **Architecture choices**: Progressive growing, self-attention, residual connections

  **Follow-up: What are the key applications?**
  - **Image synthesis**: Generating photorealistic faces, objects, scenes
  - **Super-resolution (SR)**: Upscaling low-resolution images with realistic details
  - **Style transfer**: Converting images between artistic styles or domains
  - **Domain translation**: Converting between different visual domains (e.g., horse↔zebra, sketches↔photos)

- **Optimizers**
  **Q:** Compare SGD, Adam, and RMSprop. When would you use each, and what are the trade-offs?

  **A:** Each optimizer has distinct characteristics that make them suitable for different scenarios.

  **Follow-up: Can you break down each optimizer's mechanics?**
  - **SGD + Momentum**: Uses exponentially weighted moving average of gradients: `v = βv + ∇L`, `θ = θ - αv`. Simple, reliable, strong generalization properties.
  - **RMSprop**: Adapts learning rate per parameter using gradient magnitude: `v = βv + (1-β)∇²`, `θ = θ - α∇/√(v+ε)`. Good for non-stationary objectives.
  - **Adam/AdamW**: Combines momentum + adaptive learning rates: maintains both first and second moment estimates. AdamW decouples weight decay from gradient updates.

  **Follow-up: What are the key trade-offs?**
  - **SGD + Momentum**: Better generalization, fewer hyperparameters, but slower convergence and requires careful learning rate tuning
  - **RMSprop**: Good for RNNs and non-stationary problems, but can be unstable with large learning rates
  - **Adam/AdamW**: Fast convergence, robust to hyperparameter choices, but may overfit more and sometimes finds worse local minima

  **Follow-up: When should you use each optimizer?**
  - **SGD + Momentum**: Computer vision, ResNets, when you have time to tune hyperparameters and want best final performance
  - **RMSprop**: Historically popular for RNNs, though largely replaced by Adam variants
  - **AdamW**: NLP, Transformers, when you need fast prototyping or don't have time for extensive hyperparameter tuning

  **Follow-up: Any practical heuristics for choosing?**
  Start with AdamW as default. Switch to SGD + momentum if: (1) you need better generalization, (2) working with CNNs/computer vision, (3) AdamW isn't converging well. Monitor both training loss and validation metrics when deciding.
  
---

## Mathematical Foundations

- **Linear Algebra Basics**  
  **Q:** What are eigenvalues and eigenvectors? Provide intuition and common uses (e.g., PCA, clustering).  
  **A:** For a square matrix A, v ≠ 0 is an eigenvector and λ an eigenvalue if Av = λv. Intuition: directions v that A only scales (no rotation). Uses: PCA (eigenvectors of covariance give principal components), spectral clustering/graph Laplacian, power iteration, stability (sign of eigenvalues), diagonalization and fast exponentials.

  **Follow-up: How exactly are eigenvectors/eigenvalues used in PCA?**  
  PCA finds directions of maximum variance in data. Given centered data X (n×d), compute covariance matrix C = X^T X / (n-1). The eigenvectors of C are the principal components—directions of maximum variance. Eigenvalues λᵢ give the variance explained along each component. Sort by decreasing eigenvalue: PC1 explains most variance, PC2 explains most remaining variance orthogonal to PC1, etc. 
  
  **Dimensionality reduction:** Project data onto top-k eigenvectors: X_reduced = X @ V_k where V_k contains the k largest eigenvectors as columns. This preserves maximum variance in k dimensions.
  
  **Why eigendecomposition works:** The covariance matrix C is symmetric positive semi-definite, so it has real eigenvalues and orthogonal eigenvectors. The eigenvector with largest eigenvalue points in the direction of maximum variance, which is exactly what we want for the first principal component.

  **Follow-up: How do eigenvalues/eigenvectors enable spectral clustering?**  
  Spectral clustering uses the graph Laplacian matrix L of similarity graphs. Two common forms:
  - **Unnormalized Laplacian:** L = D - W (degree matrix D minus adjacency matrix W)  
  - **Normalized Laplacian:** L_norm = I - D^(-1/2) W D^(-1/2)
  
  **Key insight:** The eigenvectors of L encode cluster structure. For k clusters, compute the k smallest eigenvalues and their eigenvectors of L. The eigenvector corresponding to the second smallest eigenvalue (Fiedler vector) provides the most informative partition.
  
  **Algorithm:** 1) Build similarity graph/adjacency matrix W, 2) Compute Laplacian L, 3) Find k smallest eigenvectors, 4) Form feature matrix from eigenvectors, 5) Apply k-means to the rows of this matrix.
  
  **Why it works:** Graph cuts correspond to eigenvalues—small eigenvalues indicate good cluster separations. The eigenvectors provide a low-dimensional embedding where traditional clustering (k-means) can work effectively on the spectral coordinates.

- **Bayes' Theorem**  
  **Q:** State Bayes' theorem and explain its use in ML (e.g., calibration, Naive Bayes, regularization-as-prior).  
  **A:** Bayes: p(θ|D) = p(D|θ)p(θ)/p(D). In ML: Naive Bayes uses class‑conditional likelihoods with independence assumptions; calibration and updating beliefs with priors; regularization‑as‑prior (e.g., L2 ≈ Gaussian prior, L1 ≈ Laplace prior) gives MAP estimation. p(D) is a normalizer (evidence), often intractable—approximated by VI/MCMC.

- **Convexity**  
  **Q:** What is a convex function? Give examples and why convexity matters for optimization guarantees.  
  **A:** f is convex if f(λx+(1−λ)y) ≤ λf(x)+(1−λ)f(y) for λ∈[0,1]. Convex problems (convex f, convex set) have no spurious local minima; any local optimum is global and can be solved efficiently. Examples: least squares, logistic regression with L2, SVM hinge loss with convex regs. Non‑convex: deep nets. Implications: initialization less critical vs. non‑convex where restarts/heuristics help.

- **Jacobian**  
  **Q:** What is a Jacobian? Where does it appear in ML (backpropagation, change of variables, sensitivity analysis)?  
  **A:** Jacobian J_ij = ∂f_i/∂x_j collects partial derivatives of vector‑valued f(x). In backprop, chain rule composes Jacobians to get gradients. In flows/change‑of‑variables, log p_x(x) = log p_z(z) + log|det ∂z/∂x|. Also used for sensitivity/ saliency and linearization.

- **Matrix Multiplication**  
  **Q:** How do you compute matrix multiplication and reason about shapes/complexity? For a lower-triangular L, how do you compute Lx efficiently (forward substitution)?  
  **A:** (AB)_ij = ∑_k A_ik B_kj with shapes (m×k)(k×n)→(m×n); naive cost O(mkn). Forward substitution for lower‑triangular L solves Ly=b by iterating i=1..n: x_i = (b_i − ∑_{j<i} L_{ij}x_j)/L_{ii}. Use vectorization and BLAS for speed.

---

## Reinforcement Learning

- **Intuition**  
  **Q:** What is reinforcement learning, and how does it differ from supervised and unsupervised learning? Give examples of applications (e.g., AlphaGo).  
  **A:** RL optimizes sequential decisions via interaction: the agent chooses actions, observes state transitions and rewards, and maximizes expected return. Unlike supervised learning (labeled targets per example), RL has delayed/partial feedback and exploration needs; unlike unsupervised, the objective is task reward, not density/structure. Apps: games (AlphaGo/Atari), robotics, recommender bandits, ads.

- **MDPs**  
  **Q:** What is a Markov Decision Process (MDP)? What are the key components and the Markov property?  
  **A:** MDP = (S, A, P, R, γ) where S=states, A=actions, P=transition probabilities P(s'|s,a), R=reward function R(s,a,s'), γ=discount factor. **Markov property**: future depends only on current state, not history: P(s_{t+1}|s_t,a_t,s_{t-1},...) = P(s_{t+1}|s_t,a_t). This memoryless assumption enables dynamic programming solutions.

- **Value Functions**  
  **Q:** Explain state-value V^π(s) and action-value Q^π(s,a) functions. How do they relate?  
  **A:** **V^π(s)** = expected return starting from state s following policy π. **Q^π(s,a)** = expected return starting from state s, taking action a, then following π. Relationship: V^π(s) = ∑_a π(a|s)Q^π(s,a) and Q^π(s,a) = ∑_{s'} P(s'|s,a)[R(s,a,s') + γV^π(s')]. Q-function captures action-specific value, V-function averages over policy.

- **Bellman Equations**  
  **Q:** State the Bellman equations for V^π and Q^π. Why are they fundamental to RL?  
  **A:** **Bellman for V^π**: V^π(s) = ∑_a π(a|s) ∑_{s'} P(s'|s,a)[R(s,a,s') + γV^π(s')]. **Bellman for Q^π**: Q^π(s,a) = ∑_{s'} P(s'|s,a)[R(s,a,s') + γ ∑_{a'} π(a'|s')Q^π(s',a')]. These recursive equations express value in terms of immediate reward + discounted future value. They enable dynamic programming (value iteration, policy iteration) and form the basis for temporal difference learning.

- **Exploration vs Exploitation**  
  **Q:** What is the exploration-exploitation trade-off in RL? Give examples of exploration strategies.  
  **A:** **Exploitation**: choose actions that maximize current knowledge (highest Q-values). **Exploration**: try new actions to gain information and potentially discover better strategies. Trade-off: too much exploitation gets stuck in local optima; too much exploration wastes time on suboptimal actions. Strategies: ε-greedy (random action with probability ε), UCB (upper confidence bounds), Thompson sampling (Bayesian), curiosity-driven exploration, count-based bonuses.

- **On-Policy vs Off-Policy**  
  **Q:** What's the difference between on-policy and off-policy learning? Give algorithm examples and trade-offs.  
  **A:** **On-policy**: learns about the policy being followed (e.g., SARSA, Policy Gradient). Sample efficiency limited to current policy's experience. **Off-policy**: learns about target policy while following different behavior policy (e.g., Q-learning, DQN). Can reuse experience from any policy, better sample efficiency, but requires importance sampling corrections and can be less stable. Q-learning is off-policy because it learns Q* regardless of behavior policy.

- **Temporal Difference (TD) Learning**  
  **Q:** What is TD learning and how does it differ from Monte Carlo methods? Explain the TD error.  
  **A:** **TD learning**: updates value estimates using bootstrapping—estimates based on other estimates rather than complete returns. **TD error**: δ = R + γV(s') - V(s), measures prediction error. **vs Monte Carlo**: MC waits for episode end and uses actual returns (unbiased, high variance); TD updates immediately using estimated future value (biased initially, lower variance, learns online). TD(λ) interpolates between TD(0) and MC via eligibility traces.

- **Policy-Based vs Value-Based Methods**  
  **Q:** Compare policy-based and value-based approaches in RL. When would you prefer each?  
  **A:** **Value-based**: learn V or Q functions, derive policy greedily (e.g., Q-learning, DQN). Works well for discrete actions, sample efficient, but struggles with continuous action spaces and stochastic policies. **Policy-based**: directly optimize parameterized policy π_θ (e.g., REINFORCE, PPO). Handles continuous actions naturally, can learn stochastic policies, but higher variance and sample inefficiency. **Actor-Critic**: combines both—actor (policy) and critic (value function)—gets benefits of both approaches.

- **Credit Assignment Problem**  
  **Q:** What is the credit assignment problem in RL? How do different methods address it?  
  **A:** Problem: when reward comes after many actions, which actions deserve credit? Delayed rewards make it hard to identify which decisions were good/bad. Solutions: **Discounting** (γ < 1) gives more credit to recent actions; **Eligibility traces** (TD(λ)) distribute credit over recent state-action pairs; **Advantage estimation** (A = Q - V) reduces variance in credit assignment; **Return attribution** methods like GAE provide better baselines.

---

## Model Training & Evaluation

- **Evaluation Metrics**  
  **Q:** How would you evaluate a classification model's performance? (Discuss common metrics like accuracy, precision, recall, F1-score, and ROC-AUC, and when each is most appropriate.)  
  **A:** Accuracy for balanced classes; precision/recall/F1 for imbalance and asymmetric costs; PR curves/AUCPR when positives are rare; ROC/AUROC for ranking across thresholds but can be misleading under imbalance; calibrated metrics (Brier, NLL) when probabilities matter. Always report by key segments to avoid Simpson’s paradox.

- **Train/Validation/Test Split**  
  **Q:** What roles do the training set, validation set, and test set play in model development? (Why is each needed and how are they used?)  
  **A:** Train fits parameters. Validation tunes hyperparameters/early stopping and selects models. Test is a holdout used once at the end to estimate generalization. Keep leakage out: fit preprocessors on train only; use time‑based or group splits when i.i.d. fails.

- **Cross-Validation**  
  **Q:** What is cross-validation and why is it important in the model training process? (Describe how it helps in model selection and avoiding overfitting.)  
  **A:** K‑fold (often stratified) provides low‑variance performance estimates and robust model selection; use GroupKFold for clustered data and rolling‑origin CV for time series. Prevents overfitting to a single split and enables uncertainty estimates via fold dispersion.

- **Imbalanced Data**  
  **Q:** How do you handle imbalanced datasets in a classification problem? (Mention techniques like resampling, class weights, or appropriate evaluation metrics.)  
  **A:** Use class‑weighted loss, focal loss, or cost‑sensitive thresholds; resample (downsample majority, upsample minority, SMOTE/augment); choose AUCPR/recall@k; calibrate probabilities; monitor per‑segment metrics; consider anomaly‑style formulations.

- **Metric Selection Under Imbalance**  
  **Q:** When to prefer AUCPR over AUROC; how to report metrics across key segments to avoid masking failures.  
  **A:** Prefer AUCPR when positives are rare—precision reflects base‑rate changes; AUROC can look high even for weak minority recall. Always slice by cohorts (e.g., geography, device, time) and report recall/precision@business thresholds to surface harms.

- **Threshold Selection & Costs**  
  **Q:** Choose a classification threshold under asymmetric costs; derive the decision-theoretic threshold with calibrated probabilities.  
  **A:** With calibrated p = P(y=1|x) and costs C_FP, C_FN, predict 1 if p ≥ t*, where t* = C_FP/(C_FP + C_FN) under equal priors; incorporate priors π via t* = (C_FP (1−π)) / (C_FP (1−π) + C_FN π). Validate against utility curves; adjust per‑segment.

- **Time-Series / Non-iid Splits**  
  **Q:** Design proper splits for temporal or clustered data (rolling-origin cross-validation); detect and prevent leakage.  
  **A:** Use rolling‑origin CV (train up to t, validate on (t,t+Δ]) to respect causality; use GroupKFold for entities to avoid identity leakage. Fit scalers/encoders on train windows only; avoid peeking via future features or label‑derived signals.

- **Offline-to-Online Confidence**  
  **Q:** How do you ensure offline metrics predict online impact? Discuss Simpson's paradox and segment analysis.  
  **A:** Build segment‑level scorecards and check consistency of lifts across key cohorts; simulate policy using replay/bandit evaluation when applicable; reconcile proxy→business metric mappings; run shadow eval/canaries; beware Simpson’s paradox by weighting per‑segment appropriately.

- **Probability Calibration**  
  **Q:** What is probability calibration and how do you measure it (e.g., ECE, Brier score)? When should you apply temperature scaling vs isotonic regression, and how do you validate calibration shifts in production?  
  **A:** Measure with reliability diagrams, ECE, Brier/NLL. Temperature scaling is simple/monotone, good with enough data and mild miscalibration; isotonic is non‑parametric, better with complex miscalibration but risks overfit. Validate on a held‑out set and monitor drifted segments; recalibrate post‑deployment if shift occurs.

- **Data Leakage Basics**  
  **Q:** Provide examples of subtle leakage (e.g., target leakage in features, split leakage in preprocessing) and concrete defenses (proper pipeline splits, fit transforms on train only, time-based validation).  
  **A:** Leakage examples: target‑derived features, future info in time series, fitting scalers/imputers on full data, entity overlap across splits. Defenses: pipeline with fit on train only, time‑based CV, group splits, strict schema/versioning, and shadow checks vs live data.

  **Follow-up: What are concrete examples of each type of leakage?**
  
  **Target Leakage (Direct):**
  - **Credit scoring**: Using "account_closed_date" when predicting default (only available after default)
  - **Medical diagnosis**: Including "treatment_prescribed" when predicting disease (treatment decision depends on diagnosis)
  - **E-commerce**: Using "return_processed_date" when predicting purchase satisfaction
  
  **Target Leakage (Derived):**
  - **Fraud detection**: Using "transaction_reviewed_by_human" (only flagged transactions get reviewed)  
  - **Churn prediction**: Using "customer_service_calls_last_month" (unhappy customers call more)
  - **Ad targeting**: Using "clicked_similar_ad_yesterday" (behavioral feature that's outcome-dependent)
  
  **Temporal Leakage:**
  - **Stock prediction**: Using next day's opening price to predict today's closing movement
  - **Medical**: Using lab results from after diagnosis to predict diagnosis
  - **Marketing**: Using campaign performance from week 2 to predict week 1 conversions
  
  **Split Leakage (Preprocessing):**
  - **Scaling**: Computing mean/std on full dataset before train/test split
  - **Feature selection**: Selecting features based on correlation with target using full dataset
  - **Imputation**: Filling missing values using statistics from test set
  - **Encoding**: Fitting categorical encoders on validation/test data

  **Follow-up: What are systematic defenses against each type?**
  
  **Pipeline Discipline:**
  ```python
  # Wrong - leakage prone
  X_scaled = StandardScaler().fit_transform(X)  # Uses full dataset
  X_train, X_test = train_test_split(X_scaled)
  
  # Right - leak-free
  X_train, X_test = train_test_split(X)
  scaler = StandardScaler().fit(X_train)  # Fit only on train
  X_train_scaled = scaler.transform(X_train)
  X_test_scaled = scaler.transform(X_test)
  ```
  
  **Temporal Validation:**
  - **Rolling-origin CV**: Train on [1, t], validate on [t+1, t+k], test on [t+k+1, t+k+h]
  - **No future features**: Ensure all features use only information available at prediction time
  - **Feature lag**: Add explicit delays (e.g., use last week's engagement to predict this week's churn)
  
  **Group-Based Splits:**
  - **User-level**: Split by user_id to prevent user overlap between train/test
  - **Entity-level**: For time series, split by company/product rather than individual records
  - **Geographic**: Split by region when location-based features are strong
  
  **Schema & Versioning:**
  - **Feature documentation**: Track when each feature becomes available in production
  - **Temporal metadata**: Include "feature_computed_at" timestamps in datasets
  - **Causality graphs**: Map which features can causally influence others
  
  **Production Validation:**
  - **Shadow mode**: Run model on live data, compare feature values with training distribution
  - **Feature drift monitoring**: Alert when feature distributions shift unexpectedly
  - **Prediction intervals**: Compare offline holdout performance with online shadow results

  **Follow-up: How do you detect leakage systematically?**
  
  **Statistical Tests:**
  - **Unrealistic performance**: AUC > 0.95 on complex problems often indicates leakage
  - **Feature importance**: If single feature dominates (>50% importance), investigate carefully
  - **Perfect separators**: Features that perfectly predict target in subsets of data
  
  **Temporal Validation:**
  - **Forward-chaining CV**: Performance should degrade gracefully as prediction horizon increases
  - **Time-based feature analysis**: Features shouldn't have predictive power in "impossible" time windows
  
  **Business Logic Checks:**
  - **Causal review**: Every feature should have plausible causal story for predicting target
  - **Production feasibility**: Verify all features will be available at inference time
  - **Domain expert review**: Subject matter experts can spot features that "shouldn't work"

  **Q:** How do you design and interpret A/B tests for ML model updates? Choose primary/guardrail metrics, handle sequential testing, and ensure statistical validity.  
  **A:** Define hypothesis and primary metric; add guardrails (latency, error, fairness). Power analysis for sample size; randomize at correct unit; pre‑register analysis. Use sequential methods (alpha spending/SPR T) or fixed horizons; handle peeking; use CUPED/stratification to reduce variance; check heterogeneous effects.

- **Uncertainty Estimation**  
  **Q:** How do you quantify and use predictive uncertainty (e.g., ensembles, MC dropout, Bayesian last-layer)? How does uncertainty inform thresholding and decision costs?  
  **A:** Deep ensembles and SWAG for strong baselines; MC dropout/test‑time augmentation for cheap uncertainty; Bayesian last‑layer or Laplace for calibrated logits. Use uncertainty to defer, request human review, or raise thresholds under high cost; report risk‑adjusted metrics.

  **Follow-up: Compare the main uncertainty quantification methods and their trade-offs.**
  
  **Deep Ensembles:**
  - **Method**: Train multiple models with different random initializations, average predictions, use variance as uncertainty
  - **Pros**: Strong empirical performance, captures model uncertainty well, straightforward to implement
  - **Cons**: Expensive (5-10x compute), storage overhead, doesn't capture parameter uncertainty within models
  - **Best for**: High-stakes applications where accuracy and reliability matter more than efficiency
  
  **Monte Carlo (MC) Dropout:**
  - **Method**: Keep dropout active during inference, sample multiple forward passes, use prediction variance
  - **Pros**: Nearly free at inference (single model), easy retrofit to existing models
  - **Cons**: May underestimate uncertainty, doesn't capture systematic model biases, requires tuning dropout rates
  - **Best for**: Large-scale deployment where computational cost is critical
  
  **Stochastic Weight Averaging Gaussian (SWAG):**
  - **Method**: Track running statistics of SGD trajectory, approximate posterior as Gaussian, sample weights
  - **Pros**: Better calibration than single models, modest computational overhead, captures loss landscape
  - **Cons**: Gaussian assumption may be limiting, requires modified training procedure
  - **Best for**: Balance between ensemble quality and single-model efficiency
  
  **Bayesian Last Layer/Laplace Approximation:**
  - **Method**: Treat only output layer as Bayesian, approximate posterior Hessian around MAP estimate
  - **Pros**: Principled uncertainty, good calibration, computationally tractable for large networks
  - **Cons**: Limited to last-layer uncertainty, Gaussian posterior assumption, requires Hessian computation
  - **Best for**: Large pre-trained models where full Bayesian treatment is impractical

  **Follow-up: How does uncertainty inform decision-making and thresholding?**
  
  **Risk-Adjusted Thresholding:**
  ```python
  # Traditional: fixed threshold
  prediction = model.predict(x)
  decision = prediction > 0.5
  
  # Uncertainty-aware: adaptive threshold
  mean, std = model.predict_with_uncertainty(x)
  confidence_interval = [mean - 2*std, mean + 2*std]
  
  if confidence_interval[0] > threshold:
      decision = "high_confidence_positive" 
  elif confidence_interval[1] < threshold:
      decision = "high_confidence_negative"
  else:
      decision = "defer_to_human"  # High uncertainty
  ```
  
  **Cost-Sensitive Decision Framework:**
  - **High-certainty predictions**: Use automated decision with standard threshold
  - **Medium uncertainty**: Raise threshold (require stronger signal) or request additional features
  - **High uncertainty**: Defer to human expert, collect more data, or decline to make prediction
  - **Cost matrix**: C_automated << C_human << C_wrong_decision
  
  **Practical Applications:**
  - **Medical diagnosis**: Defer uncertain cases to specialists, avoiding dangerous misdiagnoses
  - **Financial lending**: Request additional documentation for uncertain applications
  - **Content moderation**: Human review for borderline cases, automated only for clear violations
  - **Autonomous driving**: Reduce speed or request human takeover in uncertain situations
  
  **Production Integration:**
  - **Confidence-based routing**: Route uncertain predictions to more expensive but accurate models
  - **Active learning**: Prioritize uncertain examples for labeling to improve model
  - **A/B testing**: Segment users by prediction confidence to measure uncertainty quality
  - **Monitoring**: Track correlation between uncertainty and actual errors to validate calibration

  **Follow-up: How do you validate and calibrate uncertainty estimates?**
  
  **Calibration Assessment:**
  - **Reliability diagrams**: Plot predicted confidence vs actual accuracy in bins
  - **Expected Calibration Error (ECE)**: ∑|confidence - accuracy| weighted by bin frequency  
  - **Brier score decomposition**: Reliability + resolution + uncertainty components
  
  **Uncertainty-Error Correlation:**
  ```python
  # Good uncertainty: high uncertainty → high error rate
  uncertainty_scores = model.predict_uncertainty(X_test)
  errors = (predictions != y_true).astype(int)
  correlation = np.corrcoef(uncertainty_scores, errors)[0,1]
  # Target: correlation > 0.3 for useful uncertainty
  ```
  
  **Post-hoc Calibration:**
  - **Temperature scaling**: Scale logits by learned temperature T before softmax
  - **Platt scaling**: Fit sigmoid to map raw scores to calibrated probabilities  
  - **Isotonic regression**: Non-parametric monotonic calibration mapping
  
  **Deployment Validation:**
  - **Shadow testing**: Compare uncertainty-informed decisions vs traditional thresholding
  - **Human-in-the-loop validation**: Track cases where human review overturns model + uncertainty
  - **Segment analysis**: Validate uncertainty quality across different data subgroups

- **Handling Missing/Noisy Data**  
  **Q:** How do you preprocess and model datasets with missingness or label noise? Discuss imputation strategies, robust training, and checks you’d run.  
  **A:** Diagnose MCAR/MAR/MNAR; add missingness indicators; use median/knn/mice imputation; robustify with Huber/focal/label‑smoothing; apply strong regularization and augmentation; for label noise, use confident learning, co‑teaching, or relabeling; validate with stress tests and segment audits.

---

## Deep Learning

- **Backpropagation**  
  **Q:** Explain how backpropagation works in a neural network. (What is it computing and why is it essential for training deep models?)  
  **A:** Backpropagation applies the chain rule to efficiently compute the gradient of the loss with respect to every parameter by propagating errors from the output layer back to the inputs. Those gradients drive an optimizer (e.g., SGD/AdamW) to update weights in the direction that reduces loss. Without backprop, training deep networks would be computationally infeasible.

- **Vanishing/Exploding Gradients**  
  **Q:** What is the "vanishing gradient" problem in deep neural networks? (Why does it occur, and what techniques or architectures help mitigate it?)  
  **A:** Vanishing or exploding gradients arise from repeatedly multiplying by Jacobians with singular values <1 or >1 through many layers or timesteps. Signals then shrink to near zero or blow up, stalling learning. I mitigate this with careful initialization (He/Xavier), normalization (BatchNorm/LayerNorm), residual connections, appropriate activations (ReLU/GELU), gradient clipping, and well‑tuned learning‑rate schedules. For sequences, gated units or Transformers help.

- **ELBO (Evidence Lower BOund)**  
  **Q:** What is ELBO in variational inference and VAEs? Derive the decomposition, explain the reconstruction vs regularization terms, and discuss why we optimize a lower bound instead of the marginal likelihood directly.  
  **A:** ELBO(x) = E_{q(z|x)}[log p(x|z)] − KL(q(z|x) || p(z)) = E_q[log p(x,z) − log q(z|x)] ≤ log p(x). Reconstruction encourages faithful decoding; KL regularizes capacity by matching posterior to prior. We optimize ELBO because log p(x) has an intractable log‑integral; ELBO is a tractable lower bound.

- **SVM vs Logistic Regression**  
  **Q:** Discuss margin principle, hinge vs log-loss, soft vs hard margins; when SVMs win (small-n, high-dim, robust margins) vs when logistic is preferable (probabilities, calibration).  
  **A:** SVM maximizes margin with hinge loss; soft margin (C) trades slack vs margin; kernel SVMs handle nonlinearity. Strong with small‑n, high‑dim, and outliers limited by margin. Logistic regression optimizes log‑loss; outputs calibrated probabilities and supports regularization/feature effects. Prefer logistic for probability‑aware decisions and large‑scale linear problems; SVM for robust margins and kernels when n is modest.

- **Trees & Ensembles**  
  **Q:** Random Forest vs Gradient Boosting (e.g., XGBoost/LightGBM): bias–variance trade-offs, regularization knobs (depth, learning rate, subsampling), overfitting risks, and when to choose each.  
  **A:** Random Forest: bagged deep trees → low variance via averaging; few knobs; robust baseline. Gradient Boosting: sequential shallow trees reduce bias; strong accuracy but higher overfit risk—regularize with depth, learning rate, subsampling, L1/L2, and early stopping. Choose RF for speed/robustness; GBMs for SOTA tabular with careful tuning.

---

## ML System Design

- **End-to-End Pipeline**  
  **Q:** If you were to design an end-to-end ML pipeline for a new project, what stages would it include? (Walk through steps such as data collection, preprocessing, feature engineering, model training, validation, deployment, and monitoring.)  
  **A:** I would set up an iterative pipeline: (1) scope the problem and metrics; (2) collect and version data with clear schemas; (3) split data properly (time/group aware), then preprocess and build features in a leak‑free pipeline; (4) train with reproducible configs and track experiments; (5) validate via cross‑validation/holdouts with robust metrics and calibration; (6) package the model with its preprocessing and a contract; (7) deploy behind a safe interface with canaries; and (8) monitor quality, drift, latency, and costs with alerting and rollback.

- **Scalable Deployment**  
  **Q:** How would you design a system to deploy a machine learning model at scale for real-time predictions? (Describe your approach to serving the model, ensuring low latency and high throughput, and what trade-offs you'd consider—e.g., model complexity vs latency, caching results, load balancing.)  
  **A:** I would deploy stateless model servers behind a load balancer, with autoscaling based on QPS and latency SLOs. To hit latency targets, I would keep preprocessing lightweight, enable request batching where feasible, and leverage hardware acceleration (GPU/TPU/AVX). I would add caching for hot keys and feature lookups, use circuit breakers and timeouts, and expose health/readiness probes. The key trade‑offs are accuracy vs latency/cost; I often distill or quantize models for serving while retaining a heavier offline model.

- **Extreme-Scale Serving (Billion QPS)**  
  **Q:** Describe a strategy to scale an ML model to handle billions of queries per second. Discuss caching/CDNs, request batching, distillation/approximation, hierarchical retrieval, hardware acceleration, multi-region replication, and cost/latency trade-offs.  
  **A:** Multi‑tier: CDN/edge caching for static results, request batching and async I/O, ANN retrieval + lightweight rerankers, distillation/quantization for small fast models, and canary routing. Use GPUs/TPUs with fused kernels; shard/replicate across regions; autoscale; degrade gracefully. Continuously profile cost/latency and apply TTLs and cache invalidation policies.

- **Recommendation System Design**  
  **Q:** Design a recommendation system for a music streaming app or YouTube. How would you structure candidate generation vs ranking, embeddings/features, exploration (e.g., bandits), cold-start, feedback loops, and scalability?  
  **A:** Two‑stage: candidate generation (ANN over user/item embeddings; recall from co‑visitation, graph walks) then ranking (wide & deep, GBDT, or Transformers) with calibrated probabilities and business constraints. Exploration via contextual bandits/UCB/Thompson. Cold‑start: content features and popularity priors. Combat feedback loops with de‑biasing and counterfactual evaluation.

- **Reproducibility & Versioning**  
  **Q:** What best practices ensure reproducibility in machine learning workflows? (Discuss things like version controlling data and code, tracking experiments/model versions, setting random seeds, etc.)  
  **A:** I pin code, dependencies, and data versions; capture configs and random seeds; and run jobs in containers for stable environments. I track experiments, artifacts, and metrics with a registry so any model can be reproduced from its lineage. For determinism, I control parallelism flags, set library seeds, and document non‑deterministic ops.

- **Large-Scale Training**  
  **Q:** How would you approach training a model on a very large dataset or a very large model? (Consider techniques like distributed training, using GPUs/TPUs, data sharding, and pipeline parallelism. What system design choices help handle big data or models?)  
  **A:** I combine data parallelism for throughput with tensor/model/pipeline parallelism for very large models, using mixed precision to save memory and increase FLOPs. Data are sharded and streamed from an object store with prefetching. I checkpoint frequently, enable elastic recovery, and monitor throughput/efficiency. When bandwidth is a bottleneck, I use gradient compression and overlap compute with communication.

- **Handling Models Exceeding GPU Memory**  
  **Q:** Have you trained models that exceed GPU memory usage? How do you make it work (gradient checkpointing, activation recomputation, model/tensor/pipeline parallelism, ZeRO/offloading, micro-batching, quantization/LoRA)?  
  **A:** Use mixed precision and gradient checkpointing/activation recompute; partition with tensor/model/pipeline parallelism; ZeRO/FSDP with optimizer/state sharding and CPU/NVMe offload; micro‑batching and gradient accumulation; quantization/LoRA for fine‑tuning. Monitor memory fragmentation and overlap compute/communication.

- **Distributed Training Pipeline (TB-scale)**  
  **Q:** System design: Design a distributed pipeline for training a neural network on terabytes of data. Cover data ingestion, distributed storage, sharding, streaming/ETL, training strategy (data/model/pipeline parallel), failure handling, and observability.  
  **A:** Ingest via streaming ETL to object store (S3/GCS/HDFS) with schema/versioning; shard and precompute features; use tf.data/WebDataset/Parquet readers with prefetch. Train with DDP + gradient compression; scale via data/model/pipeline parallel. Add checkpointing, job retry, idempotent steps. Observability: metrics, traces, tensorboard, drift/throughput dashboards.

- **Design Trade-offs**  
  **Q:** When designing an ML system, what trade-offs might you consider? (For example, discuss choices between a simpler model vs a more complex one, offline batch processing vs online real-time processing, or accuracy vs computational cost depending on the application.)  
  **A:** I weigh model accuracy against latency, cost, and maintainability. For example, a distilled model may reduce latency by 3× at a 1–2% accuracy hit that is acceptable for the product. I also choose between batch vs real‑time features based on freshness needs, and I prefer simpler components when they meet requirements because they are cheaper to operate and debug.

- **Shadow Deployments**  
  **Q:** Design shadow evaluation systems to validate model performance before full rollout.  
  **A:** I mirror live traffic to a shadow service that runs the new model in parallel without affecting user responses. I log predictions, features, and latencies, then compare against the current model on key metrics and guardrails. After stability and quality look good, I progress to canaries and gradual rollout.

- **Training–Serving Skew**  
  **Q:** Top causes (feature drift, preprocessing mismatches), detection (shadow eval, parity checks), and defenses (feature store, schema validation).  
  **A:** The most common causes are time/segment drift, differences between offline and online preprocessing, and stale or missing features. I detect skew with shadow eval, feature/value parity checks, and drift monitors. I prevent it by packaging preprocessing with the model, using a feature store with schemas and freshness SLAs, and validating payloads at serving time.

- **Monitoring & Drift**  
  **Q:** How would you design monitoring for covariate/prior/concept drift and model regressions? What alerts, shadow eval, and rollback criteria would you use?  
  **A:** Track input/latent distributions (PSI/KS), class priors, calibration (ECE), and performance by segment; alert on threshold breaches and trend breaks. Shadow eval against a control, canaries, and automatic rollback on guardrail violations. Maintain feature/schema validation and data freshness SLOs.

---

## Fairness & Interpretability

- **Fairness Metrics & Trade-offs**  
  **Q:** Define EO/DP/EOD; when to use each, and how to navigate trade-offs with calibration and accuracy in thresholding decisions.  
  **A:** Demographic Parity (DP): equal positive rates; Equalized Odds (EO): equal TPR and FPR; Equal Opportunity (EOD): equal TPR. Choose based on context/legal constraints; may conflict with calibration. Use per‑group thresholds or post‑processing to balance utility and fairness; report calibrated performance by group.

- **Interpretability Toolbox**  
  **Q:** SHAP, LIME, permutation importance, and integrated gradients—when to use them and stability concerns with correlated features.  
  **A:** Permutation importance: global, model‑agnostic but unstable with correlated features. SHAP: local+global additive attributions with consistency but costly; handle correlation via conditional SHAP. LIME: local linear approximations—quick but fragile. Integrated Gradients: path‑integrated attributions for differentiable models.

- **Detecting and Addressing Bias**  
  **Q:** How would you detect and address bias in an ML model? Discuss dataset audits and segment analysis, fairness metrics (EO/DP/EOD), data reweighting/augmentation, post-processing, fairness-aware training, and stakeholder alignment/transparency.  
  **A:** Audit data coverage and label quality by cohort; compute fairness metrics and calibration per group; mitigate via reweighting/augmentation, constraint‑based training or adversarial debiasing, and post‑processing (thresholds, equalized odds). Align with stakeholders on trade‑offs; document model cards and monitoring plans.

---

## Troubleshooting & Debugging

- **High Bias vs High Variance**  
  **Q:** Your model isn’t performing well—how would you determine if the problem is due to high bias or high variance? (Once identified, describe how you would address each case—e.g., improving a high-bias model vs reducing overfitting in a high-variance model.)  
  **A:** I diagnose with learning curves and cross‑validation. High bias shows up as high training and validation error; I fix it by adding capacity, improving features, training longer, and reducing regularization. High variance shows low training error but much worse validation error; I fix it with stronger regularization, data augmentation, simpler models, early stopping, and more data.

- **Non-Converging Model**  
  **Q:** What would you do if your model’s training loss is not decreasing (or is diverging) during training? (Outline steps to troubleshoot, such as checking the learning rate, data preprocessing, model architecture, or looking for bugs in the implementation.)  
  **A:** I first lower the learning rate and verify the loss on a single batch can be overfit. I check data and labels (ranges, normalization, leakage), confirm gradients flow and norms are reasonable, and inspect initialization and activation choices. If needed, I simplify the model, add gradient clipping, and write unit tests around the loss and a tiny synthetic problem to catch implementation bugs.

- **Sanity Checks**  
  **Q:** Run label randomization, single-batch overfit, and gradient flow diagnostics; what failures does each reveal?  
  **A:** If the model cannot overfit a tiny batch, the optimizer, architecture, or data pipeline is broken. If training on randomized labels still achieves very high accuracy, there is likely leakage. If gradient norms are near zero or explode through layers, there is a vanishing/exploding gradient issue that calls for better initialization, normalization, or clipping.

- **Learning Curves & Ablations**  
  **Q:** How do you design and interpret them to localize high-bias vs high-variance vs data issues?  
  **A:** I plot training and validation error vs dataset size and training steps. Parallel curves at high error suggest high bias; widening the model or improving features should help. A large gap that shrinks with more data suggests variance; regularization and augmentation help. Ablations remove components or features to identify which ones actually drive performance.

- **Out-of-Distribution Failures**  
  **Q:** How do you detect and mitigate OOD failures in production (dataset shift, spurious correlations)? Outline detection, monitoring, and mitigation strategies.  
  **A:** Detect via density/score proxies (MSP, energy, ensembles), drift tests, and OOD benchmarks; monitor per‑segment errors and uncertainty. Mitigate with robust training (augmentations, invariances), domain adaptation, confidence‑based abstention, and fallback heuristics; retrain on refreshed data.

- **Production Performance Drop**  
  **Q:** If a model that performed well in training starts failing or degrading in production, how would you diagnose the issue? (Consider factors like data drift, changes in input data quality, distribution shifts, or pipeline bugs. How would you detect and resolve the problem?)  
  **A:** I compare online feature distributions and priors to the training baselines, check schema/freshness, and run shadow eval on recent traffic. I audit upstream changes, validate preprocessing parity, and segment errors to find where performance regressed. Fixes include rolling back, hot‑patching features, recalibrating thresholds, or retraining on refreshed data.

- **Improving Inference Speed**  
  **Q:** Suppose your deployed model’s inference is too slow. What are some ways to make predictions more efficient? (Discuss options like model compression, quantization, distillation, hardware accelerators, batching requests, etc., and the trade-offs involved in each approach.)  
  **A:** I reduce latency with model distillation, quantization, pruning, and operator/kernel fusion; I also enable batching and use accelerators where appropriate. The trade‑off is accuracy vs speed/cost, so I measure end‑to‑end impact and keep a budget for tail latency. Caching hot results and precomputing features can further cut p99.

- **Improving an 80% Accuracy Classifier**  
  **Q:** You have a classification model with 80% accuracy. What steps would you take to improve it, including diagnosing bias vs variance?  
  **A:** I start by choosing the right metric (e.g., AUCPR under imbalance) and running an error analysis by segment to see where it fails. Then I address data issues (label quality, coverage), improve features, and try stronger baselines or ensembles. Learning curves tell me whether I need capacity (bias) or regularization/augmentation and more data (variance). Finally, I calibrate and tune thresholds to optimize business utility.
