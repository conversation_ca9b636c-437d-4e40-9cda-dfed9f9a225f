## ML Engineering & Practices Interview Questions (Google DeepMind Research Engineer)

### Model Training & Evaluation (Engineering Focus)
- **Evaluation Metrics**: How to evaluate a classification model using accuracy, precision, recall, F1-score, and ROC-AUC.
- **Train/Validation/Test Split**: Roles of training, validation, and test sets and their uses.
- **Cross-Validation**: Importance and how it helps in model selection and avoiding overfitting.
- **Imbalanced Data**: Methods to handle imbalanced datasets, including resampling, class weights, and choosing appropriate metrics.
- **Early Stopping Criteria**: Indicators for stopping training to prevent overfitting or unnecessary computation.
- **Hyperparameter Tuning**: Approaches such as grid search, random search, and Bayesian optimization.
- **Metric Selection Under Imbalance**: When to prefer AUCPR over AUROC; how to report metrics across segments.
- **Threshold Selection & Costs**: Choose a classification threshold under asymmetric costs; derive decision-theoretic threshold with calibrated probabilities.
- **Time-Series / Non-iid Splits**: Design proper splits for temporal data; rolling-origin cross-validation; detect and prevent leakage.
- **Offline�Online Confidence**: How do you ensure offline metrics predict online impact? Discuss <PERSON>'s paradox and segment analysis.
- **A/B Test Design**: Pick primary/guardrail metrics, sample size, power, and handle sequential testing safely.
- **Budgeted Hyperparameter Search**: Compare random vs Bayesian vs multi-fidelity (ASHA/BOHB); early stopping strategies.
- **Case Study (80% Validation Accuracy)**: On a balanced dataset with 80% validation accuracy, propose a sequenced plan of diagnostics and experiments (data, features, model, evaluation) and what you'd expect to learn from each step.
- **Data Versioning & Lineage**: How to track data provenance, schema changes, and feature lineage in production ML pipelines.
- **Shadow Deployments**: Design shadow evaluation systems to validate model performance before full deployment.
- **Sequential Testing & Multiple Comparisons**: Handle multiple testing corrections in A/B tests; design sequential testing procedures safely.

### Deep Learning Engineering
- **Initialization & Residuals**: Xavier/He, residual connections, and deep signal propagation.
- **Weight Decay vs L2 (AdamW)**: Why decoupled weight decay matters; when L2 ` weight decay.
- **Normalization Choices**: BatchNorm vs LayerNorm vs GroupNorm vs WeightNormtrade-offs and where to use each.
- **Mixed Precision (FP16/bfloat16)**: Benefits, loss scaling, common numerical pitfalls and debugging NaNs.
- **Gradient Clipping**: When it helps; norms vs value clipping; interactions with optimizers.
- **Optimizer Behavior**: When Adam diverges; remedies (learning-rate schedule, warmup, decoupled decay).
- **Training Efficiency**: Maximize batch size, gradient accumulation, activation checkpointing, data pipeline throughput.
- **Transfer Learning Strategy**: Freeze/unfreeze schedules; when to fine-tune head-only vs full model.
- **Attention Scaling**: O(N^2) cost; memory considerations; simple mitigation strategies.
- **Reading Curves**: Diagnose vanishing/exploding gradients using gradient norms and loss/accuracy curves.
- **Sanity Checks**: Implement label randomization, single-batch overfit, and gradient flow diagnostics; what failures each reveals.
- **Learning Rate Scheduling**: Compare step decay, cosine annealing, and warmup strategies; when each is most effective.
- **Memory Optimization**: Gradient accumulation, activation checkpointing, and selective recomputation strategies for large models.

### Reinforcement Learning Engineering
- **Map an RL problem using four axes**: Goal (prediction vs control), Knowledge (model-based vs model-free), Method (value vs policy vs actorcritic), Process (on- vs off-policy). Justify 12 trade-offs and note one risk + mitigation.
- **Deadly Triad**: Identify off-policy + bootstrapping + function approximation in a scenario and propose mitigations (target networks, double estimators, conservative updates, on-policy fallback).
- **Exploration Design**: Compare �-greedy, UCB/Thompson sampling, and entropy bonuses; pick for sparse-reward tasks and specify metrics you'd monitor.
- **Off-Policy Evaluation**: When to use importance sampling (trajectory vs per-decision), variance pathologies, clipping/weight-normalization, and doubly-robust estimators; report CIs and sensitivity checks.
- **Policy Gradients vs Value-Based**: When to choose each; variance reduction via baselines and advantage estimation; why actorcritic.

### ML System Design
- **End-to-End Pipeline**: Key stages such as data collection, preprocessing, feature engineering, model training, validation, deployment, and monitoring.
- **Scalable Deployment**: Designing for real-time predictions with considerations for latency, throughput, caching, and load balancing.
- **Reproducibility & Versioning**: Best practices for version control, experiment tracking, and random seed setting; seeds; determinism (GPU/TPU); environment capture.
- **Training�Serving Skew**: Name top causes (feature drift, different preprocessing, stale embeddings), detection methods (shadow eval, canary, feature parity checks), and defenses (feature store, schema validation, offline/online parity tests).
- **Distributed Training Topology**: Data vs model vs pipeline parallelism; when to use AllReduce vs parameter server.
- **Input Pipeline Performance**: Optimize DataLoader (num workers, pin_memory, prefetch); overlap compute and data transfer.
- **Data Pipelines & Feature Stores**: Ingestion � validation � featurization � training; offline/online parity; schema checks; data versioning & lineage; feature reuse and freshness.
- **DDP > DP**: Why DistributedDataParallel outperforms DataParallel; failure modes and debugging stragglers.
- **Scaling Very Large Models**: FSDP/DeepSpeed/ZeRO, CPU offload, optimizer state sharding.
- **Deployment Patterns**: Batch vs online; canary/blue-green; rollback and SLA/SLO considerations.
- **Monitoring & Drift**: Detect covariate/prior/concept drift; shadow deployments; alerting design.
- **Low-Latency Inference**: Caching, request batching, quantization and distillation; hardware accelerators; throughput vs latency trade-offs.
- **GPU OOM Debugging**: Systematic approach to memory issues during training; gradient accumulation vs batch size trade-offs.
- **Checkpointing & Recovery**: Design fault-tolerant training with efficient checkpointing; handling preemption in cloud environments.
- **Multi-GPU Communication**: AllReduce vs Parameter Server trade-offs; handling stragglers and network bottlenecks.

### Fairness & Interpretability (Practical)
- **Fairness Metrics (EO, DP, EOD)**: Define each; when to use them; trade-offs with accuracy and calibration; how to compute from confusion matrices.
- **Segmented Evaluation**: Disaggregate metrics by sensitive attributes; detect Simpson's paradox; choose parity goals (e.g., TPR parity vs PPV parity) and implications for thresholding.
- **Mitigations**: Reweighting/resampling, adversarial debiasing, and post-processing (group-specific thresholds); discuss side effects, distribution shift risks, and monitoring for regressions.
- **Interpretability Toolbox**: Global vs local; SHAP, LIME, integrated gradients, permutation importance; stability concerns with correlated features; when to prefer inherently interpretable models.
- **Case: Disparate FNR Across Groups**: Triage hypotheses, check group-wise calibration, consider cost-sensitive training or fairness constraints, evaluate threshold adjustments, and define online monitoring + rollback.
- **Governance & Documentation**: Model cards and data sheets; fairness review; logging/alerts for subgroup-metric drift.

### Research Engineer Craft
- **From Paper � Baseline**: Describe how you would build a minimal, reproducible re-implementation, what you would ablate first, and how you'd scale and harden it for production.
- **Experiment Design at Scale**: Factorial vs sequential experimentation; early stopping; primary vs guardrail metrics; drawing trustworthy conclusions under budget.
- **Code Quality in ML**: What unit/property tests would you add for preprocessing and numerics? How would you validate dataset schemas and catch silent data bugs?
- **Collaboration & Artifacts**: How do you structure experiment logs, reproducible notebooks, and summaries for research partners?
- **Exit Criteria**: When do you stop iterating and ship? Define success, diminishing returns, and compute-cost trade-offs.
- **Reproducibility Engineering**: Version control for data, models, and experiments; containerization; environment management; random seed handling.
- **Research Velocity**: Balancing exploration vs exploitation in research; when to pursue incremental vs breakthrough approaches.

### Large Language Model Engineering
- **Scaling Laws**: Understand compute-optimal training (Chinchilla scaling); parameter count vs data size trade-offs.
- **Tokenization**: BPE, SentencePiece, and their impact on model performance; handling multilingual and code tokenization.
- **Fine-tuning Strategies**: Full fine-tuning vs LoRA vs prompt tuning; catastrophic forgetting prevention.
- **Alignment Techniques**: RLHF pipeline design; Constitutional AI; DPO vs PPO trade-offs for preference learning.
- **Inference Optimization**: KV-cache management; speculative decoding; model quantization (INT8, FP16, bfloat16).
- **Safety & Evaluation**: Red-teaming approaches; automated safety evaluation; handling hallucinations and factual accuracy.

### Troubleshooting & Debugging
- **High Bias vs High Variance**: Diagnosing and addressing issues related to underfitting or overfitting.
- **Sanity Checks**: Run label randomization, single-batch overfit, and gradient flow diagnostics; what failures does each reveal?
- **Learning Curves & Ablations**: How do you design and interpret them to localize high-bias vs high-variance vs data issues?
- **Non-Converging Model**: Steps to troubleshoot training loss issues, including learning rate and data checks.
- **Production Performance Drop**: Diagnosing production failures, data drift, distribution shifts, or pipeline bugs.
- **Improving Inference Speed**: Methods like model compression, quantization, distillation, hardware accelerators, and batching requests.

### Computer Vision & NLP Engineering
- **Data Augmentation**: Advanced augmentation strategies; AutoAugment and RandAugment; domain-specific augmentation techniques.
- **Transfer Learning in Vision**: Pre-training strategies; domain adaptation; fine-tuning vs feature extraction trade-offs.
- **Multi-Modal Systems**: Vision-language models; cross-modal retrieval; alignment strategies between modalities.
- **Production NLP**: Text preprocessing pipelines; handling different languages and scripts; real-time inference constraints.
- **Evaluation in Vision/NLP**: Beyond standard metrics; human evaluation; adversarial evaluation; robustness testing.

### MLOps & Production Monitoring
- **Model Governance**: Model registry; approval workflows; compliance tracking; audit trails.
- **Performance Monitoring**: Latency, throughput, and resource utilization monitoring; SLA enforcement.
- **Cost Optimization**: Training cost analysis; inference cost per request; resource right-sizing.
- **Incident Response**: Model failure runbooks; rollback strategies; communication protocols during outages.
- **Continuous Integration/Deployment**: ML pipeline testing; model validation gates; automated deployment strategies.