## Common ML Fundamentals Interview Questions (Google DeepMind Research Engineer)

### Fundamentals
- **Types of Learning**: Explain the differences between supervised, unsupervised, and reinforcement learning. Provide an example use case for each.
- **<PERSON>ias vs. Variance**: Describe the bias-variance trade-off and its importance for model generalization.
- **Overfitting and Underfitting**: Define overfitting vs underfitting and how to identify each.
- **Regularization (L1 vs L2)**: What is regularization and how do L1 and L2 differ? When might you prefer one over the other?
- **Gradient Descent Variants**: Explain batch, stochastic, and mini-batch gradient descent, with pros and cons of each.
- **Numerical Stability (Log-Sum-Exp/Softmax)**: Why and how to implement a numerically stable softmax cross-entropy in production?
- **Calibration**: What is probability calibration, how do you measure it (ECE/Brier), and when should you apply temperature scaling vs isotonic regression?
- **Feature Scaling/Normalization**: When is feature scaling required? How does it affect optimization dynamics and regularization?
- **Data Leakage Basics**: Provide two examples of subtle leakage and concrete defenses.
- **<PERSON><PERSON><PERSON> vs Frequentist (practical)**: Contrast the paradigms and discuss when you'd prefer each (e.g., small-n, hierarchical priors, regularization-as-prior), including implications for calibration and uncertainty.
- **Convex Functions**: Define a convex function mathematically (f(λx + (1-λ)y) ≤ λf(x) + (1-λ)f(y)) and geometrically (line segment between any two points lies above the function). Explain key properties: local minima are global minima, sum of convex functions is convex, composition rules with non-decreasing convex functions.
- **Convex Optimization**: What makes an optimization problem convex (convex objective + convex constraint set)? Why does convexity matter for ML (guaranteed global optimum, efficient algorithms, no local minima traps)? Contrast with non-convex optimization challenges (local minima, saddle points, initialization sensitivity). Give examples: linear/logistic regression, SVM, LASSO are convex; neural networks, matrix factorization are non-convex.
- **KKT Conditions**: State Karush-Kuhn-Tucker conditions for constrained optimization; worked example for L2-constrained logistic regression.
- **Second-Order Methods**: Compare Newton's method vs gradient descent; when does Newton diverge? Explain BFGS/L-BFGS and their advantages.
- **Autodiff & Backpropagation**: Explain reverse-mode vs forward-mode autodiff; when to prefer forward-mode; JVP vs VJP computations.


### Classical ML
- **SVM vs Logistic Regression**: Margin principle; hinge vs log-loss; soft vs hard margin; when SVMs win (small-n, high-dim, robust margins) vs when logistic is preferable (probability outputs, calibration).
- **Trees & Ensembles**: Random Forest vs Gradient Boosting (XGBoost/LightGBM) — bias/variance trade-offs, overfitting risks, regularization knobs (depth, learning rate, subsampling), and when to choose each.
- **Gaussian Processes**: When to prefer a GP (small-n, uncertainty quantification, smooth priors) vs deep nets; cost scaling and kernel choices.
- **Probability Theory Essentials**: Define KL divergence, entropy, and cross-entropy; derive their relationships; explain when KL divergence is infinite.
- **Statistical Inference**: Compare MLE vs MAP estimation; conjugate priors for common distributions; small-n behavior and overfitting.
- **Confidence vs Credible Intervals**: Distinguish frequentist confidence intervals from Bayesian credible intervals; bootstrap pitfalls with heavy-tailed distributions.

### Deep Learning
- **Backpropagation**: How it works and why it's essential for training deep networks.
- **Vanishing/Exploding Gradients**: Causes and mitigation strategies.
- **CNNs vs RNNs**: Architectural differences and typical applications (image processing vs sequential data).
- **Dropout**: Purpose and effect on model performance.
- **Batch Normalization**: Benefits for training stability and performance.
- **Transfer Learning**: Use cases for pre-trained models or feature reuse.
- **Attention & Transformers**: How attention mechanisms work and how Transformers improved sequence modeling.
- **Activation Functions**: Compare ReLU, GELU, SiLU, and their properties; dead neuron problem and solutions.
- **Loss Functions**: Cross-entropy, focal loss, triplet loss, contrastive loss - when to use each.
- **Generative Models**: VAE vs GAN vs Diffusion models - core principles, training objectives, and trade-offs.
- **Universal Approximator Caveats**: Why UAT does not imply learnability; role of sample complexity and inductive biases for practical training.

### Reinforcement Learning (Theoretical Foundations)
- **MDP Framework**: Define state space, action space, transition probabilities, reward function, and policy in the context of reinforcement learning.
- **Value Functions**: Distinguish between state-value function V(s) and action-value function Q(s,a); Bellman equations and their significance.
- **Policy vs Value Methods**: Theoretical foundations of policy gradient methods vs value-based methods; convergence guarantees.
- **Exploration vs Exploitation**: Multi-armed bandit problem; regret bounds; theoretical analysis of exploration strategies.
- **Temporal Difference Learning**: TD(0), TD(λ), and eligibility traces; bias-variance trade-offs in temporal difference methods.
- **Policy Gradient Theorem**: Derive the policy gradient theorem; importance of baseline and advantage functions.

### Computer Vision & NLP Fundamentals
- **CNN Architectures**: Evolution from LeNet to ResNet to Vision Transformers; architectural innovations and their motivations.
- **Object Detection**: Two-stage (R-CNN family) vs one-stage (YOLO, SSD) detectors; anchor-based vs anchor-free methods.
- **Sequence Models**: RNN, LSTM, GRU limitations; Transformer architecture; positional encodings.
- **Language Models**: N-gram models to neural language models; perplexity as evaluation metric.
- **Word Embeddings**: Word2Vec, GloVe, and contextual embeddings (BERT, GPT); semantic relationships in embedding space.
