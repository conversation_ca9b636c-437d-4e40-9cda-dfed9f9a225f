# DeepMind ML Fundamentals Interview — Consolidated Topic & Question Checklist

Sources integrated: `ml-fundamentals/README.md` (official guidance), `google_deep_mind_ml_fundamentals_research_engineer_interview_question_bank_curated.md`, `RL.md`, and `exercises.md`.

This is a curated, high‑signal checklist aligned to a 60‑minute conversational interview (no coding). Emphasis per guideline: breadth and depth in ML (esp. deep learning) plus ML engineering workflows and pipelines. Use a notepad for quick diagrams or 2–3 line derivations.

How to use:
- Drill 2–3 sections/day. For each section: (i) 5 quick definitions, (ii) 1 derivation, (iii) 1 design trade‑off, (iv) 1 concrete story from experience.
- Focus first on deep learning, data/evaluation, systems/MLOps, and debugging (most probed for RE roles). RL and generative/LLMs as applicable to your background.

---

## 0) Warm‑ups (rapid fire)
- Define: convex function, Lipschitz continuity, strong convexity, subgradient, Hessian, Jacobian, condition number, KL divergence, entropy, cross‑entropy, log‑sum‑exp trick, bias–variance trade‑off, VC dimension, PAC learning, Bayes vs frequentist, calibration, prior vs. hyperprior, eigenvalue/eigenvector, SVD, PD vs. PSD.
- Name and briefly explain 5 optimizers beyond SGD. When does each shine/fail?
- Name 5 causes of training→serving skew and how to detect them.
- Compare supervised, unsupervised, and RL: key differences and example use cases.
- Explain batch, stochastic, and mini-batch gradient descent: pros/cons of each.

---

## 1) Core Math & Optimization
- Concept checks
  - Convexity: equivalent definitions; composition rules; when composition fails.
  - Gradient descent from first‑order Taylor; convergence assumptions (fixed vs diminishing LR).
  - Second‑order: Newton, quadratic approximation, divergence modes; line search vs trust region; BFGS/L‑BFGS.
  - Hessian eigenvalues: curvature, step size/preconditioning implications.
  - Constraints: KKT conditions; worked example (e.g., L2‑constrained logistic regression).
  - Numerics: softmax + cross‑entropy stability; implement/derive log‑sum‑exp.
  - Autodiff: backprop as reverse‑mode; JVP/VJP; when forward‑mode is preferable.
- Application prompts
  - Loss oscillations with momentum: curvature hypotheses and fixes.
  - LR finder suggests very small LR: root causes and quick tests.
- From exercises
  - Softmax + cross‑entropy gradient: dL/dz = p − y; numeric checks; shapes.

---

## 2) Probability, Statistics & Inference
- Concept checks
  - Bayes’ theorem (state/derive); calibrated example with odds and likelihood ratios.
  - MLE vs MAP; conjugate priors for Bernoulli/Binomial/Poisson/Gaussian; small‑n behavior.
  - Bias–variance decomposition; under/overfitting link.
  - Confidence vs credible intervals; bootstrap pitfalls with heavy tails.
  - Hypothesis testing: p‑values, multiple testing, FDR; design an A/B test with guardrails.
  - Markov chains and mixing; Gibbs vs Metropolis–Hastings; when to use variational inference.
- Application prompts
  - Overconfident but accurate classifier: measuring/improving calibration (temp scaling, isotonic, label smoothing, focal loss).
  - Train/validation/test split roles; when cross-validation helps vs. hurts.
- From exercises
  - Welford online mean/variance; stability vs naive.
  - Beta‑Binomial Bayesian update; posterior vs MLE; credible intervals.
  - Two‑proportion z‑test; power, MDE, sequential testing caveats.

---

## 3) Linear Algebra & Numerical Methods
- Invertibility criteria: rank/determinant/full rank; geometric intuition.
- Factorizations: LU/QR/Cholesky/SVD — when/why; normal equations and conditioning.
- Jacobians for y=Ax and simple MLP; broadcasting/shapes gotchas.
- Gaussian elimination vs Cholesky on SPD; stability trade‑offs.
- Conditioning of XᵀX in linear regression; why ridge helps.
- From exercises: PCA via SVD — centering, scaling, explained variance, SVD vs eigendecomposition.

---

## 4) Classical ML
- Concept checks
  - Linear/Logistic regression: link, decision boundary, L1/L2/elastic‑net geometry.
  - SVMs: margin principle; primal/dual; kernels; hinge vs logistic; soft vs hard margin; when SVMs win in 2025.
  - Trees & ensembles: bias/variance; bagging vs boosting; XGBoost vs Random Forests.
  - kNN, Naïve Bayes, Gaussian Processes; when to prefer a GP.
- Application prompts
  - Data leakage case study: sneaky leakage in tabular and defenses.
  - Imbalanced classes: resampling, class‑weighted loss, threshold moving, focal loss, AUCPR; evaluation metrics beyond accuracy.
- From exercises
  - Linear regression: normal equation vs GD; regularization (ridge); large‑n scaling and memory/streaming.
  - Logistic regression: stable log‑loss; L2 penalty; class weighting; ROC‑AUC and calibration curve.
  - Multinomial softmax classifier: log‑sum‑exp; label smoothing; temperature scaling.

---

## 5) Deep Learning
- Architectures & training: CNNs, RNNs/LSTMs/GRUs, Transformers; attention (Q/K/V), positional encodings, receptive fields.
- Activations: ReLU, GELU, SiLU; saturation and dead neurons.
- Normalization: batch/layer/group/weight; placement and rationale.
- Regularization: dropout, augmentation, label smoothing, weight decay; L1 vs L2 (sparsity vs shrinkage).
- Optimizers: SGD+momentum, RMSProp, Adam/AdamW, Adagrad; warmup + cosine; when Adam diverges; decoupled weight decay.
- Initialization & scale: Xavier/Kaiming; residual connections; deep signal propagation.
- Diagnostics: reading curves; vanishing/exploding; gradient clipping; data pipeline stalls.
- Architecture choices: CNNs vs RNNs for different data types; when to use transfer learning.
- Universal approximator caveats: approximation ≠ learnability; sample complexity; inductive biases.
- Large‑model pragmatics: activation checkpointing, mixed precision/bfloat16, ZeRO, CPU offload, chunking, tensor parallelism.
- Training optimization tactics: efficient optimizers (AdamW preferred), hardware acceleration (GPU/TPU), max batch size, momentum, mixed precision, He/Xavier init, gradient accumulation, data normalization post-GPU transfer.
- From exercises
  - MLP from scratch: init/forward/backward; finite‑diff sanity checks.
  - Optimization steps: SGD/momentum/Adam; LR decay (cosine); compare on convex tasks.

---

## 6) Reinforcement Learning
- Foundations (mental model)
  - MDP tuple (S,A,P,R,γ); return; value functions; advantage; greedy policy improvement.
  - Prediction vs Control; model‑based vs model‑free; value vs policy vs actor‑critic; on‑policy vs off‑policy.
  - Bellman expectation & optimality equations; bootstrapping bias–variance.
  - Eligibility traces and TD(λ); off‑policy evaluation via importance sampling.
  - Deadly triad (off‑policy + bootstrapping + function approximation) and mitigations.
  - Exploration: ε‑greedy, UCB, Thompson sampling; exploration–exploitation trade‑off.
  - Offline RL and distribution shift; evaluation without online A/B.
  - Credit assignment and long horizons; reward shaping pitfalls; sparse‑reward strategies.
- Key algorithms & formulas
  - DP: iterative policy evaluation; value iteration.
  - MC: first‑visit prediction; on‑policy control.
  - TD: TD(0); SARSA; Q‑learning.
  - Policy gradient theorem; REINFORCE with baseline; actor–critic (one‑step); entropy bonus.
  - DQN essentials; Double DQN; target networks; replay.
- Modern tie‑in: PPO and RLHF (KL‑regularized control, GAE, clipped objective; reward modeling from preferences).
- High‑level prompts
  - Map an RL problem using the four mental models: Goal (prediction vs control), Knowledge (model‑based vs model‑free), Method (value vs policy vs actor‑critic), Process (on‑ vs off‑policy). Justify trade‑offs.
    - Outline starter: State each dimension (Goal/Knowledge/Method/Process), argue 1–2 trade‑offs, list 1 risk + 1 mitigation.
    - Example answer sketch: Continuous‑action robot nav → Goal: control; Knowledge: model‑free; Method: actor–critic (DDPG/TD3) vs value‑based; Process: off‑policy for sample efficiency; risk: deadly triad → mitigations: target nets, Double Q, conservative updates.
  - TD vs MC vs DP: connect bootstrapping to bias–variance; choose an approach given data availability, episode length, and compute.
    - Outline starter: Relate bootstrapping→bias/variance; pick online (TD) vs episodic (MC) vs planning (DP); consider compute and convergence.
  - Exploration design for sparse rewards: compare ε‑greedy, entropy bonus, optimistic init, UCB/bonus‑based; risks and metrics to monitor.
    - Outline starter: Start with ε‑greedy baseline; add entropy bonus/optimistic init; for MDPs add count/bonus‑based; monitor coverage and reward variance.
  - Off‑policy evaluation in production: when to use importance sampling (trajectory vs per‑decision); variance pathologies and mitigation.
    - Outline starter: Prefer per‑decision IS with clipping/weight‑normalization; mention doubly‑robust; report CIs and sensitivity checks.
  - Detect/mitigate the deadly triad in deep RL: symptoms and fixes (target networks, double estimators, conservative updates, on‑policy fallback).
    - Outline starter: Identify off‑policy + bootstrapping + FA; mitigations: target nets, Double Q, regularization/smaller steps, or move on‑policy.
  - RLHF with PPO+KL: articulate objective and shaped rewards; why on‑policy; how to control KL, normalize advantages, and evaluate offline.
    - Outline starter: Write J(θ)=E[r_φ]−β·KL; token‑level r′_t; justify on‑policy; controls: KL target, advantage norm, length penalty; offline eval with held‑out prompts + reward model.
    - Example answer sketch: J(θ)=E[r_φ]−β·KL, r′_t=r_t−β log(π/π_ref); use PPO (on‑policy) with KL target + advantage norm; eval via held‑out prompts, reward model score, safety/toxicity checks.
 - Daily drill (5–10 min)
  1) Pick one high‑level prompt above.
  2) Spend 2 minutes structuring your answer with the outline starter.
  3) State 1 formula from memory (e.g., TD(0), policy gradient theorem, or PPO‑clip objective).
  4) Add 1 risk + 1 mitigation specific to the scenario.
  5) Record and review for clarity, trade‑offs, and numerical precision.
 - From exercises
   - Policy evaluation and value iteration on grid MDP; terminal states and stochastic rewards.
   - Tabular Q‑learning with ε‑greedy; learning curves and ε schedules.

---

## 7) Generative Models & LLMs
- VAEs: ELBO, posterior collapse; priors.
- GANs: minimax, mode collapse, WGAN objectives.
- Normalizing flows: change of variables; tractable likelihood.
- Diffusion models: forward/reverse processes; score matching.
- LLMs: causal vs masked LM; tokenization; attention complexity; retrieval‑augmented generation; alignment (SFT, RLHF, DPO); safety.
- Evaluation: FID, BLEU/ROUGE, perplexity, human eval; hallucination/factuality.
- Application: achieving sub‑100ms inference at scale (quantization, distillation, caching, KV offload, speculative decoding) and trade‑offs.

---

## 8) Data, Evaluation & Experimentation
- Metric selection by objective: accuracy, precision/recall/F1, AUROC vs AUPRC; ranking (NDCG/MRR/MAP); calibration (ECE/Brier).
- Thresholding and cost curves; decision‑theoretic thresholds.
- Cross‑validation (k‑fold, time‑series); leakage via global normalization; data versioning/lineage.
- Trustworthy offline metrics that predict online impact; Simpson’s paradox; segment analysis.
- Case: 80% validation accuracy on balanced set → diagnostics and sequenced experiments (data, features, model, evaluation), with bias/variance clues.
- Hyperparameter tuning: grid vs random vs Bayesian; early stopping criteria.

---

## 9) ML Systems Design & MLOps
- Distributed training: data/model/pipeline parallelism; sharding; all‑reduce vs parameter server; checkpointing.
- Training infrastructure: DataLoader optimization (max_workers, pin_memory), DistributedDataParallel over DataParallel, DeepSpeed/FSDP for large models.
- Data pipelines: ingestion → validation → featurization → training; TFX‑like components; feature stores.
- Reproducibility: seeds; determinism/non‑determinism (GPU/TPU); env capture; experiment tracking.
- Deployment patterns: batch vs online; streaming; canary/blue‑green; rollback; SLA/SLO; cost controls.
- Monitoring: drift (covariate/prior/concept), performance decay, outliers, shadow deployments.
- Privacy/security: PII handling, differential privacy, federated learning, membership‑inference defenses.
- Design prompts
  - TB‑scale training pipeline: partitioning, input pipelines, storage/I/O.
  - GPUs OOM during training: accumulation, shard optimizer state, batch size vs grad scaling, selective activation checkpointing.
  - Billions of QPS thought experiment: caching, approximate retrieval, feature precompute, model compression, CDNs, graph partitioning.
  - End‑to‑end ML pipeline: data collection → preprocessing → training → deployment → monitoring; key failure points.
  - Scalable real‑time inference: latency vs throughput trade‑offs; load balancing; caching strategies.
- From exercises
  - Training pipeline skeleton: stages, artifacts, data validation rules, schema/versioning.
  - Distributed training trade‑offs: PS vs AllReduce; failure modes, stragglers, gradient compression.
  - Embedding cache policy (Zipfian): admission/eviction, LFU/LRU hybrid, staleness/refresh.
  - Production ML under constraints: shift detection, eval pitfalls, failure‑mode debugging; 1‑page outline with alerts/mitigations.
- Training optimization checklist: batch size maximization, Bayesian hyperparameter search for large spaces, memory-compute trade-offs (checkpointing vs recomputation).

---

## 10) Fairness, Ethics & Interpretability
- Bias sources: sampling, label, measurement, deployment; metrics (EO, DP, EOD) and mitigations (reweighting, adversarial training, post‑processing).
- Interpretability: global vs local (feature importance, SHAP, LIME, integrated gradients); when to prefer interpretable models.
- Governance: model cards, data sheets, approval checklists, incident response.
- Application: disparate FNR across groups — investigation and ethical/technical response.

---

## 11) Debugging & Failure Analysis
- High‑bias vs high‑variance symptoms and targeted fixes.
- Learning curves and ablations; what each reveals.
- Sanity checks: label randomization, single‑batch overfit, gradient flow diagnostics.
- Data bugs: target leakage, feature drift, timestamp bugs, join keys, time travel.
- Case drill: strong offline metrics but production failure — five hypotheses and triage plan.
- Non‑converging training: systematic debugging (learning rate, data quality, architecture, gradients).
- Production performance drop: data drift vs distribution shift vs pipeline bugs; diagnostic steps.
- Inference speed optimization: model compression, quantization, distillation, hardware acceleration, batching.

---

## 12) Research Engineer Craft
- From paper to baseline: minimal reproducible re‑implementation → ablate → scale → harden.
- Experiment design at scale: factorial vs sequential; early stopping; pick primary vs guardrails.
- Code quality in ML: unit/property tests for preprocessing; numerics tests; dataset schema validation.
- Collaboration: clean experiment logs, repro notebooks, clear summaries for research partners.
- Prompts: discuss a recent paper you implemented (what you didn’t copy and why); when to stop iterating and ship.

---

## 13) Behavioral (Leadership & Collaboration)
- Pushback on a modeling choice — later proven wrong/right; what changed your mind.
- Handling strong disagreements with researchers/PMs; real example.
- Recovering a failing model/launch; partner communication; postmortem learning.
- Proactively learning a new subfield to unlock impact; how you structured it.

---

## 14) Domain‑Focused Deep Dives (pick 1–2 matching your background)
- Recommenders: candidate generation vs ranking; long‑ vs short‑term metrics; exploration; counterfactual eval.
- Vision: augmentation policy search; self‑supervised pretraining; detection vs segmentation pitfalls; class imbalance.
- NLP/LLMs: tokenizer choice; long‑context strategies; retrieval; safety filters; evaluation granularity.
- Time‑series: leakage prevention; backtesting; regime change detection; probabilistic forecasting.

---

## 15) End‑to‑End Scenarios (mock interview starters)
1) Production classifier at 80% accuracy → hour‑by‑hour improvement plan (error analysis, targeted data, model, metrics).
2) Bias surfaced post‑launch → triage, mitigations, and comms plan.
3) Memory‑bound training (seq2seq OOM on 24GB GPUs) → design training approach w/o losing quality.
4) RL for recommendations → states/actions/rewards; offline evaluation; safe rollout.
5) Distributed pipeline on TB‑scale vision data → I/O, sharding, fault tolerance, reproducibility.

---

## 16) Personal Deep Dives (be ready)
- Projects where you scaled an ML system — what changed from prototype.
- Counterintuitive choices (e.g., smaller model wins) — evidence and lessons.
- Failures: most instructive bug shipped; safeguards added.

---

### Appendix — Quick Reference Prompts
- L1 vs L2: geometry, sparsity, optimization, when to prefer each.
- Overfitting mitigations: regularization, augmentation, early stopping, ensembling, data quality.
- Explain reinforcement learning (60 seconds to SWE, and to an exec).
- Handling data imbalance: metrics, sampling, thresholds.
- Hyperparameter tuning: grid vs random vs Bayesian; low‑budget strategies; multi‑fidelity.
- GANs: failure modes; stabilization tricks; evaluation pitfalls.
- Eigen‑stuff: practical uses (PCA, conditioning, Newton’s method).
- Jacobian: why it matters for backprop and sensitivity.
