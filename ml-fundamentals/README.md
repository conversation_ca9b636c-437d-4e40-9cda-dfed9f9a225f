# Official Guideline for ML Fundamentals Interview at Google DeepMind

What to expect: This 60-minute conversational interview (i.e. no coding) will be conducted via Google Hangouts to assess your Machine Learning knowledge. This interview will include some verbal questions, primarily aimed at assessing your “book knowledge” and fundamentals in machine learning. This interview may also focus on engineering aspects for ML workflows and pipelines. We will dig into the details of your past ML engineering experience, ask you about good practices in ML engineering. Questions will usually have a variety of good answers rather than one “correct” answer, and the answers are not likely to be found in typical textbooks, but are usually learned through practical experience. Since the interview will be conducted remotely,
and a whiteboard may not be available, please have a notepad and pen/marker available to
articulate your ideas to your interviewer.

How to prepare: Review fundamental concepts in machine learning (with a particular focus on deep learning), for example by skimming through a course syllabus or the table of contents in a textbook and reviewing the areas you are least familiar with. We are aiming to understand the breadth and depth of your machine learning knowledge and vision and so there are no particular machine learning techniques or models you'll be expected to know, but we will expect significant knowledge across a wide variety of topics.