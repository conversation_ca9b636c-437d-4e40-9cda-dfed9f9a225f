# The Reinforcement Learning Mental Model Canvas (Sutton & Barto Edition)

**For Advanced ML Interviews (DeepMind, etc.) | Last Updated: September 15, 2025**

-----

### **Part 0: MDP and Return (Notation)**

- MDP: $\mathcal{M} = (\mathcal{S}, \mathcal{A}, P, R, \gamma)$ with dynamics
  $P(s', r \mid s, a) \equiv \Pr\{S_{t+1} = s', R_{t+1} = r \mid S_t = s, A_t = a\}$.
- Policy: $\pi(a\mid s) \equiv \Pr\{A_t = a \mid S_t = s\}$.
- Return: $G_t = \sum_{k=0}^{\infty} \gamma^k R_{t+1+k}$ (episodic: until termination; continuing: $\gamma < 1$ ensures convergence).
- Value functions: $v_\pi(s) = \mathbb{E}_\pi[G_t \mid S_t = s]$, $q_\pi(s,a) = \mathbb{E}_\pi[G_t \mid S_t = s, A_t = a]$.
- Advantage: $A_\pi(s,a) = q_\pi(s,a) - v_\pi(s)$.
- Greedy policy improvement: $\pi_{k+1}(s) \in \arg\max_a q_{\pi_k}(s,a)$.

-----

### **Part 1: The Foundational Mental Models**

*(How to think about any RL problem or algorithm)*

#### **1. The Goal: Prediction vs. Control**

  * **🔮 Prediction (Evaluation):** "How good is this policy $\pi$?"

      * **Output:** A value function, $v_\pi(s)$ or $q_\pi(s,a)$.

  * **🕹️ Control (Optimization):** "What is the best policy $\pi_*$?"

      * **Output:** An optimal policy, $\pi_*$. 
      * **Core Idea:** Achieved via **Generalized Policy Iteration (GPI)**, the interplay of policy evaluation and policy improvement.

#### **2. The Knowledge: Model-Based vs. Model-Free**

  * 🗺️ **Model-Based:** Agent knows the environment's dynamics, $p(s', r \mid s, a)$. This allows for **planning**.
  * 🧭 **Model-Free:** Agent learns from direct experience (samples of transitions) without knowing $p(s', r \mid s, a)$.

#### **3. The Learned Component: Value vs. Policy vs. Actor-Critic**

  * 💰 **Value-Based:** Learns a value function. The policy is implicit (e.g., $\epsilon$-greedy w.r.t. values).
  * 📜 **Policy-Based:** Directly learns a parameterized policy, $\pi(a\mid s, \boldsymbol{\theta})$.
  * 🎭 **Actor-Critic:** A hybrid. The **Actor** (policy, $\pi(a\mid s, \boldsymbol{\theta})$) learns how to act, and the **Critic** (value function, $\hat{v}(s, \mathbf{w})$) provides feedback.

#### **4. The Learning Process: On-Policy vs. Off-Policy**

  * 👣 **On-Policy:** Learns about policy $\pi$ from experience generated by $\pi$. "Learn on the job."
 
 * 🕵️ **Off-Policy:** Learns about a *target* policy $\pi$ from experience generated by a *behavior* policy $\mu$ (where $\mu \neq \pi$). "Learn from others' experience."

-----

### **Part 2: The Algorithm Families & Key Formulas**

| Family                       | Key Idea                                                    | Model? | Algorithms & Key Formulas                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
| :--------------------------- | :---------------------------------------------------------- | :----- | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **Dynamic Programming (DP)** | Plans using a perfect model; bootstraps.                    | Based  | **Prediction** — Iterative Policy Evaluation (computes $v_\pi$): $v_{k+1}(s) \leftarrow \sum_{a} \pi(a\mid s) \sum_{s', r} p(s', r\mid s, a) [r + \gamma\, v_k(s')]$ <br><br> **Control** — Value Iteration (computes $v_*$): $v_{k+1}(s) \leftarrow \max_{a} \sum_{s', r} p(s', r\mid s, a) [r + \gamma\, v_k(s')]$                                                                                                                                                                                                                                       |
| **Monte Carlo (MC)**         | Learns from complete episodes; no bootstrapping.            | Free   | **Prediction** — First-Visit MC Prediction (computes $v_\pi$): $V(S_t) \leftarrow V(S_t) + \alpha\, [G_t - V(S_t)]$ <br><br> **Control** — On-Policy MC Control (computes $q_*$): Apply the prediction update to $Q(S_t,A_t)$ and improve policy via $\epsilon$-greedy.                                                                                                                                                                                                                                                                                    |
| **Temporal-Difference (TD)** | Learns from each step; bootstraps.                          | Free   | **Prediction** — TD(0) Prediction (computes $v_\pi$): $V(S_t) \leftarrow V(S_t) + \alpha\, [R_{t+1} + \gamma V(S_{t+1}) - V(S_t)]$ [MUST MEMORIZE] <br><br> **On-Policy Control** — SARSA (computes $q_\pi$ for an improving $\pi$): $Q(S_t,A_t) \leftarrow Q(S_t,A_t) + \alpha\, [R_{t+1} + \gamma Q(S_{t+1},A_{t+1}) - Q(S_t,A_t)]$ [MUST MEMORIZE] <br><br> **Off-Policy Control** — Q-learning (computes $q_*$): $Q(S_t,A_t) \leftarrow Q(S_t,A_t) + \alpha\, [R_{t+1} + \gamma \max_{a} Q(S_{t+1},a) - Q(S_t,A_t)]$ [MUST MEMORIZE]                   |
| **Policy Gradient**          | Directly parameterizes $\pi(a\mid s, \boldsymbol{\theta})$. | Free   | **REINFORCE with baseline**: $\boldsymbol{\theta} \leftarrow \boldsymbol{\theta} + \alpha\, (G_t - \hat{v}(S_t, \mathbf{w}))\, \nabla \ln \pi(A_t\mid S_t, \boldsymbol{\theta})$ [MUST MEMORIZE] <br><br> **Actor–Critic (one-step)**: $\delta_t = R_{t+1} + \gamma\, \hat{v}(S_{t+1}, \mathbf{w}) - \hat{v}(S_t, \mathbf{w})$; $\boldsymbol{\theta} \leftarrow \boldsymbol{\theta} + \alpha\, I_t\, \delta_t\, \nabla \ln \pi(A_t\mid S_t, \boldsymbol{\theta})$; $\mathbf{w} \leftarrow \mathbf{w} + \beta\, \delta_t\, \nabla \hat{v}(S_t, \mathbf{w})$ |

-----

### **Part 3: Core Concepts & Interview Talking Points**

  * **The Bellman Equations (The Foundation)**

      * **Bellman Expectation Equation (for Evaluation):** Defines the value of a policy recursively.
        $v_\pi(s) = \mathbb{E}_\pi\big[ R_{t+1} + \gamma\, v_\pi(S_{t+1}) \mid S_t = s \big]$ [MUST MEMORIZE]
        
        $q_\pi(s,a) = \mathbb{E}_\pi\big[ R_{t+1} + \gamma\, v_\pi(S_{t+1}) \mid S_t = s, A_t = a \big]$
      * **Bellman Optimality Equation (for Control):** Defines the optimal value function, independent of policy.
        $v_*(s) = \max_a \mathbb{E}\big[ R_{t+1} + \gamma\, v_*(S_{t+1}) \mid S_t = s, A_t = a \big]$
        
        $q_*(s,a) = \mathbb{E}\big[ R_{t+1} + \gamma\, \max_{a'} q_*(S_{t+1}, a') \mid S_t = s, A_t = a \big]$ [MUST MEMORIZE]

  * **Bootstrapping: The Bias-Variance Trade-off**

      * **What is it?** Updating an estimate based on other estimates (`V(S_t)` is updated using `V(S_{t+1})`).
      * **Who uses it?** **DP** and **TD** methods. **MC** does not.
      * **Interview Point:** "TD learning's key advantage over MC is its lower variance and ability to learn online from incomplete episodes. However, this comes at the cost of introducing bias, as it bootstraps from potentially inaccurate value estimates. This trade-off is central to RL."

  * **Sample Efficiency: On-Policy vs. Off-Policy**

      * **On-Policy:** Low sample efficiency. Experience generated by policy $\pi_k$ is discarded after $\pi_{k+1}$ is formed.
      * **Off-Policy:** High sample efficiency. Allows for **Experience Replay**—storing transitions from a behavior policy in a memory buffer and reusing them for multiple updates to the target policy.
      * **Interview Point:** "A key reason Deep Q-Networks (DQN) were a breakthrough is their use of experience replay, an off-policy technique. It breaks the temporal correlations in experience and reuses data efficiently, which is critical when data collection is expensive."

  * **Eligibility Traces and TD(λ)**

      * **Bias–variance continuum:** $\lambda \in [0,1]$ bridges TD(0) ($\lambda=0$) and MC ($\lambda=1$).
      * **One-step with function approximation:**
        
        $\delta_t = R_{t+1} + \gamma\, V_{\mathbf{w}}(S_{t+1}) - V_{\mathbf{w}}(S_t)$
        
        $\mathbf{e}_t = \gamma\lambda\, \mathbf{e}_{t-1} + \nabla_{\mathbf{w}} V_{\mathbf{w}}(S_t)$
        
        $\mathbf{w} \leftarrow \mathbf{w} + \alpha\, \delta_t\, \mathbf{e}_t$

  * **Off-Policy Evaluation (Importance Sampling)**

      * **Trajectory ratio:** $\rho_{t:T-1} = \prod_{k=t}^{T-1} \frac{\pi(A_k\mid S_k)}{\mu(A_k\mid S_k)}$.
      * **MC update:** $V(S_t) \leftarrow V(S_t) + \alpha\, [\rho_{t:T-1}\, G_t - V(S_t)]$.
      * **TD(0) update:** $V(S_t) \leftarrow V(S_t) + \alpha\, \rho_t\, [R_{t+1} + \gamma V(S_{t+1}) - V(S_t)]$.

  * **The Deadly Triad**

      * Off-policy + bootstrapping + function approximation $\Rightarrow$ possible divergence.
      * **Mitigations:** target networks, double estimators (e.g., Double Q-learning), gradient TD (e.g., GTD2/TDC), conservative/regularized updates, or on-policy learning.

  * **Convergence (Tabular Cheat-Sheet)**

      * **DP:** Guaranteed convergence by contraction (finite MDPs).
      * **MC (prediction/control):** Converges under GLIE/exploring starts assumptions.
      * **TD(0) on-policy:** Converges to $v_\pi$ with suitable step-sizes.
      * **Off-policy TD:** May diverge; needs special care (e.g., gradient TD).

-----

### **Part 4: The Modern View — Connecting to RLHF for LLMs**

Show you can bridge foundational knowledge with the state-of-the-art. The main algorithm is **PPO**.

  * **Problem:** Aligning LLMs. This is a **control** problem in a massive state-action space.
  * **PPO Classified using the Framework:**
      * **Goal:** 🕹️ **Control**.
      * **Knowledge:** 🧭 **Model-Free**. The dynamics of language are unknown.
      * **Method:** 🎭 **Actor-Critic**. An LLM *is* the actor ($\pi_{\boldsymbol{\theta}}$). A reward model acts as a learned component of the environment, and a value function acts as the critic ($\hat{v}_{\mathbf{w}}$).
      * **Process:** 👣 **On-Policy**. It collects data from the current LLM policy to make a small, stable update.

  * **PPO Essentials (Math You Can State Precisely)**

      * **Clipped objective:** with $r_t(\theta) = \frac{\pi_\theta(a_t\mid s_t)}{\pi_{\theta_\text{old}}(a_t\mid s_t)}$ and advantage $\hat{A}_t$, [MUST MEMORIZE]
        
        $\mathcal{L}^{\text{CLIP}}(\theta) = \mathbb{E}_t\big[ \min\big( r_t(\theta)\, \hat{A}_t,\ \operatorname{clip}(r_t(\theta), 1-\epsilon, 1+\epsilon)\, \hat{A}_t \big) \big]$. [MUST MEMORIZE]
      * **Value loss + entropy bonus:** $\mathcal{L}^V(\mathbf{w}) = \mathbb{E}_t[(V_{\mathbf{w}}(s_t) - V^{\text{targ}}_t)^2]$, add $\beta\, \mathbb{E}_t[\mathcal{H}[\pi_\theta(\cdot\mid s_t)]]$.  
      * **GAE advantage:** with $\delta_t = r_{t+1} + \gamma V_{\mathbf{w}}(S_{t+1}) - V_{\mathbf{w}}(S_t)$, [MUST MEMORIZE]
        
        $\hat{A}^{\text{GAE}(\lambda)}_t = \sum_{\ell=0}^{\infty} (\gamma\lambda)^{\ell}\, \delta_{t+\ell}$. [MUST MEMORIZE]
      * **Practical notes:** multiple epochs of minibatch updates on the same on-policy batch; optional KL penalty variant; normalize $\hat{A}_t$.

#### **RLHF for LLMs: From First Principles (PPO + KL Control)**

  * **Problem as an MDP**

      * State $s_t$: the prompt plus generated prefix tokens. Action $a_t$: next token. Episode ends at EOS/length cap.
      * Rewards are often sequence-level via a learned reward model $r_\phi(x, y)$ trained from human preferences.

  * **Preference modeling (Reward Model)**

      * Train $r_\phi$ with pairwise preferences $(y^+, y^-)\,$ for prompt $x$ using Bradley–Terry:
        
        $\Pr(y^+ \succ y^- \mid x) = \sigma\!\Big( \sum_t r_\phi(s^{+}_t, a^{+}_t) - \sum_t r_\phi(s^{-}_t, a^{-}_t) \Big)$.

  * **KL‑regularized RL objective (keep policy near a reference LM)**

      * Reference policy $\pi_{\text{ref}}$ is typically the SFT model. Optimize
        
        $J(\theta) = \mathbb{E}_{x\sim\mathcal{D},\ y\sim \pi_\theta(\cdot\mid x)}[\ r_\phi(x,y)\ ]\; -\; \beta\, \mathbb{E}_{t}[\operatorname{KL}(\pi_\theta(\cdot\mid s_t)\,\|\,\pi_{\text{ref}}(\cdot\mid s_t))]$.

      * Reward‑shaping view (token level):
        
        $r'_t\;=\; r_t\; -\; \beta\, \log \frac{\pi_\theta(a_t\mid s_t)}{\pi_{\text{ref}}(a_t\mid s_t)}$. [MUST MEMORIZE]

  * **Credit assignment (token‑level) with GAE**

      * TD error with shaped reward: $\delta_t = r'_t + \gamma V_{\mathbf{w}}(s_{t+1}) - V_{\mathbf{w}}(s_t)$.
      * Advantage: $\hat{A}_t = \sum_{\ell\ge 0} (\gamma\lambda)^{\ell} \, \delta_{t+\ell}$.

  * **PPO update for the policy**

      * Importance ratio: $r_t(\theta) = \frac{\pi_\theta(a_t\mid s_t)}{\pi_{\theta_{\text{old}}}(a_t\mid s_t)}$.
      * Surrogate: $\mathcal{L}^{\text{CLIP}}(\theta) = \mathbb{E}[\min(r_t\hat{A}_t,\ \operatorname{clip}(r_t,1-\epsilon,1+\epsilon)\hat{A}_t)]$.
      * Optionally include an explicit KL penalty or implement it via the shaped reward $r'_t$.
      * Critic loss: $\mathcal{L}^V(\mathbf{w}) = \mathbb{E}[(V_{\mathbf{w}}(s_t) - V^{\text{targ}}_t)^2]$.

  * **Practical RLHF loop**

      1. SFT to obtain $\pi_{\text{ref}}$.
      2. Train reward model $r_\phi$ from human preference data.
      3. For prompts $x$, sample $y\sim \pi_\theta(\cdot\mid x)$; score with $r_\phi(x,y)$.
      4. Compute per‑token shaped rewards $r'_t$ using KL to $\pi_{\text{ref}}$; estimate $\hat{A}_t$ with GAE.
      5. Update $\theta$ with PPO (K epochs over minibatches); update $V$; adapt $\beta$ to hit a KL target.
      6. Controls: normalize $\hat{A}_t$, clip values, length penalty/normalization, early stop if KL drifts.

  * **Notes**

      * On‑policy is preferred for stability and accurate credit assignment in the non‑stationary, long‑horizon language setting.
      * Sequence‑level reward densified via token‑level shaping and value bootstrapping enables effective learning.
      * Alternatives: TRPO (theoretical trust region), A2C (simpler actor–critic); non‑RL preference optimizers (e.g., DPO) are related but distinct.

-----

### **Part 5: DeepMind Addendum — Must‑Know Extras**

  * **Policy Gradient Theorem (with Baseline Invariance)**

      * Objective (episodic): $J(\theta) = \mathbb{E}_{\pi_\theta}[G_0]$. The theorem:
        
        $\nabla_\theta J(\theta) = \mathbb{E}_{s\sim d^{\pi_\theta},\ a\sim \pi_\theta}[\nabla_\theta \log \pi_\theta(a\mid s)\, Q^{\pi_\theta}(s,a)]$. [MUST MEMORIZE]
        
      * Baseline invariance: for any $b(s)$, $\mathbb{E}[\nabla_\theta \log \pi_\theta(A\mid S)\, b(S)] = 0$; use $A(s,a) = Q(s,a) - b(s)$ (common: $b(s)=V_{\mathbf{w}}(s)$).

  * **DQN Essentials (Value-Based Deep RL)**

      * TD target: $y = r + \gamma\, (1-\text{done})\, \max_{a'} Q_{\theta^-}(s', a')$.
      * Loss: $\mathcal{L}(\theta) = \mathbb{E}[(y - Q_\theta(s,a))^2]$.
      * Stabilization: target network $\theta^-$ (hard copy every $C$ steps or soft: $\theta^- \leftarrow \tau\,\theta + (1-\tau)\,\theta^-$), experience replay (i.i.d. minibatches), $\epsilon$-greedy.
      * Double DQN target: $y = r + \gamma\, (1-\text{done})\, Q_{\theta^-}\big(s',\ \underset{a}{\arg\max}\ Q_\theta(s', a)\big)$. [MUST MEMORIZE]

  * **Exploration Cheatsheet**

      * $\epsilon$-greedy: with prob. $\epsilon$ take a random action; otherwise greedy.
      * Softmax/Boltzmann: $\pi(a\mid s) \propto \exp(Q(s,a)/\tau)$; smaller $\tau$ $\Rightarrow$ more greedy.
      * Entropy regularization: add $\beta\, \mathbb{E}[\mathcal{H}[\pi(\cdot\mid s)]]$ to encourage exploration.
      * Optimistic initialization: initialize $Q$ high to induce early exploration (decays under bootstrapping).
      * UCB (bandits): pick $a = \arg\max_a\big[\hat{Q}(a) + c\, \sqrt{\tfrac{\ln N}{N(a)}}\big]$; in MDPs, relate to bonus-based/count-based exploration.

**Final Interview Tip:** Structure your algorithm comparisons around the trade-offs defined by these mental models. When asked "Compare SARSA and Q-learning," you can now give a multi-layered answer: "At a high level, both are model-free, value-based, TD control algorithms that bootstrap. The critical difference lies in the on-policy vs. off-policy distinction. SARSA is on-policy... which makes it more conservative... In contrast, Q-learning is off-policy... which allows for techniques like experience replay but can be less stable..." This demonstrates a deep, structured understanding.