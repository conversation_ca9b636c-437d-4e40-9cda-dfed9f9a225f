"""
Hash Maps and Sets - Essential Patterns for Coding Interviews

This module covers the 9 most important hash-based data structure patterns
frequently tested in coding interviews at top tech companies.

Key Patterns Covered:
1. Frequency Counting (Top-K)
2. Two Sum Variations
3. Longest/Shortest Sequence Problems
4. Anagram Detection and Grouping
5. Set Operations

Time/Space Complexity Analysis and Use Cases included for each pattern.
"""

import heapq
from typing import List
from collections import Counter, defaultdict


class HashMapSetPatterns:
    """Collection of essential hash map and set algorithms for coding interviews."""

    # ========== FREQUENCY COUNTING ==========
    # Q1
    def top_k_frequent_elements(self, nums: List[int], k: int) -> List[int]:
        """
        PROBLEM: Top K Frequent Elements (Medium)

        Given an integer array nums and an integer k, return the k most frequent elements.
        You may return the answer in any order.

        Example 1:
        Input: nums = [1,1,1,2,2,3], k = 2
        Output: [1,2]
        Explanation: 1 appears 3 times, 2 appears 2 times

        Example 2:
        Input: nums = [1], k = 1
        Output: [1]

        Constraints:
        - 1 <= nums.length <= 10^5
        - -10^4 <= nums[i] <= 10^4
        - k is in the range [1, number of unique elements]
        - It's guaranteed that the answer is unique

        Time Complexity: O(n log k) using heap
        Space Complexity: O(n)
        """

        freq_map = Counter(nums)
        heap = []

        for num, freq in freq_map.items():
            heapq.heappush(heap, (freq, num))
            if len(heap) > k:
                heapq.heappop(heap)

        return [x[-1] for x in heap]

    # Q2
    def first_unique_character(self, s: str) -> int:
        """
        PROBLEM: First Unique Character in a String (Easy)

        Given a string s, find the first non-repeating character in it and return its index.
        If it does not exist, return -1.

        Example 1:
        Input: s = "leetcode"
        Output: 0
        Explanation: The first non-repeating character is 'l' at index 0

        Example 2:
        Input: s = "loveleetcode"
        Output: 2
        Explanation: The first non-repeating character is 'v' at index 2

        Example 3:
        Input: s = "aabb"
        Output: -1
        Explanation: All characters repeat

        Constraints:
        - 1 <= s.length <= 10^5
        - s consists of only lowercase English letters

        Time Complexity: O(n)
        Space Complexity: O(1) - bounded by alphabet size (26 letters)
        """

        counts = {}

        for char in s:
            counts[char] = counts.get(char, 0) + 1

        for i, char in enumerate(s):
            if counts[char] == 1:
                return i

        return -1

    # ========== SUBARRAY/SUBSTRING PROBLEMS ==========
    # Q3
    def two_sum(self, nums: List[int], target: int) -> List[int]:
        """
        PROBLEM: Two Sum (Easy)

        Given an array of integers nums and an integer target, return indices of the
        two numbers such that they add up to target.

        You may assume that each input would have exactly one solution, and you may
        not use the same element twice.

        Example 1:
        Input: nums = [2,7,11,15], target = 9
        Output: [0,1]
        Explanation: Because nums[0] + nums[1] = 2 + 7 = 9

        Example 2:
        Input: nums = [3,2,4], target = 6
        Output: [1,2]

        Example 3:
        Input: nums = [3,3], target = 6
        Output: [0,1]

        Constraints:
        - 2 <= nums.length <= 10^4
        - -10^9 <= nums[i] <= 10^9
        - -10^9 <= target <= 10^9
        - Only one valid answer exists

        Time Complexity: O(n)
        Space Complexity: O(n)
        """

        num_map = {}

        for i, num in enumerate(nums):
            complement = target - num
            if complement in num_map:
                return [num_map[complement], i]
            num_map[num] = i

        return []

    # Q4
    def longest_consecutive_sequence(self, nums: List[int]) -> int:
        """
        PROBLEM: Longest Consecutive Sequence (Medium)

        Given an unsorted array of integers nums, return the length of the longest
        consecutive elements sequence.

        You must write an algorithm that runs in O(n) time.

        Example 1:
        Input: nums = [100,4,200,1,3,2]
        Output: 4
        Explanation: The longest consecutive elements sequence is [1,2,3,4]. Therefore its length is 4.

        Example 2:
        Input: nums = [0,3,7,2,5,8,4,6,0,1]
        Output: 9
        Explanation: The longest consecutive sequence is [0,1,2,3,4,5,6,7,8]

        Example 3:
        Input: nums = []
        Output: 0

        Constraints:
        - 0 <= nums.length <= 10^5
        - -10^9 <= nums[i] <= 10^9

        Time Complexity: O(n)
        Space Complexity: O(n)

        """

        if not nums:
            return 0

        num_set = set(nums)

        max_len = 0

        for num in num_set:
            if num - 1 not in num_set:
                current_num = num
                current_len = 1  # Count the starting number

                while current_num + 1 in num_set:
                    current_num += 1
                    current_len += 1

                max_len = max(max_len, current_len)

        return max_len

    # ========== ANAGRAM DETECTION AND GROUPING ==========
    # Q5
    def is_anagram(self, s: str, t: str) -> bool:
        """
        PROBLEM: Valid Anagram (Easy)

        Given two strings s and t, return true if t is an anagram of s, and false otherwise.

        An Anagram is a word or phrase formed by rearranging the letters of a different
        word or phrase, typically using all the original letters exactly once.

        Example 1:
        Input: s = "anagram", t = "nagaram"
        Output: true

        Example 2:
        Input: s = "rat", t = "car"
        Output: false

        Constraints:
        - 1 <= s.length, t.length <= 5 * 10^4
        - s and t consist of lowercase English letters

        Time Complexity: O(n)
        Space Complexity: O(1) - bounded by alphabet size (26 letters)
        """

        # --- Solution (commented out) ---
        if len(s) != len(t):
            return False

        return Counter(s) == Counter(t)

    # Q6
    def group_anagrams(self, strs: List[str]) -> List[List[str]]:
        """
        PROBLEM: Group Anagrams (Medium)

        Given an array of strings strs, group the anagrams together.
        You can return the answer in any order.

        An Anagram is a word or phrase formed by rearranging the letters of a different
        word or phrase, typically using all the original letters exactly once.

        Example 1:
        Input: strs = ["eat","tea","tan","ate","nat","bat"]
        Output: [["bat"],["nat","tan"],["ate","eat","tea"]]

        Example 2:
        Input: strs = [""]
        Output: [[""]]

        Example 3:
        Input: strs = ["a"]
        Output: [["a"]]

        Constraints:
        - 1 <= strs.length <= 10^4
        - 0 <= strs[i].length <= 100
        - strs[i] consists of lowercase English letters

        Time Complexity: O(n * m log m) where n is number of strings, m is avg string length
        Space Complexity: O(n * m)
        """

        # --- Solution (commented out) ---
        anagram_groups = defaultdict(list)

        for s in strs:
            # Use sorted string as key
            key = "".join(sorted(s))
            anagram_groups[key].append(s)

        return list(anagram_groups.values())

    # Q7
    def find_anagrams(self, s: str, p: str) -> List[int]:
        """
        PROBLEM: Find All Anagrams in a String (Medium)

        Given two strings s and p, return an array of all the start indices of p's
        anagrams in s. You may return the answer in any order.

        An Anagram is a word or phrase formed by rearranging the letters of a different
        word or phrase, typically using all the original letters exactly once.

        Example 1:
        Input: s = "abab", p = "ab"
        Output: [0,2]
        Explanation: The substring with start index 0 is "ab", which is an anagram of "ab".
                    The substring with start index 2 is "ab", which is an anagram of "ab".

        Example 2:
        Input: s = "ababbaba", p = "abab"
        Output: [0,2,4]
        Explanation: The substring with start index 0 is "abab", which is an anagram of "abab".
                    The substring with start index 2 is "abba", which is an anagram of "abab".
                    The substring with start index 4 is "baba", which is an anagram of "abab".

        Constraints:
        - 1 <= s.length, p.length <= 3 * 10^4
        - s and p consist of lowercase English letters

        Hints:
        - Use sliding window technique with character frequency counting
        - Create frequency map for pattern p first
        - Maintain a sliding window of size len(p) over string s
        - For efficiency: when sliding the window, remove leftmost char and add rightmost char
        - Two frequency maps are equal when they have same char counts
        - Alternative: track number of matching characters instead of comparing entire maps
        - Key insight: only need to check windows of exactly len(p) size

        Time Complexity: O((n-k)*k) where n=len(s), k=len(p) due to counter comparisons
        Space Complexity: O(1) - bounded by alphabet size (26 letters)

        NOTE: This can be optimized to O(n) by tracking count of matched characters
        instead of comparing entire frequency maps on each iteration.
        """

        if len(p) > len(s):
            return []

        result = []
        p_count = Counter(p)
        window_count = Counter()

        # Initial window
        for i in range(len(p)):
            window_count[s[i]] += 1

        if window_count == p_count:
            result.append(0)

        # Slide window
        for i in range(len(p), len(s)):
            # Add new character
            window_count[s[i]] += 1

            # Remove old character
            left_char = s[i - len(p)]
            window_count[left_char] -= 1
            if window_count[left_char] == 0:
                del window_count[left_char]

            # Check if current window is anagram
            if window_count == p_count:
                result.append(i - len(p) + 1)

        return result

    # ========== SET OPERATIONS ==========
    # Q8
    def intersection_of_arrays(self, nums1: List[int], nums2: List[int]) -> List[int]:
        """
        PROBLEM: Intersection of Two Arrays (Easy)

        Given two integer arrays nums1 and nums2, return an array of their intersection.
        Each element in the result must be unique and you may return the result in any order.

        Example 1:
        Input: nums1 = [1,2,2,1], nums2 = [2,2]
        Output: [2]

        Example 2:
        Input: nums1 = [4,9,5], nums2 = [9,4,9,8,4]
        Output: [9,4]
        Explanation: [4,9] is also accepted.

        Constraints:
        - 1 <= nums1.length, nums2.length <= 1000
        - 0 <= nums1[i], nums2[i] <= 1000

        Time Complexity: O(n + m)
        Space Complexity: O(min(n, m))
        """

        set1, set2 = set(nums1), set(nums2)
        return list(set1 & set2)

    # Q9
    def intersection_of_arrays_ii(
        self, nums1: List[int], nums2: List[int]
    ) -> List[int]:
        """
        PROBLEM: Intersection of Two Arrays II (Easy)

        Given two integer arrays nums1 and nums2, return an array of their intersection.
        Each element in the result should appear as many times as it shows in both arrays
        and you may return the result in any order.

        Example 1:
        Input: nums1 = [1,2,2,1], nums2 = [2,2]
        Output: [2,2]

        Example 2:
        Input: nums1 = [4,9,5], nums2 = [9,4,9,8,4]
        Output: [4,9]
        Explanation: [9,4] is also accepted.

        Constraints:
        - 1 <= nums1.length, nums2.length <= 1000
        - 0 <= nums1[i], nums2[i] <= 1000

        Follow up:
        - What if the given array is already sorted? How would you optimize your algorithm?
        - What if nums1's size is small compared to nums2's size? Which algorithm is better?
        - What if elements of nums2 are stored on disk, and the memory is limited?

        Time Complexity: O(n + m)
        Space Complexity: O(min(n, m))
        """

        counter1 = Counter(nums1)
        result = []

        for num in nums2:
            if counter1[num] > 0:
                counter1[num] -= 1
                result.append(num)

        return result


def demonstrate_patterns():
    """Demonstrate usage of hash map and set patterns."""
    patterns = HashMapSetPatterns()

    print("=== HASH MAPS AND SETS DEMONSTRATIONS ===\n")

    # Top K Frequent
    print("1. Top K Frequent Elements:")
    nums = [1, 1, 1, 3, 3, 2]
    result = patterns.top_k_frequent_elements(nums, 2)
    print(f"   Array: {nums} -> Top 2 frequent: {result}")

    # Two Sum
    print("\n2. Two Sum pattern:")
    nums = [2, 7, 11, 15]
    target = 9
    result = patterns.two_sum(nums, target)
    print(f"   Array: {nums}, Target: {target} -> Indices: {result}")

    # Longest consecutive sequence
    print("\n3. Longest Consecutive Sequence:")
    nums = [100, 4, 200, 1, 3, 2]
    length = patterns.longest_consecutive_sequence(nums)
    print(f"   Array: {nums} -> Length: {length}")

    # Group anagrams
    print("\n4. Group Anagrams:")
    strs = ["eat", "tea", "tan", "ate", "nat", "bat"]
    groups = patterns.group_anagrams(strs)
    print(f"   Strings: {strs}")
    print(f"   Groups: {groups}")

    # Find anagrams
    print("\n5. Find All Anagrams:")
    s, p = "abab", "ab"
    indices = patterns.find_anagrams(s, p)
    print(f"   String: '{s}', Pattern: '{p}' -> Indices: {indices}")


if __name__ == "__main__":
    demonstrate_patterns()
