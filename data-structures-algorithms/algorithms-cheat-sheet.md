## Coding Interview Mental Reminders (DeepMind Research Engineer)

### 1. Workflow
* Clarify: restate the problem, inputs/outputs, constraints, edge cases. Confirm big-O targets.
* Small examples: hand-run 2–3 cases (including edge cases) to surface invariants.
* Baseline first: propose a clear brute-force. Identify the bottleneck.
* Pattern match: map to a known technique (two pointers, sliding window, BFS/DFS, DP, heap, union-find, etc.).
* Prove/argue correctness: loop invariant, exchange argument, or optimal substructure.
* Analyze complexity: time, space, worst/average; note constants and data structure costs.
* Implement cleanly: small helpers, descriptive names, tight loops, avoid premature micro-opts.
* Validate: test normal, edge, and adversarial inputs; reason about failure modes.

### 2. Quick Review
#### Strings & Arrays
- Two pointers: sorted/order leverage; move ends to maintain an invariant.
- Sliding window: maintain counts/stats; expand/contract until constraints hold.
- Prefix sums: compute cumulative; answer = difference of two prefix values.
- Binary search: on index or on answer (monotone feasibility predicate).

#### Hashmaps & Sets
- Use `collections.Counter` for frequencies (anagrams, windows).
- Compare counters: equality; or subtract and check emptiness for windows.
- Set ops: intersection/union/difference for membership/overlap logic.

#### Linked Lists
- Fast/slow pointers: cycle detect/start; find middle.
- k-th from end: two pointers with k-gap.
- Reverse / reverse-between: `prev`/`current`, reverse one node at a time; keep links safe.
- Merge two lists: pointer rewiring; prefer dummy head.
- Remove duplicates (sorted): keep one with single pointer; remove all dups with dummy + skip block.

#### Stacks & Queues
- Monotonic stack: nearest greater/smaller; histogram area; trapping rain water.
- Monotonic deque: sliding window maximum (store indices, decreasing values).
- Expression parsing: Reverse Polish Notation; basic calculator; decode strings.

#### Heaps (Priority Queues)
- Min-heap default; max-heap via negating values.
- Top‑k: k largest → min-heap of size k; k smallest → max-heap via negation.

#### Greedy
- Assign cookies; interval scheduling (earliest finish); justify with exchange argument.

#### Recursion / Backtracking
- Maintain `path`; choose → recurse → unchoose. Include/skip structure.
- Prune early (bounds/feasibility). Sort inputs to enable pruning/dedup.
- Example: Combination Sum (include same index for reuse; skip by advancing index).

#### Graphs: BFS vs DFS
- BFS (queue): unweighted shortest path, fewest steps, level order.
- DFS (recursion/stack): full exploration, cycles/topo, backtracking paths.
- Quick chooser: fewest steps → BFS; enumerate/search with constraints → DFS.

#### General Tips
- Distinguish index vs value when reading arrays/maps.
- Track “best” with an external variable if searching for optimum; prune when dominated.
- For tree BFS, use level size to process layer by layer.
- If recursion depth is risky, switch to an explicit stack/queue.

### 3. Reference

#### Core patterns
* Two pointers
  * When: sorted/ordered data; shrink/grow from ends; pair operations.
  * Mini-template:
```python
l, r = 0, len(a) - 1
while l < r:
    if should_move_left(a, l, r):
        l += 1
    else:
        r -= 1
```
* Sliding window (variable)
  * When: longest/shortest subarray with property; at-most-K distinct.
  * Mini-template:
```python
from collections import Counter
need = Counter(); have = Counter(); left = 0
for right, x in enumerate(a):
    have[x] += 1
    while violates(have, need):
        have[a[left]] -= 1
        left += 1
    # window [left, right] is valid
```
* Monotonic stack / deque
  * When: next greater/smaller; histogram; sliding window extrema.
  * Next greater element:
```python
stack = []  # store indices with decreasing values
for i, x in enumerate(a):
    while stack and a[stack[-1]] < x:
        j = stack.pop()
        next_greater[j] = i
    stack.append(i)
```
  * Sliding window max (deque):
```python
from collections import deque
dq = deque()
for i, x in enumerate(a):
    while dq and a[dq[-1]] <= x:
        dq.pop()
    dq.append(i)
    if dq[0] <= i - k:
        dq.popleft()
    if i >= k - 1:
        ans.append(a[dq[0]])
```
* Prefix sums / differences
  * When: range sums; subarray problems; constant-time range queries.
  * Mini-template:
```python
pref = [0]
for x in a:
    pref.append(pref[-1] + x)
# sum of a[i:j] -> pref[j] - pref[i]
```
* Binary search (on answer)
  * When: monotone predicate over answers.
  * Mini-template:
```python
lo, hi = min_ans, max_ans
while lo < hi:
    mid = (lo + hi) // 2
    if feasible(mid):
        hi = mid
    else:
        lo = mid + 1
# lo is minimal feasible
```
* BFS (unweighted shortest path)
  * When: fewest steps / levels.
```python
from collections import deque
q = deque([start]); dist = {start: 0}
while q:
    u = q.popleft()
    for v in nbrs(u):
        if v not in dist:
            dist[v] = dist[u] + 1
            q.append(v)
```
* DFS / Backtracking
  * When: enumerate possibilities with constraints; search with pruning.
```python
path = []
def bt(i):
    if done(i, path):
        ans.append(path[:]); return
    for choice in choices(i, path):
        if invalid(choice):
            continue
        apply(choice, path)
        bt(i + 1)
        undo(choice, path)
```
* Union–Find (DSU)
  * When: dynamic connectivity; Kruskal; cycle detection.
```python
parent = list(range(n)); size = [1]*n
def find(x):
    while x != parent[x]:
        parent[x] = parent[parent[x]]
        x = parent[x]
    return x
def union(a, b):
    ra, rb = find(a), find(b)
    if ra == rb:
        return False
    if size[ra] < size[rb]:
        ra, rb = rb, ra
    parent[rb] = ra; size[ra] += size[rb]
    return True
```
* Topological sort (Kahn’s)
  * When: DAG order; dependency resolution.
```python
from collections import deque
q = deque([u for u in V if indeg[u] == 0])
order = []
while q:
    u = q.popleft(); order.append(u)
    for v in out[u]:
        indeg[v] -= 1
        if indeg[v] == 0:
            q.append(v)
```
* Dijkstra / 0-1 BFS
  * When: non-negative weights (Dijkstra); edges in {0,1} (0-1 BFS).
```python
import heapq
dist = {s: 0}; pq = [(0, s)]
while pq:
    d, u = heapq.heappop(pq)
    if d != dist.get(u, float('inf')):
        continue
    for v, w in nbrs(u):
        nd = d + w
        if nd < dist.get(v, float('inf')):
            dist[v] = nd
            heapq.heappush(pq, (nd, v))
```
* Intervals: sort + sweep / merge
  * When: overlaps, rooms, coverage.
```python
events = []
for s, e in intervals:
    events.append((s, 1)); events.append((e, -1))
events.sort()
cur = best = 0
for _, d in events:
    cur += d; best = max(best, cur)
```
* Dynamic programming (tabulation)
  * Steps: state, base, transition, order, space-opt.
```python
dp = [base_value] * (N + 1)
dp[0] = base
for i in range(1, N + 1):
    dp[i] = combine(dp[i-1], ...)
```



#### Arrays & strings
* At-a-glance
  * When: contiguous subarray targets; pair operations; frequency/anagram/grouping; ordering exploitation.
  * Core patterns: two pointers, sliding window, prefix sums, hashing, sorting+sweep, binary search on answer.
  * Pitfalls: negative numbers with sliding windows; off-by-one on ranges; duplicates handling.
* Two pointers
  * When: sorted arrays, partitioning, pair-sum, deduplication, in-place reverse/merge.
  * Trigger: "Can I advance left/right based on an order?"
  * Mini-template:
```python
l, r = 0, len(a) - 1
while l < r:
    # advance l or r based on condition
    # maintain invariant
```
* Sliding window (fixed/variable)
  * When: longest/shortest subarray with property, at-most-K distinct, sums/counts.
  * Trigger: "Maintain window summary; expand/contract until constraint holds."
  * Mini-template:
```python
have = collections.Counter(); left = 0
for right, x in enumerate(a):
    have[x] += 1
    while constraint_violated(have):
        have[a[left]] -= 1
        left += 1
    # window [left, right] is valid
```
* Prefix sums / differences
  * Ask: "Can I express the answer as difference of cumulative values?"
  * 1D/2D prefix sums; difference arrays for range updates.
* Binary search (index or answer)
  * When: monotone predicate over index/answer.
  * Mini-template (answer):
```python
lo, hi = min_ans, max_ans
while lo < hi:
    mid = (lo + hi) // 2
    if feasible(mid):
        hi = mid
    else:
        lo = mid + 1
# lo is minimal feasible
```
* Hash maps/sets
  * Fast membership, counting, first/last seen index, frequency maps for anagrams, two-sum variants.
* Sorting + sweep
  * When ordering simplifies decisions; combine with two pointers or event sweeps.

#### Intervals
* At-a-glance
  * When: overlaps, merging, meeting rooms, coverage, choosing non-overlapping sets.
  * Core patterns: sort by start/end, sweep line (+1/-1 events), two-pointer merge.
  * Pitfalls: tie-breaking on equal endpoints; open vs closed intervals; inclusive boundaries.
* Normalize by sorting (start, then end).
* Merge intervals; detect overlaps; count rooms via sweep line with (+1/-1) events.
* Two-pointer merge for two interval lists.

#### Linked lists
* At-a-glance
  * When: reordering, cycle detection, K-way merge, in-place mutations.
  * Core patterns: fast/slow pointers, in-place reverse, heap for merging.
  * Pitfalls: pointer re-link order; lost references; dummy head usage.
* Fast/slow pointers: middle, cycle detect (Floyd), cycle start.
* In-place reverse; merge k lists (heap), reorder list.
* Prefer O(1) space; mind pointer re-link order.

#### Stacks & queues
* At-a-glance
  * When: next greater/smaller, span problems, streaming window extrema, validation.
  * Core patterns: monotonic stack/deque, parentheses stack, deque for sliding max.
  * Pitfalls: handling equals consistently; expiring indices in deque.
* Monotonic stack: next greater/smaller element, histogram, daily temperatures.
* Parentheses/validation: push indices/chars; pop on match; track best spans.
* Queue/deque: sliding window max (monotonic deque), BFS.

#### Trees & BST
* At-a-glance
  * When: hierarchical traversals, ancestor queries, BST-specific order logic.
  * Core patterns: DFS orders, BFS level-order, BST bounds, LCA strategies.
  * Pitfalls: recursion depth; mutable nodes; incorrect bounds for BST.
* Traversals: preorder/inorder/postorder; level-order via BFS.
* BST invariants: inorder is sorted; use bounds in recursion; LCA via binary search on values.
* Depth/diameter: return (height, best) from postorder.
* Serialization or iterative traversals if recursion depth is risky.

#### Graphs
* At-a-glance
  * When: connectivity, shortest paths, dependencies, reachability.
  * Core patterns: BFS/DFS, topological sort, Dijkstra/0-1 BFS/Bellman-Ford, DSU, bidirectional BFS.
  * Pitfalls: visited/state encoding; negative edges with Dijkstra; disconnected components.
* BFS (queue, layer by layer)
  * When: unweighted shortest path, minimum steps, level-order processing.
  * Trigger: "First pop yields fewest edges to reach it."
  * Mini-template:
```python
from collections import deque
q = deque([start]); dist = {start: 0}
while q:
    u = q.popleft()
    for v in nbrs(u):
        if v not in dist:
            dist[v] = dist[u] + 1
            q.append(v)
```
* DFS (stack/recursion, dive deep)
  * When: full exploration, cycle detection, topological order, backtracking/paths, timestamps.
  * Trigger: "Dive until I can't, backtrack, record order/state."
  * Mini-template:
```python
seen = set()
def dfs(u):
    seen.add(u)
    for v in nbrs(u):
        if v not in seen:
            dfs(v)
```
* Topological sort: Kahn (indegree queue) or DFS postorder; detect cycles.
* Weighted shortest paths: Dijkstra (non-negative), Bellman-Ford (negatives), Floyd-Warshall (all-pairs, small n).
* Union-Find (DSU): connectivity, cycle detection, Kruskal MST.
* Bidirectional BFS for large, sparse, unweighted shortest paths.
* If recursion depth is unbounded or stack limits are tight, switch to an explicit stack/queue.

#### Heaps & priority queues
* At-a-glance
  * When: top-k/k-th, scheduling, streaming medians, merging sorted sources.
  * Core patterns: min-heap with negation for max, two heaps for median, lazy deletion.
  * Pitfalls: stale entries; comparator via key tuples.
* k-th or top-k, merging k sorted lists, running median (two heaps), scheduling by earliest finish.
* Use min-heap with negated values for max behavior.

#### Dynamic programming (DP)
* At-a-glance
  * When: overlapping subproblems and optimal substructure; counting/min/max under constraints.
  * Core patterns: tabulation, memoization, bitmask DP, interval DP, tree DP.
  * Pitfalls: iteration order mismatch; in-place overwrites; state explosion.
* Steps: define state, transition, base case, iteration order, space optimization.
* Patterns/families: knapsack, LIS/LCS/LCSubstring, edit distance, partition, grid paths, coin change.
* Bitmask DP for subsets; interval DP for ranges; tree DP with returns per subtree.
* Mini-template:
```python
# 1) identify state dp[i][j] (or 1D)
# 2) base cases
# 3) transitions
for i in range(...):
    for j in range(...):
        dp[i][j] = combine(dp[...], ...)
```

#### Recursion & backtracking
* At-a-glance
  * When: explore combinatorial search spaces with constraints; generate or decide with pruning.
  * Core patterns: choose → explore → unchoose; dedup via sorting/used[]; feasibility pruning.
  * Pitfalls: shared mutable state; recursion depth; forgetting to undo.
* Choose → explore → unchoose; pass partial solution; prune early.
* Deduplicate: sort then skip equal choices on the same depth; use used[] for permutations.
* Mini-template:
```python
path = []
def backtrack(i):
    if done_condition(i, path):
        ans.append(path[:]); return
    for choice in choices(i, path):
        if invalid(choice):
            continue
        apply(choice, path)
        backtrack(i + 1)
        undo(choice, path)
```

#### Backtracking pattern (playbook)
* When: enumerate valid combinations/permutations/selections under constraints; path search with pruning.
* Core loop: decide → recurse → undo. Keep state minimal and copy only when recording answers.
* Pruning: check feasibility early (bounds, remaining capacity, constraint violations); stop branches fast.
* Dedup strategies:
  * Sort inputs and skip equal choices at the same depth for combinations: `if j>i and a[j]==a[j-1]: continue`.
  * For permutations with duplicates: `if i>0 and a[i]==a[i-1] and not used[i-1]: continue`.
* State representation: `path` list, `used[]` boolean, or bitmask integer; prefer tuples for hash keys.
* Goal checks: add solution on reaching size/target; optionally short-circuit on first solution.
* Typical skeletons:
  * Subsets (handle duplicates):
```python
def subsets_with_dup(nums):
    nums.sort(); res, path = [], []
    def bt(i: int) -> None:
        res.append(path[:])
        for j in range(i, len(nums)):
            if j > i and nums[j] == nums[j-1]:
                continue
            path.append(nums[j])
            bt(j + 1)
            path.pop()
    bt(0)
    return res
```
  * Permutations (handle duplicates):
```python
def permute_unique(nums):
    nums.sort(); res, path, used = [], [], [False]*len(nums)
    def bt() -> None:
        if len(path) == len(nums):
            res.append(path[:]); return
        for i in range(len(nums)):
            if used[i]:
                continue
            if i > 0 and nums[i] == nums[i-1] and not used[i-1]:
                continue
            used[i] = True
            path.append(nums[i])
            bt()
            path.pop()
            used[i] = False
    bt(); return res
```
  * Combination sum (with pruning):
```python
def combination_sum(cands, target):
    cands.sort(); res, path = [], []
    def bt(i: int, remain: int) -> None:
        if remain == 0:
            res.append(path[:]); return
        for j in range(i, len(cands)):
            x = cands[j]
            if j > i and x == cands[j-1]:
                continue
            if x > remain:
                break
            path.append(x)
            bt(j, remain - x)  # allow reuse; use j+1 to disallow
            path.pop()
    bt(0, target); return res
```
  * N-Queens constraints (row by row):
```python
def solve_n_queens(n: int):
    cols, diag1, diag2 = set(), set(), set()  # col, r-c, r+c
    res, path = [], []
    def bt(r: int) -> None:
        if r == n:
            res.append([''.join(row) for row in path]); return
        row = ['.'] * n
        for c in range(n):
            if c in cols or (r-c) in diag1 or (r+c) in diag2:
                continue
            row[c] = 'Q'; path.append(row[:])
            cols.add(c); diag1.add(r-c); diag2.add(r+c)
            bt(r + 1)
            cols.remove(c); diag1.remove(r-c); diag2.remove(r+c)
            path.pop(); row[c] = '.'
    bt(0); return res
```
* Debug tips: print with indentation by depth; assert invariants; write small target cases to validate pruning.

#### Tries
* At-a-glance
  * When: prefix-heavy queries, dictionary replacements, autocomplete.
  * Core patterns: node maps and terminal flags; compressed tries when needed.
  * Pitfalls: memory growth; consider sort+bisect for static sets.
* Prefix queries, autocomplete, word replacement, word break accelerations.
* Mind memory; compress if needed; alternative: sort + binary search by prefix.

#### Matrices & grids
* At-a-glance
  * When: 2D traversal problems; multi-source diffusion; submatrix sums.
  * Core patterns: BFS/DFS with direction arrays; 2D prefix sums; difference matrices.
  * Pitfalls: bounds checks; visited marking; diagonals policy.
* Directions: `dirs = [(1,0),(-1,0),(0,1),(0,-1)]` (+ diagonals if allowed).
* Boundary checks; mark visited; multi-source BFS by seeding queue.
* 2D prefix sums for submatrix sums; difference matrix for range updates.

#### Bit manipulation
* At-a-glance
  * When: parity, bit counting, set operations, subset iteration, constant-time tricks.
  * Core patterns: mask arithmetic, lowbit, fast pow, XOR properties.
  * Pitfalls: signed vs unsigned in other languages; precedence.
* Basics: set/clear/test bits, masks, parity, `x & -x` lowbit.
* XOR properties: a^a=0, a^0=a; pair cancellation; single number tricks.
* Fast exponentiation; count bits; subset iteration: `s = t; s = (s-1) & t`.

#### Numerics, randomness, and streaming (DM-adjacent)
* At-a-glance
  * When: numerical stability, modular arithmetic, randomized/streaming algorithms.
  * Core patterns: epsilon comparisons, pow with mod, Fisher–Yates, reservoir sampling, two-heaps.
  * Pitfalls: catastrophic cancellation; RNG seeding; modulo inverses constraints.
* Numerics: avoid catastrophic cancellation; compare floats with epsilon; consider integer math where possible.
* Modular arithmetic: guard overflow; use pow(base, exp, mod).
* Randomization: Fisher–Yates shuffle; reservoir sampling for unknown-length streams; rejection sampling.
* Streaming: running median (two heaps); rolling hashes; sliding statistics in O(1) amortized.

#### Python-specific utilities & pitfalls
* Use `collections`: `deque` (O(1) pops from both ends), `Counter`, `defaultdict`.
* Use `heapq` for priority queues; `bisect` for binary search on lists.
* Avoid `list.pop(0)`; use `deque.popleft()`.
* Strings: build with list and `''.join(...)` to avoid O(n^2) concatenation.
* Sorting: prefer `key=` over custom comparators; stable sort helps for multi-key.
* Recursion: Python has limited recursion depth; consider iterative solutions or increase limit cautiously.
* Default arguments: avoid mutable defaults (e.g., `def f(x, acc=[])`); use `None` and initialize inside.
* Hashability: tuples are hashable and can be dict keys/ set elements; lists are not; prefer tuple keys for composite states.

#### Edge-case checklist
* Empty input, single element/node, all equal/unique, already sorted/reverse, duplicates.
* Negative numbers, zeros, large values, overflow risks, non-ASCII chars.
* Off-by-one at boundaries, inclusive/exclusive intervals, index vs value confusion.
* Graph edge cases: cycles, disconnected components, self-loops, multi-edges.
* Parameters: k=0, k=1, k>n; windows larger than array; empty strings.

#### Complexity & correctness
* Choose target: O(n), O(n log n), O(n·k), etc., justify data-structure costs.
* Argue correctness: loop invariants; greedy exchange argument; optimal substructure for DP.
* Space trade-offs: time vs memory; iterative vs recursive.

#### Communication & testing during interview
* Narrate intent: what invariant you're maintaining and why it proves correctness.
* Write small tests: normal + edge; dry-run code on them.
* If stuck: articulate subproblems, try simpler variants, or switch patterns.

#### Quick chooser (BFS vs DFS)
* Fewest steps / levels? → BFS.
* All possibilities / constraints / orderings? → DFS.
* Unweighted shortest path? → BFS.
* Topo sort / cycles / backtrack search (combos, trees)? → DFS.

#### Extra tips
* For tree BFS, get the size of each level and use a for-loop to process.
* When searching for the best solution during recursion or graph search, keep a global or external accumulator for the current best, with pruning.
* Distinguish indices from values when reading from arrays or maps.
