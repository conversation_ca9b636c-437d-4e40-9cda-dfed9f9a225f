"""
Hash Table - Essential Coding Interview Problems

Provides a basic separate-chaining hash table with linked lists plus
6 carefully selected hash table problems covering essential patterns.

Problem Difficulty Levels:
- Easy (2): Word Frequency Counter, Two Sum
- Medium (3): First Non-Repeating Character, Group Anagrams, Longest Substring
- Medium-Hard (1): Top K Frequent Elements

Each problem includes detailed descriptions, constraints, examples, and edge case tests.
"""

import heapq
from typing import Optional


class _HashNode:
    """Internal node storing a key-value pair for separate chaining."""

    def __init__(self, key, value, next_node=None):
        """Initialize a hash node.

        Args:
            key: The key for the hash table entry
            value: The value associated with the key
            next_node: Reference to the next node in the bucket chain
        """
        self.key = key
        self.value = value
        self.next = next_node


class HashTable:
    """Simple hash table using separate chaining.

    - Average operations are O(1) with a reasonable load factor.
    - Uses Python's built-in hash() and modulo for bucket indexing.
    """

    def __init__(self, capacity=16):
        """Initialize the table.

        Args:
            capacity: Initial number of buckets (will be clamped to >= 1)
        """
        self.capacity = max(1, capacity)
        self.size = 0
        self._buckets = [None] * self.capacity

    def __len__(self):
        """Return number of key-value pairs stored."""
        return self.size

    def _index(self, key):
        """Compute bucket index for a key.

        Args:
            key: Key to hash

        Returns:
            Index in the buckets array
        """
        return hash(key) % self.capacity

    def put(self, key, value):
        """Insert or update a key-value pair.

        Time: Amortized O(1)
        """
        index = self._index(key)
        head = self._buckets[index]

        # Check if key already exists and update
        node = head
        while node is not None:
            if node.key == key:
                node.value = value  # need to update if the value is different
                return
            node = node.next

        # Key doesn't exist, add new node at front of chain
        new_head = _HashNode(key, value, head)  # add at the beginning
        self._buckets[index] = new_head  # type: ignore
        self.size += 1  # remember to increase size

        # Optional: resize when load factor > 0.75
        if self.size > (self.capacity * 3) // 4:
            self._resize(self.capacity * 2)

    def get(self, key):
        """Retrieve the value for a key, or None if not present.

        Time: O(chain length) average O(1)
        """
        index = self._index(key)
        node = self._buckets[index]
        while node is not None:
            if node.key == key:
                return node.value
            node = node.next
        return None

    def remove(self, key):
        """Remove key from table if present.

        Returns:
            True if a key was removed, False otherwise
        """
        index = self._index(key)
        prev = None  # key to the solution
        node = self._buckets[index]

        while node is not None:
            if node.key == key:
                # Remove node from chain
                if prev is None:
                    # Removing head of chain
                    self._buckets[index] = node.next  # still at the head
                else:
                    # Removing middle/end of chain
                    prev.next = node.next
                self.size -= 1
                return True
            prev = node
            node = node.next
        return False

    def items(self):
        """Return a list of all (key, value) pairs in the table."""
        result = []
        for head in self._buckets:
            node = head
            while node is not None:
                result.append((node.key, node.value))
                node = node.next
        return result

    def keys(self):
        """Return a list of all keys in the table."""
        result = []
        for head in self._buckets:
            node = head
            while node is not None:
                result.append(node.key)
                node = node.next
        return result

    def values(self):
        """Return a list of all values in the table."""
        result = []
        for head in self._buckets:
            node = head
            while node is not None:
                result.append(node.value)
                node = node.next
        return result

    def _resize(self, new_capacity):
        """Resize and rehash all entries to a larger bucket array."""
        # Save old buckets
        old_buckets = self._buckets

        # Create new bucket array
        self.capacity = max(1, new_capacity)
        self._buckets = [None] * self.capacity
        self.size = 0

        # Rehash all existing entries, this is REQUIRED due to modulo operation!
        for head in old_buckets:
            node = head
            while node is not None:
                self.put(node.key, node.value)  # use put method for simplicity
                node = node.next


def demonstrate_hash_table():
    """Simple demonstration for the hash table API."""
    print("=== Hash Table Demonstration ===")

    # Create hash table
    table = HashTable(capacity=8)
    print(f"Created hash table with capacity: {table.capacity}")

    # Insert some items
    table.put("apple", 1)
    table.put("banana", 2)
    table.put("orange", 3)
    print(f"Added 3 items, size: {len(table)}")

    # Update existing item
    table.put("banana", 20)
    print(f"Updated banana value to: {table.get('banana')}")

    # Get items
    print(f"apple: {table.get('apple')}")
    print(f"banana: {table.get('banana')}")
    print(f"orange: {table.get('orange')}")
    print(f"grape: {table.get('grape')}")  # Should be None

    # Remove item
    removed = table.remove("banana")
    print(f"Removed banana: {removed}")
    print(f"banana after removal: {table.get('banana')}")  # Should be None

    # Show all items
    print(f"All items: {table.items()}")
    print(f"All keys: {table.keys()}")
    print(f"All values: {table.values()}")

    # Test collision by adding many items
    print("\n=== Testing Collisions ===")
    for i in range(10):
        table.put(f"key{i}", i * 10)

    print(f"After adding 10 more items, size: {len(table)}")
    print(f"Capacity after auto-resize: {table.capacity}")
    print(f"Load factor: {len(table) / table.capacity:.2f}")


class HashTableProblems:
    """Essential hash table algorithms for coding interviews."""

    # ========== EASY PROBLEMS ==========
    # Q1
    def word_frequency_counter(self, text: str) -> HashTable:
        """
        PROBLEM: Word Frequency Counter (Easy)

        Given a string of words, return a hash table containing the frequency
        of each word.

        Example 1:
        Input: "the quick brown fox jumps over the lazy dog the fox is quick"
        Output: HashTable with {"the": 3, "fox": 2, "quick": 2, ...}

        Example 2:
        Input: "hello world hello"
        Output: HashTable with {"hello": 2, "world": 1}

        Constraints:
        - 1 <= text.length <= 10^4
        - Text contains only lowercase letters and spaces
        - Words are separated by single spaces

        Time Complexity: O(n) where n is the length of text
        Space Complexity: O(k) where k is the number of unique words
        """
        words = text.split()
        word_count = HashTable()

        for word in words:
            current_freq = word_count.get(word)
            if current_freq is None:
                word_count.put(word, 1)
            else:
                word_count.put(word, current_freq + 1)

        return word_count

    # Q2
    def two_sum(self, nums: list, target: int) -> Optional[list]:
        """
        PROBLEM: Two Sum (Easy)

        Given an array of integers and a target sum, return indices of two numbers
        that add up to the target.

        Example 1:
        Input: nums = [2, 7, 11, 15], target = 9
        Output: [0, 1]
        Explanation: nums[0] + nums[1] = 2 + 7 = 9

        Example 2:
        Input: nums = [3, 2, 4], target = 6
        Output: [1, 2]

        Constraints:
        - 2 <= nums.length <= 10^4
        - -10^9 <= nums[i] <= 10^9
        - -10^9 <= target <= 10^9
        - Only one valid answer exists

        Time Complexity: O(n)
        Space Complexity: O(n)
        """

        seen = HashTable()
        for i, num in enumerate(nums):  # Fixed: use enumerate
            match = seen.get(target - num)
            if match is not None:  # Fixed: explicit None check
                return [match, i]
            else:
                seen.put(num, i)
        return None

    # ========== MEDIUM PROBLEMS ==========
    # Q3
    def first_unique_char(self, s: str) -> Optional[str]:
        """
        PROBLEM: First Non-Repeating Character (Medium)

        Find the first character that appears exactly once in a string.
        Return the character, or None if no such character exists.

        Example 1:
        Input: s = "leetcode"
        Output: "l"

        Example 2:
        Input: s = "loveleetcode"
        Output: "v"

        Example 3:
        Input: s = "aabb"
        Output: None

        Constraints:
        - 1 <= s.length <= 10^5
        - s consists of only lowercase English letters

        Time Complexity: O(n)
        Space Complexity: O(1) - at most 26 characters
        """

        char_count = HashTable()

        # First pass: count frequencies
        for char in s:
            count = char_count.get(char)
            char_count.put(char, 1 if count is None else count + 1)

        # Second pass: find first unique
        for char in s:
            if char_count.get(char) == 1:
                return char
        return None

    # Q4
    def group_anagrams(self, words: list) -> list:
        """
        PROBLEM: Group Anagrams (Medium)

        Given an array of strings, group anagrams together.
        An anagram is a word formed by rearranging letters of another word.

        Example 1:
        Input: words = ["eat", "tea", "tan", "ate", "nat", "bat"]
        Output: [["eat", "tea", "ate"], ["tan", "nat"], ["bat"]]

        Example 2:
        Input: words = [""]
        Output: [[""]]

        Constraints:
        - 1 <= words.length <= 10^4
        - 0 <= words[i].length <= 100
        - words[i] consists of lowercase English letters

        Time Complexity: O(n * k log k) where n is number of words, k is max word length
        Space Complexity: O(n * k)

        Hint: Use sorted characters as the key to group anagrams
        """
        groups = HashTable()
        for word in words:
            # Sort characters to create key
            sorted_chars = "".join(sorted(word))
            group = groups.get(sorted_chars)
            if group is None:
                groups.put(sorted_chars, [word])
            else:
                group.append(word)
        return groups.values()

    # Q5
    def longest_substring_without_repeating(self, s: str) -> int:
        """
        PROBLEM: Longest Substring Without Repeating Characters (Medium)

        Given a string, find the length of the longest substring without
        repeating characters.

        Example 1:
        Input: s = "abcabcbb"
        Output: 3
        Explanation: "abc" has length 3

        Example 2:
        Input: s = "bbbbb"
        Output: 1
        Explanation: "b" has length 1

        Example 3:
        Input: s = "pwwkew"
        Output: 3
        Explanation: "wke" has length 3

        Constraints:
        - 0 <= s.length <= 5 * 10^4
        - s consists of English letters, digits, symbols and spaces

        Time Complexity: O(n)
        Space Complexity: O(min(m, n)) where m is charset size

        Hint: Use sliding window with hash map tracking last seen indices
        - When a duplicate is found, move left pointer to max(current left, last seen index + 1)
        - Keep updating the maximum length as you expand the window
        - Only shrink when current char was seen at or after left: if c in map and map[c] >= left
        - Window length formula: right - left + 1; update ans = max(ans, right - left + 1)
        """

        if len(s) <= 1:
            return len(s)

        last_seen = HashTable()  # char -> last index
        left = 0
        max_len = 0

        for right in range(len(s)):
            char = s[right]
            idx = last_seen.get(char)
            if idx is not None and idx >= left:
                left = idx + 1
            last_seen.put(char, right)
            max_len = max(max_len, right - left + 1)

        return max_len

    # ========== MEDIUM-HARD PROBLEMS ==========
    # Q6
    def top_k_frequent(self, nums: list, k: int) -> list:
        """
        PROBLEM: Top K Frequent Elements (Medium-Hard)

        Given an integer array and an integer k, return the k most frequent elements.
        The answer is guaranteed to be unique.

        Example 1:
        Input: nums = [1,1,1,2,2,3], k = 2
        Output: [1,2]

        Example 2:
        Input: nums = [1], k = 1
        Output: [1]

        Constraints:
        - 1 <= nums.length <= 10^5
        - -10^4 <= nums[i] <= 10^4
        - k is in the range [1, number of unique elements]

        Time Complexity: O(n log k) with heap, O(n log n) with sorting
        Space Complexity: O(n)

        Hint: Count frequencies, then use sorting or heap for top-k selection
        - Build frequency map with HashTable, then extract top-k using sorting or bucket sort
        - Alternative: Use bucket sort by frequency (O(n) time) if frequencies are bounded
        - For heap approach: use min-heap of size k to maintain top-k elements efficiently
        """

        counter = HashTable()

        # compute frequency of num in nums
        for num in nums:
            current_count = counter.get(num)
            counter.put(num, 1 if current_count is None else current_count + 1)

        # use heap to get the max K frequent num
        heap = []
        for num, freq in counter.items():
            if len(heap) < k:
                heapq.heappush(heap, (freq, num))
            else:
                if freq > heap[0][0]:
                    heapq.heappop(heap)
                    heapq.heappush(heap, (freq, num))

        # read out from heap top k frequent num
        return [x[-1] for x in heap]


def practice_exercises():
    """Demonstrate essential hash table problems with comprehensive test cases."""
    problems = HashTableProblems()

    print("=== ESSENTIAL HASH TABLE PROBLEMS ===\n")

    # Easy Problems
    print("1. Word Frequency Counter (Easy):")
    text = "the quick brown fox jumps over the lazy dog the fox is quick"
    result = problems.word_frequency_counter(text)
    print(f"   Input: '{text}'")
    print(f"   Word counts: {dict(result.items())}")
    print(f"   Verification: 'the' appears {result.get('the')} times (Expected: 3)\n")

    # Edge cases for word frequency
    edge_tests_freq = [
        ("", {}),
        ("hello", {"hello": 1}),
        ("a a a", {"a": 3}),
        ("hello world hello", {"hello": 2, "world": 1}),
    ]
    for test_text, expected in edge_tests_freq:
        if test_text:  # Skip empty string for demo
            result = problems.word_frequency_counter(test_text)
            actual = dict(result.items())
            print(f"   Edge Test: '{test_text}' -> {actual} (Expected: {expected})")

    print("\n2. Two Sum (Easy):")
    nums = [2, 7, 11, 15]
    target = 9
    result = problems.two_sum(nums, target)
    print(f"   Input: nums={nums}, target={target}")
    print(f"   Output: {result} (Expected: [0, 1])\n")

    # Edge cases for two sum
    edge_tests_2sum = [
        ([3, 2, 4], 6, [1, 2]),
        ([3, 3], 6, [0, 1]),
        ([-1, 0, 1], 0, [0, 2]),
    ]
    for test_nums, test_target, expected in edge_tests_2sum:
        result = problems.two_sum(test_nums[:], test_target)
        print(
            f"   Edge Test: two_sum({test_nums}, {test_target}) -> {result} (Expected: {expected})"
        )

    # Medium Problems
    print("\n3. First Non-Repeating Character (Medium):")
    s = "programming"
    result = problems.first_unique_char(s)
    print(f"   Input: '{s}'")
    print(f"   First unique: '{result}' (Expected: 'p')\n")

    # Edge cases for first unique char
    edge_tests_unique = [
        ("leetcode", "l"),
        ("loveleetcode", "v"),
        ("aabb", None),
        ("abccba", None),
        ("z", "z"),
    ]
    for test_s, expected in edge_tests_unique:
        result = problems.first_unique_char(test_s)
        print(
            f"   Edge Test: first_unique_char('{test_s}') -> '{result}' (Expected: '{expected}')"
        )

    print("\n4. Group Anagrams (Medium):")
    words = ["eat", "tea", "tan", "ate", "nat", "bat"]
    result = problems.group_anagrams(words)
    print(f"   Input: {words}")
    print(f"   Groups: {result}")
    print(
        "   Expected: [['eat','tea','ate'], ['tan','nat'], ['bat']] (order may vary)\n"
    )

    # Edge cases for group anagrams
    edge_tests_anagram = [
        ([""], [[""]]),
        (["a"], [["a"]]),
        (["abc", "bca", "cab"], [["abc", "bca", "cab"]]),
        (["abc", "def"], [["abc"], ["def"]]),
    ]
    for test_words, expected_pattern in edge_tests_anagram:
        result = problems.group_anagrams(test_words[:])
        print(f"   Edge Test: group_anagrams({test_words}) -> {result}")

    print("\n5. Longest Substring Without Repeating (Medium):")
    s = "abcabcbb"
    result = problems.longest_substring_without_repeating(s)
    print(f"   Input: '{s}'")
    print(f"   Length: {result} (Expected: 3, substring 'abc')\n")

    # Edge cases for longest substring
    edge_tests_longest = [
        ("", 0),
        ("b", 1),
        ("bbbbb", 1),
        ("pwwkew", 3),  # "wke"
        ("abcdef", 6),
        ("aab", 2),  # "ab"
    ]
    for test_s, expected in edge_tests_longest:
        result = problems.longest_substring_without_repeating(test_s)
        print(
            f"   Edge Test: longest_substring('{test_s}') -> {result} (Expected: {expected})"
        )

    # Medium-Hard Problems
    print("\n6. Top K Frequent Elements (Medium-Hard):")
    nums = [1, 1, 1, 2, 2, 3]
    k = 2
    result = problems.top_k_frequent(nums, k)
    print(f"   Input: nums={nums}, k={k}")
    print(f"   Top {k} frequent: {result} (Expected: [1, 2] - order may vary)\n")

    # Edge cases for top k frequent
    edge_tests_topk = [
        ([1], 1, [1]),
        ([1, 2], 2, [1, 2]),  # order may vary
        ([1, 1, 1, 2, 2, 3], 1, [1]),
        ([4, 1, -1, 2, -1, 2, 3], 2, [-1, 2]),  # order may vary
    ]
    for test_nums, test_k, expected_pattern in edge_tests_topk:
        result = problems.top_k_frequent(test_nums[:], test_k)
        print(f"   Edge Test: top_k_frequent({test_nums}, {test_k}) -> {result}")

    print("\n" + "=" * 60)
    print("SUMMARY:")
    print("- 2 Easy problems (Word Frequency, Two Sum)")
    print("- 3 Medium problems (First Unique Char, Group Anagrams, Longest Substring)")
    print("- 1 Medium-Hard problem (Top K Frequent)")
    print("- All essential hash table patterns covered")
    print("- Comprehensive edge case testing included")
    print("- Ready for coding interview practice!")


if __name__ == "__main__":
    # First understand the implementation
    demonstrate_hash_table()

    print("\n" + "=" * 60 + "\n")

    # Then practice with exercises
    practice_exercises()
