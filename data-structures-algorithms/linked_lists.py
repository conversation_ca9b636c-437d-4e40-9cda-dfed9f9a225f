"""
Linked Lists - Top 10 Essential Patterns for Coding Interviews

This module covers the 10 most critical linked list patterns frequently tested
in coding interviews at top tech companies like Google, Meta, Amazon.

Top 10 Essential Patterns:
1. Cycle Detection (<PERSON>'s Algorithm) - has_cycle, detect_cycle  
2. Fast/Slow Pointers - find_middle, find_kth_from_end
3. Reverse Operations - reverse_list, reverse_between
4. Merge Operations - merge_two_sorted_lists
5. List Manipulation - remove_nth_from_end, remove_duplicates_sorted
6. Mathematical Operations - add_two_numbers

Each pattern includes Time/Space Complexity analysis and interview tips.
Focus on these 10 patterns to maximize your interview preparation efficiency.
"""

from typing import Optional, List


class ListNode:
    """Definition for singly-linked list node."""
    def __init__(self, val: int = 0, next: Optional['ListNode'] = None):
        self.val = val
        self.next = next
    
    def __repr__(self):
        """String representation for debugging."""
        result = []
        current = self
        seen = set()
        
        while current and id(current) not in seen:
            seen.add(id(current))
            result.append(str(current.val))
            current = current.next
            if len(result) > 10:  # Prevent infinite loops in representation
                result.append("...")
                break
        
        if current:
            result.append(f"-> cycle to {current.val}")
        
        return " -> ".join(result)


class LinkedListPatterns:
    """Top 10 essential linked list algorithms for coding interviews.
    
    STUDY PLAN MARKERS:
    📖 READ & UNDERSTAND: Learn the pattern and solution approach
    💻 IMPLEMENT YOURSELF: Practice coding without looking at solution
    
    INTERVIEW FREQUENCY:
    ⭐⭐⭐ = Very High (asked in 70%+ of linked list interviews)
    ⭐⭐ = High (asked in 40-70% of interviews)
    ⭐ = Medium (asked in 20-40% of interviews)
    """
    
    # ========== 1. CYCLE DETECTION (FLOYD'S ALGORITHM) ⭐⭐⭐ ==========
    # Exercise 1: has_cycle
    def has_cycle(self, head: Optional[ListNode]) -> bool:
        """
        📖 READ & UNDERSTAND: Detect if linked list has a cycle using Floyd's cycle detection.
        
        Time Complexity: O(n)
        Space Complexity: O(1)
        Interview Frequency: ⭐⭐⭐ Very High
        
        Key insight: Fast pointer moves 2 steps, slow moves 1. If cycle exists, they meet.
        Companies: Google, Facebook, Amazon, Microsoft
        """


        if not head or not head.next: # zero or 1 node only
            return False
        
        slow = fast = head
        
        while fast and fast.next:
            assert slow is not None
            slow = slow.next
            fast = fast.next.next
            
            if slow == fast:
                return True
        
        return False

    
    # Exercise 2: detect_cycle
    def detect_cycle(self, head: Optional[ListNode]) -> Optional[ListNode]:
        """
        💻 IMPLEMENT YOURSELF: Find the node where cycle begins.
        
        Time Complexity: O(n)
        Space Complexity: O(1)
        Interview Frequency: ⭐⭐ High
        
        Floyd's algorithm: After meeting, move one pointer to head and advance both
        at same speed until they meet again - that's the cycle start.
        Companies: LinkedIn, Microsoft, Apple
        """

        if not head or not head.next:
            return None
        
        # Phase 1: Detect cycle
        slow = fast = head
        
        while fast and fast.next:
            assert slow is not None
            slow = slow.next
            fast = fast.next.next
            
            if slow == fast:
                break
        else:
            return None  # No cycle found
        
        # Phase 2: Find cycle start
        slow = head
        while slow != fast:
            assert slow is not None and fast is not None
            slow = slow.next
            fast = fast.next
        
        return slow 

    
    # ========== 2. FAST/SLOW POINTERS ⭐⭐⭐ ==========
    # Exercise 3: find_middle
    def find_middle(self, head: Optional[ListNode]) -> Optional[ListNode]:
        """
        💻 IMPLEMENT YOURSELF: Find middle node of linked list.
        
        Time Complexity: O(n)
        Space Complexity: O(1)
        Interview Frequency: ⭐⭐⭐ Very High
        
        For even length, returns second middle node.
        Companies: Amazon, Google, Facebook
        """

        # --- Solution (commented out) ---
        if not head:
            return None
        
        slow = fast = head
        
        while fast and fast.next:
            assert slow is not None
            slow = slow.next
            fast = fast.next.next
        
        return slow
    
    # Exercise 4: find_kth_from_end
    def find_kth_from_end(self, head: Optional[ListNode], k: int) -> Optional[ListNode]:
        """
        💻 IMPLEMENT YOURSELF: Find kth node from end using two pointers.
        
        Time Complexity: O(n)
        Space Complexity: O(1)
        Interview Frequency: ⭐⭐ High
        
        Move first pointer k steps ahead, then move both until first reaches end.
        Companies: Google, Microsoft, Amazon
        """

        if not head or k <= 0:
            return None

        first = second = head

        # Move first pointer k steps ahead
        for _ in range(k):
            if not first:
                return None
            first = first.next

        # Move both pointers until first reaches end
        while first:
            first = first.next
            assert second is not None
            second = second.next

        return second
        
    
    # ========== 3. REVERSE OPERATIONS ⭐⭐⭐ ==========
    # Exercise 5: reverse_list
    def reverse_list(self, head: Optional[ListNode]) -> Optional[ListNode]:
        """
        📖 READ & UNDERSTAND: Reverse entire linked list iteratively.
        
        Time Complexity: O(n)
        Space Complexity: O(1)
        Interview Frequency: ⭐⭐⭐ Very High
        
        Pattern: Keep track of prev, current, next. Reverse links iteratively.
        Companies: ALL major tech companies ask this
        """
        
        if not head or not head.next:
            return head 

        prev, current = None, head 

        while current:
            next_node = current.next 
            current.next = prev 
            prev = current 
            current = next_node
        
        return prev 

    
    
    # Exercise 6: reverse_between
    def reverse_between(self, head: Optional[ListNode], left: int, right: int) -> Optional[ListNode]:
        """
        📖 READ & UNDERSTAND: Reverse nodes from position left to right.
        
        Time Complexity: O(n)
        Space Complexity: O(1)
        Interview Frequency: ⭐⭐ High
        
        Find the sublist, reverse it, and reconnect.
        Companies: Facebook, Google, Amazon
        """

        # --- Solution (commented out) ---
        if not head or left == right: # nothing to reverse assuming reasonable values
            return head
        
        dummy = ListNode(0) # use dummpy nodes if helpful 
        dummy.next = head
        prev = dummy
        
        # Move to node before left position
        for _ in range(left - 1):
            temp_prev = prev.next
            if temp_prev is None:
                return dummy.next # return head 
            prev = temp_prev
        
        # Start reversing from left position
        if prev.next is None: # left is the last node 
            return dummy.next
        assert prev.next is not None
        current = prev.next
        
        for _ in range(right - left):
            assert current is not None
            next_temp = current.next
            if next_temp is None:
                break
            current.next = next_temp.next
            next_temp.next = prev.next
            prev.next = next_temp
        
        return dummy.next
    
    
    # ========== 4. MERGE OPERATIONS ⭐⭐⭐ ==========
    # Exercise 7: merge_two_sorted_lists
    def merge_two_sorted_lists(self, list1: Optional[ListNode], list2: Optional[ListNode]) -> Optional[ListNode]:
        """
        💻 IMPLEMENT YOURSELF: Merge two sorted linked lists.
        
        Time Complexity: O(n + m)
        Space Complexity: O(1)
        Interview Frequency: ⭐⭐⭐ Very High
        
        Use dummy node to simplify edge cases.
        Companies: Google, Amazon, Microsoft, Apple
        """


        # --- Solution (commented out) ---
        dummy = ListNode(0)
        current = dummy
        
        while list1 and list2:
            if list1.val <= list2.val:
                current.next = list1
                list1 = list1.next
            else:
                current.next = list2
                list2 = list2.next
            current = current.next
        
        # Append remaining nodes
        current.next = list1 or list2
        
        return dummy.next
    
    
    # ========== 5. LIST MANIPULATION ⭐⭐⭐ ==========
    # Exercise 8: remove_nth_from_end
    def remove_nth_from_end(self, head: Optional[ListNode], n: int) -> Optional[ListNode]:
        """
        💻 IMPLEMENT YOURSELF: Remove nth node from end of list.
        
        Time Complexity: O(L) where L is length
        Space Complexity: O(1)
        Interview Frequency: ⭐⭐⭐ Very High
        
        Two pointers: maintain gap of n+1 nodes.
        Companies: Amazon, Google, Facebook, LinkedIn
        """

        # --- Solution (commented out) ---
        dummy = ListNode(0)
        dummy.next = head
        first = second = dummy
        
        # Move first pointer n+1 steps ahead
        for _ in range(n + 1):
            if first is None:
                return dummy.next
            first = first.next
        
        # Move both until first reaches end
        while first:
            first = first.next
            assert second is not None
            second = second.next
        
        # Remove the nth node from end
        assert second is not None and second.next is not None
        second.next = second.next.next
        
        return dummy.next
    
    # Exercise 9: remove_duplicates_sorted
    def remove_duplicates_sorted(self, head: Optional[ListNode]) -> Optional[ListNode]:
        """
        💻 IMPLEMENT YOURSELF: Remove duplicates from sorted linked list.
        
        Time Complexity: O(n)
        Space Complexity: O(1)
        Interview Frequency: ⭐⭐ High
        
        Companies: Microsoft, Amazon, Google
        """


        # --- Solution (commented out) ---
        current = head
        
        while current is not None and current.next is not None:
            next_node = current.next
            assert next_node is not None
            if current.val == next_node.val:
                current.next = next_node.next
            else:
                current = next_node
        
        return head
    
    
    
    # ========== 6. MATHEMATICAL OPERATIONS ⭐⭐ ==========
    # Exercise 10: add_two_numbers
    def add_two_numbers(self, l1: Optional[ListNode], l2: Optional[ListNode]) -> Optional[ListNode]:
        """
        📖 READ & UNDERSTAND: Add two numbers represented as linked lists (digits in reverse order).
        
        Time Complexity: O(max(n, m))
        Space Complexity: O(max(n, m))
        Interview Frequency: ⭐⭐ High
        
        Simulate elementary addition with carry.
        Companies: Amazon, Microsoft, Google
        """
        
        # --- Solution (commented out) ---
        dummy = ListNode(0)
        current = dummy
        carry = 0
        
        while l1 or l2 or carry:
            val1 = l1.val if l1 else 0
            val2 = l2.val if l2 else 0
            
            total = val1 + val2 + carry
            carry = total // 10
            current.next = ListNode(total % 10)
            
            current = current.next
            if l1:
                l1 = l1.next
            if l2:
                l2 = l2.next
        
        return dummy.next
    


def demonstrate_patterns():
    """Demonstrate usage of the top 10 linked list patterns with comprehensive test cases."""
    patterns = LinkedListPatterns()
    
    print("=== TOP 10 LINKED LIST PATTERNS DEMONSTRATIONS ===\n")
    
    # Base test list: 1 -> 2 -> 3 -> 4 -> 5
    head = create_linked_list([1, 2, 3, 4, 5])
    print(f"Original list: {head}")
    
    # ========== Exercise 1: has_cycle ==========
    print("\n🔄 Exercise 1: has_cycle")
    print(f"  Normal list: {patterns.has_cycle(head)} (expected: False)")
    
    # Test with cycle
    cycle_head = create_linked_list([3, 2, 0, -4])
    tail = cycle_head
    while tail and tail.next:
        tail = tail.next
    if tail and cycle_head and cycle_head.next:
        tail.next = cycle_head.next  # Create cycle: -4 -> 2
    print(f"  With cycle: {patterns.has_cycle(cycle_head)} (expected: True)")
    
    # Test edge cases
    print(f"  Empty list: {patterns.has_cycle(None)} (expected: False)")
    single = ListNode(1)
    print(f"  Single node: {patterns.has_cycle(single)} (expected: False)")
    
    # ========== Exercise 2: detect_cycle ==========
    print("\n🎯 Exercise 2: detect_cycle")
    cycle_start = patterns.detect_cycle(cycle_head)
    print(f"  Cycle start: {cycle_start.val if cycle_start else 'None'} (expected: 2)")
    print(f"  No cycle: {patterns.detect_cycle(head)} (expected: None)")
    
    # ========== Exercise 3: find_middle ==========
    print("\n⚖️  Exercise 3: find_middle")
    middle = patterns.find_middle(head)
    print(f"  Odd length [1,2,3,4,5]: {middle.val if middle else 'None'} (expected: 3)")
    
    even_list = create_linked_list([1, 2, 3, 4])
    middle_even = patterns.find_middle(even_list)
    print(f"  Even length [1,2,3,4]: {middle_even.val if middle_even else 'None'} (expected: 3)")
    
    single_middle = patterns.find_middle(ListNode(42))
    print(f"  Single node: {single_middle.val if single_middle else 'None'} (expected: 42)")
    
    # ========== Exercise 4: find_kth_from_end ==========
    print("\n🔢 Exercise 4: find_kth_from_end")
    kth = patterns.find_kth_from_end(head, 2)
    print(f"  k=2: {kth.val if kth else 'None'} (expected: 4)")
    
    first = patterns.find_kth_from_end(head, 5)
    print(f"  k=5 (first): {first.val if first else 'None'} (expected: 1)")
    
    last = patterns.find_kth_from_end(head, 1)
    print(f"  k=1 (last): {last.val if last else 'None'} (expected: 5)")
    
    invalid = patterns.find_kth_from_end(head, 10)
    print(f"  k=10 (invalid): {invalid} (expected: None)")
    
    # ========== Exercise 5: reverse_list ==========
    print("\n🔄 Exercise 5: reverse_list")
    head_copy = create_linked_list([1, 2, 3, 4, 5])
    reversed_head = patterns.reverse_list(head_copy)
    print(f"  Reversed [1,2,3,4,5]: {reversed_head} (expected: 5->4->3->2->1)")
    
    single_rev = patterns.reverse_list(ListNode(42))
    print(f"  Single node: {single_rev.val if single_rev else 'None'} (expected: 42)")
    
    # ========== Exercise 6: reverse_between ==========
    print("\n🎯 Exercise 6: reverse_between")
    rb_list = create_linked_list([1, 2, 3, 4, 5])
    rb_res = patterns.reverse_between(rb_list, 2, 4)
    print(f"  Reverse(2,4): {rb_res} (expected: 1->4->3->2->5)")
    
    # Test reverse from beginning
    rb_list2 = create_linked_list([1, 2, 3, 4, 5])
    rb_res2 = patterns.reverse_between(rb_list2, 1, 3)
    print(f"  Reverse(1,3): {rb_res2} (expected: 3->2->1->4->5)")
    
    # Test single position
    rb_list3 = create_linked_list([1, 2, 3])
    rb_res3 = patterns.reverse_between(rb_list3, 2, 2)
    print(f"  Reverse(2,2): {rb_res3} (expected: 1->2->3)")
    
    # ========== Exercise 7: merge_two_sorted_lists ==========
    print("\n🔗 Exercise 7: merge_two_sorted_lists")
    l1 = create_linked_list([1, 2, 4])
    l2 = create_linked_list([1, 3, 4])
    merged = patterns.merge_two_sorted_lists(l1, l2)
    print(f"  [1,2,4] + [1,3,4]: {merged} (expected: 1->1->2->3->4->4)")
    
    # Test with empty list
    l3 = create_linked_list([1, 2, 3])
    merged_empty = patterns.merge_two_sorted_lists(l3, None)
    print(f"  [1,2,3] + []: {merged_empty} (expected: 1->2->3)")
    
    # Test different lengths
    l4 = create_linked_list([1, 5])
    l5 = create_linked_list([2, 3, 4])
    merged_diff = patterns.merge_two_sorted_lists(l4, l5)
    print(f"  [1,5] + [2,3,4]: {merged_diff} (expected: 1->2->3->4->5)")
    
    # ========== Exercise 8: remove_nth_from_end ==========
    print("\n❌ Exercise 8: remove_nth_from_end")
    test_list = create_linked_list([1, 2, 3, 4, 5])
    removed = patterns.remove_nth_from_end(test_list, 2)
    print(f"  Remove 2nd from end: {removed} (expected: 1->2->3->5)")
    
    # Test remove first
    test_list2 = create_linked_list([1, 2, 3])
    removed_first = patterns.remove_nth_from_end(test_list2, 3)
    print(f"  Remove 3rd from end: {removed_first} (expected: 2->3)")
    
    # Test remove last
    test_list3 = create_linked_list([1, 2, 3])
    removed_last = patterns.remove_nth_from_end(test_list3, 1)
    print(f"  Remove 1st from end: {removed_last} (expected: 1->2)")
    
    # ========== Exercise 9: remove_duplicates_sorted ==========
    print("\n🗑️  Exercise 9: remove_duplicates_sorted")
    dup_list = create_linked_list([1, 1, 2, 3, 3])
    no_dups = patterns.remove_duplicates_sorted(dup_list)
    print(f"  [1,1,2,3,3]: {no_dups} (expected: 1->2->3)")
    
    # Test all same values
    all_same = create_linked_list([2, 2, 2, 2])
    no_dups_same = patterns.remove_duplicates_sorted(all_same)
    print(f"  [2,2,2,2]: {no_dups_same} (expected: 2)")
    
    # Test no duplicates
    no_dup_list = create_linked_list([1, 2, 3, 4])
    no_change = patterns.remove_duplicates_sorted(no_dup_list)
    print(f"  [1,2,3,4]: {no_change} (expected: 1->2->3->4)")
    
    # ========== Exercise 10: add_two_numbers ==========
    print("\n➕ Exercise 10: add_two_numbers")
    num1 = create_linked_list([2, 4, 3])
    num2 = create_linked_list([5, 6, 4])
    sum_result = patterns.add_two_numbers(num1, num2)
    print(f"  342 + 465: {sum_result} (expected: 7->0->8)")
    
    # Test with carry
    num3 = create_linked_list([9, 9])
    num4 = create_linked_list([9])
    sum_carry = patterns.add_two_numbers(num3, num4)
    print(f"  99 + 9: {sum_carry} (expected: 8->0->1)")
    
    # Test final carry
    num5 = create_linked_list([9, 9, 9])
    num6 = create_linked_list([1])
    sum_final_carry = patterns.add_two_numbers(num5, num6)
    print(f"  999 + 1: {sum_final_carry} (expected: 0->0->0->1)")
    
    # Test with zero
    zero = create_linked_list([0])
    num7 = create_linked_list([5, 6, 4])
    sum_zero = patterns.add_two_numbers(zero, num7)
    print(f"  0 + 465: {sum_zero} (expected: 5->6->4)")
    
    print("\n🎉 All demonstrations completed!")
    print("\n=== COMPREHENSIVE TEST SUMMARY ===")
    print("✅ Edge cases: Empty lists, single nodes, boundary conditions")
    print("✅ Normal cases: Various input sizes and scenarios")  
    print("✅ Special cases: Cycles, duplicates, carries, different lengths")
    print("✅ Error cases: Invalid inputs, out-of-bounds operations")
    
    print("\n=== INTERVIEW TIP ===")
    print("Focus on these 10 patterns - they cover 80%+ of linked list interview questions!")
    print("Master the ⭐⭐⭐ patterns first, then move to ⭐⭐ patterns.")
    print("Practice explaining your approach and handling edge cases during interviews.")


# Helper function to create linked list from array
def create_linked_list(arr: List[int]) -> Optional[ListNode]:
    """Create linked list from array of values."""
    if not arr:
        return None
    
    head = ListNode(arr[0])
    current = head
    
    for val in arr[1:]:
        current.next = ListNode(val)
        current = current.next
    
    return head


# Helper function to convert linked list to array
def linked_list_to_array(head: Optional[ListNode]) -> List[int]:
    """Convert linked list to array of values."""
    result = []
    current = head
    
    while current:
        result.append(current.val)
        current = current.next
    
    return result


if __name__ == "__main__":
    demonstrate_patterns()
