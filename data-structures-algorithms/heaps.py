"""
Heaps and Priority Queues - Essential Coding Interview Problems

This module contains 8 carefully selected heap problems that cover all essential
patterns frequently tested at top tech companies (Google, Facebook, Amazon, etc.).

Problem Difficulty Levels:
- Medium (7): Kth Largest, Top K Frequent, K Closest Points, Merge <PERSON>, Task Scheduler, Network Delay, Heapify
- Hard (1): Find Median Stream

Each problem includes detailed question descriptions, constraints, examples,
and hints for optimal solutions.
"""

import heapq  # noqa: F401 - Will be used in student implementations
from typing import List
from collections import Counter, deque  # noqa: F401 - Will be used in student implementations


class HeapPatterns:
    """Essential heap algorithms for coding interviews."""

    # Q1 ⭐⭐
    def find_kth_largest(self, nums: List[int], k: int) -> int:
        """
        PROBLEM: Kth Largest Element in an Array (Medium)

        Given an integer array nums and an integer k, return the kth largest element.
        Note that it is the kth largest element in the sorted order, not the kth distinct element.

        Example 1:
        Input: nums = [3,2,1,5,6,4], k = 2
        Output: 5
        Explanation: The 2nd largest element is 5

        Example 2:
        Input: nums = [3,2,3,1,2,4,5,5,6], k = 4
        Output: 4

        Constraints:
        - 1 <= k <= nums.length <= 10^5
        - -10^4 <= nums[i] <= 10^4
        - It's guaranteed that the answer exists

        Time Complexity: O(n log k) using min heap
        Space Complexity: O(k)

        Hints:
        1. Use min heap of size k - the root will be the kth largest
        2. For each element, if heap size < k, push it
        3. If heap size == k and current element > heap[0], pop then push
        4. After processing all elements, heap[0] is the kth largest
        5. Alternative: Use quickselect for O(n) average time complexity
        """

        heap = []
        for n in nums:
            if len(heap) < k:
                heapq.heappush(heap, n)
            elif n >= heap[0]:
                heapq.heappop(heap)
                heapq.heappush(heap, n)

        return heap[0]

    # Q2 ⭐⭐
    def top_k_frequent(self, nums: List[int], k: int) -> List[int]:
        """
        PROBLEM: Top K Frequent Elements (Medium)

        Given an integer array nums and an integer k, return the k most frequent elements.
        You may return the answer in any order.

        Example 1:
        Input: nums = [1,1,1,2,2,3], k = 2
        Output: [1,2]
        Explanation: 1 appears 3 times, 2 appears 2 times

        Example 2:
        Input: nums = [1], k = 1
        Output: [1]

        Constraints:
        - 1 <= nums.length <= 10^5
        - k is in the range [1, number of unique elements]
        - It's guaranteed that the answer is unique

        Time Complexity: O(n log k)
        Space Complexity: O(n)

        Hint: Count frequencies first, then use min heap of size k with (freq, num) pairs
        """

        # Count frequencies using Counter
        count = Counter(nums)

        # Use min heap to maintain k most frequent elements
        heap = []
        for num, freq in count.items():
            if len(heap) < k:
                heapq.heappush(heap, (freq, num))
            elif freq > heap[0][0]:
                heapq.heappop(heap)
                heapq.heappush(heap, (freq, num))

        # Extract numbers from heap (no need to reverse since order doesn't matter)
        return [num for freq, num in heap]

    # Q3 ⭐⭐
    def k_closest_points(self, points: List[List[int]], k: int) -> List[List[int]]:
        """
        PROBLEM: K Closest Points to Origin (Medium)

        Given an array of points where points[i] = [xi, yi] represents a point on the X-Y plane
        and an integer k, return the k closest points to the origin (0, 0).

        The distance between two points is the Euclidean distance.

        Example 1:
        Input: points = [[1,3],[-2,2]], k = 1
        Output: [[-2,2]]
        Explanation: Distance from (-2,2) to origin is sqrt(8) = 2.83
                    Distance from (1,3) to origin is sqrt(10) = 3.16

        Example 2:
        Input: points = [[3,3],[5,-1],[-2,4]], k = 2
        Output: [[3,3],[-2,4]]

        Constraints:
        - 1 <= k <= points.length <= 10^4
        - -10^4 <= xi, yi <= 10^4

        Time Complexity: O(n log k)
        Space Complexity: O(k)

        Hint: Use max heap with distance squared (avoid sqrt). Store (-distance, point)
        """

        max_heap = []

        for point in points:
            distance = point[0] ** 2 + point[1] ** 2
            if len(max_heap) < k:
                heapq.heappush(max_heap, (-distance, point))
            elif -distance > max_heap[0][0]:
                heapq.heappop(max_heap)
                heapq.heappush(max_heap, (-distance, point))

        return [item[-1] for item in max_heap]

    # Q4 ⭐⭐⭐
    def __init__(self):
        """Initialize data structures for streaming median."""
        self.small = []  # Max heap (use negative values)
        self.large = []  # Min heap

    def add_num(self, num: int) -> None:
        """
        PROBLEM: Find Median from Data Stream (Hard)

        The median is the middle value in an ordered integer list. If the size of the list
        is even, the median is the average of the two middle values.

        Implement the MedianFinder class:
        - MedianFinder() initializes the MedianFinder object
        - void addNum(int num) adds the integer num to the data structure
        - double findMedian() returns the median of all elements so far

        Example 1:
        Input: ["MedianFinder", "addNum", "addNum", "findMedian", "addNum", "findMedian"]
               [[], [1], [2], [], [3], []]
        Output: [null, null, null, 1.5, null, 2.0]

        Constraints:
        - -10^5 <= num <= 10^5
        - There will be at least one element before calling findMedian
        - At most 5 * 10^4 calls will be made to addNum and findMedian

        Time Complexity: O(log n) for addNum, O(1) for findMedian
        Space Complexity: O(n)

        Hint: Use two heaps - max heap for smaller half, min heap for larger half
        """

        # Canonical implementation: always add to max heap first, then balance
        heapq.heappush(self.small, -num)

        # Balance: move max from small to large
        heapq.heappush(self.large, -heapq.heappop(self.small))

        # Ensure small has at most 1 more element than large
        if len(self.small) < len(self.large):
            heapq.heappush(self.small, -heapq.heappop(self.large))

    def find_median(self) -> float:
        """Find current median from the stream."""
        if len(self.small) > len(self.large):
            return -self.small[0]  # Max heap has extra element
        else:
            return (-self.small[0] + self.large[0]) / 2.0

    # Q5 ⭐⭐
    def merge_k_sorted_lists(self, lists: List[List[int]]) -> List[int]:
        """
        PROBLEM: Merge K Sorted Arrays (Medium)

        You are given an array of k sorted arrays, each with n elements.
        Merge them into one sorted array.

        Example 1:
        Input: lists = [[1,4,5],[1,3,4],[2,6]]
        Output: [1,1,2,3,4,4,5,6]

        Example 2:
        Input: lists = []
        Output: []

        Example 3:
        Input: lists = [[]]
        Output: []

        Constraints:
        - k == lists.length
        - 0 <= k <= 10^4
        - 0 <= lists[i].length <= 500
        - -10^4 <= lists[i][j] <= 10^4
        - lists[i] is sorted in ascending order

        Time Complexity: O(n log k) where n is total elements
        Space Complexity: O(k)

        Hint: Use min heap with (value, array_index, element_index) tuples
        """
        # Handle edge cases
        if not lists or not any(lists):
            return []

        heap = []
        result = []

        # Initialize heap with first element from each non-empty list
        for list_idx, lst in enumerate(lists):
            if lst:  # Only add non-empty lists
                heapq.heappush(heap, (lst[0], list_idx, 0))

        # Process heap until empty
        while heap:
            value, list_idx, element_idx = heapq.heappop(heap)
            result.append(value)

            # Add next element from same list if available
            if element_idx + 1 < len(lists[list_idx]):
                next_value = lists[list_idx][element_idx + 1]
                heapq.heappush(heap, (next_value, list_idx, element_idx + 1))

        return result

    # Q6 ⭐⭐
    def least_interval(self, tasks: List[str], n: int) -> int:
        """
        PROBLEM: Task Scheduler (Medium)

        Given a characters array tasks, representing the tasks a CPU needs to do, where each letter
        represents a different task. Tasks could be done without the original order of the array.
        Each task is done in one unit of time. For each unit of time, the CPU could complete either
        one task or just be idle. However, there is a non-negative integer n that represents the
        cooldown period between two same tasks (the same letter in the array), that is that there
        must be at least n units of time between any two same tasks.

        Return the least number of units of time that the CPU will take to finish all tasks.

        Example 1:
        Input: tasks = ["A","A","A","B","B","B"], n = 2
        Output: 8
        Explanation: A -> B -> idle -> A -> B -> idle -> A -> B

        Example 2:
        Input: tasks = ["A","A","A","B","B","B"], n = 0
        Output: 6
        Explanation: No cooldown period, so tasks can be done consecutively

        Constraints:
        - 1 <= task.length <= 10^4
        - tasks[i] is upper-case English letter
        - 0 <= n <= 100

        Time Complexity: O(time) where time is the result
        Space Complexity: O(1) - at most 26 different tasks

        Hint: Use a max heap to always pick the most frequent task available, and a
        queue to keep track of tasks in their cooldown period. At each time unit,
        either execute a task (if available) or idle if none can be scheduled.
        Continue until all tasks are processed. The heap helps efficiently select
        the next eligible task, while the queue manages cooldown timing for each task.
        """
        # Approach 1: Mathematical solution (most elegant)
        # Count frequency of each task
        count = Counter(tasks)

        # Find the maximum frequency and how many tasks have this frequency
        max_freq = max(count.values())
        max_freq_count = sum(1 for freq in count.values() if freq == max_freq)

        # Calculate minimum time slots needed
        # Pattern: most frequent task creates (max_freq - 1) blocks of size (n + 1)
        # Plus one final block for the last occurrence of max frequency tasks
        min_time = (max_freq - 1) * (n + 1) + max_freq_count

        # Result cannot be less than total number of tasks
        return max(min_time, len(tasks))

        # # Solution 2
        # # Count frequencies
        # count = Counter(tasks)

        # # Max heap for available tasks (negative for max heap)
        # available = [-freq for freq in count.values()]
        # heapq.heapify(available)

        # # Queue for cooling tasks: (frequency, time_available)
        # cooling = deque()

        # time = 0

        # while available or cooling:
        #     time += 1

        #     # Move cooled tasks back to available heap
        #     if cooling and cooling[0][1] == time:
        #         heapq.heappush(available, cooling.popleft()[0])

        #     # Execute most frequent available task
        #     if available:
        #         freq = heapq.heappop(available)
        #         if freq < -1:  # Still has remaining executions
        #             cooling.append((freq + 1, time + n + 1))

        #     # If no tasks available, we idle (time still increments)

        # return time

    # Q7 ⭐⭐
    def network_delay_time(self, times: List[List[int]], n: int, k: int) -> int:
        """
        PROBLEM: Network Delay Time (Medium)

        You are given a network of n nodes, labeled from 1 to n. You are also given times,
        a list of travel times as directed edges times[i] = (ui, vi, wi), where ui is the
        source node, vi is the target node, and wi is the time it takes for a signal to
        travel from source to target.

        We will send a signal from a given node k. Return the minimum time it takes for all
        the n nodes to receive the signal. If it is impossible for all nodes to receive the
        signal, return -1.

        Example 1:
        Input: times = [[2,1,1],[2,3,1],[3,4,1]], n = 4, k = 2
        Output: 2
        Explanation: Signal sent from node 2, reaches all nodes in 2 time units

        Example 2:
        Input: times = [[1,2,1]], n = 2, k = 1
        Output: 1

        Example 3:
        Input: times = [[1,2,1]], n = 2, k = 2
        Output: -1
        Explanation: Node 2 cannot send signal to node 1

        Constraints:
        - 1 <= k <= n <= 100
        - 1 <= times.length <= 6000
        - times[i].length == 3
        - 1 <= ui, vi <= n
        - ui != vi
        - 0 <= wi <= 100
        - All pairs (ui, vi) are unique

        Time Complexity: O(E log V) using Dijkstra's algorithm
        Space Complexity: O(V + E)

        Hint: This is Dijkstra's shortest path algorithm with min heap
        """
        # Build adjacency list graph
        graph = {}
        for u, v, w in times:
            if u not in graph:
                graph[u] = []
            graph[u].append((v, w))

        # Dijkstra's algorithm using min heap
        # Format: (distance, node)
        heap = [(0, k)]  # Start from node k with distance 0
        distances = {}  # Track shortest distances to each node

        while heap:
            current_dist, node = heapq.heappop(heap)

            # Skip if we've already found a shorter path to this node
            if node in distances:
                continue

            # Record the shortest distance to this node
            distances[node] = current_dist

            # Explore neighbors
            if node in graph:
                for neighbor, weight in graph[node]:
                    if neighbor not in distances:  # Only consider unvisited nodes
                        new_dist = current_dist + weight
                        heapq.heappush(heap, (new_dist, neighbor))

        # Check if all nodes are reachable
        if len(distances) != n:
            return -1

        # Return the maximum distance (time for signal to reach all nodes)
        return max(distances.values())

    # Q8 ⭐⭐
    def heapify(self, nums: List[int]) -> List[int]:
        """
        PROBLEM: Build Min Heap from Array (Medium)

        Given an array of integers, transform it into a min heap in-place.
        A min heap is a complete binary tree where every parent node is less
        than or equal to its children.

        Example 1:
        Input: nums = [4,1,3,2,16,9,10,14,8,7]
        Output: [1,2,3,4,7,9,10,14,8,16]
        Explanation: Array represents min heap where nums[i] <= nums[2*i+1] and nums[2*i+2]

        Example 2:
        Input: nums = [3,2,1]
        Output: [1,2,3]

        Example 3:
        Input: nums = [5]
        Output: [5]

        Constraints:
        - 1 <= nums.length <= 10^5
        - -10^9 <= nums[i] <= 10^9

        Time Complexity: O(n) - optimal heapify algorithm
        Space Complexity: O(1) - in-place transformation

        Hint: Start from last non-leaf node and bubble down. Last non-leaf is at index (n//2 - 1)
        """

        def sift_down(start, end):
            """Sift down element at start index to maintain heap property."""
            parent = start
            while True:
                left_child = 2 * parent + 1
                right_child = 2 * parent + 2
                smallest = parent

                # Find smallest among parent and children
                if left_child <= end and nums[left_child] < nums[smallest]:
                    smallest = left_child
                if right_child <= end and nums[right_child] < nums[smallest]:
                    smallest = right_child

                # If parent is already smallest, heap property satisfied
                if smallest == parent:
                    break

                # Swap and continue sifting down
                nums[parent], nums[smallest] = nums[smallest], nums[parent]
                parent = smallest

        n = len(nums)
        # Start from last non-leaf node and work backwards
        for i in range(n // 2 - 1, -1, -1):
            sift_down(i, n - 1)

        return nums


def demonstrate_patterns():
    """Demonstrate comprehensive test cases for the 7 essential heap problems."""

    print("=== COMPREHENSIVE HEAP PROBLEMS TESTING ===\n")

    # Q1: Kth Largest Element
    print("1. Kth Largest Element (Medium ★★):")
    test_cases_1 = [
        ([3, 2, 1, 5, 6, 4], 2, 5),  # Basic case
        ([3, 2, 3, 1, 2, 4, 5, 5, 6], 4, 4),  # Duplicates
        ([1], 1, 1),  # Single element
        ([7, 10, 4, 3, 20, 15], 3, 10),  # K in middle
        ([2, 1], 1, 2),  # K = 1 (largest)
        ([-1, 2, 0], 2, 0),  # Negative numbers
    ]

    for i, (nums, k, expected) in enumerate(test_cases_1):
        patterns = HeapPatterns()
        result = patterns.find_kth_largest(nums, k)
        status = "✅" if result == expected else "❌"
        print(
            f"   Test {i + 1}: nums={nums}, k={k} → {result} (expected: {expected}) {status}"
        )

    # Q2: Top K Frequent Elements
    print("\n2. Top K Frequent Elements (Medium ★★):")
    test_cases_2 = [
        ([1, 1, 1, 2, 2, 3], 2, {1, 2}),  # Basic case
        ([1], 1, {1}),  # Single element
        ([1, 2], 2, {1, 2}),  # All unique
        ([4, 1, -1, 2, -1, 2, 3], 2, {-1, 2}),  # Negative numbers
        ([1, 1, 1, 1], 1, {1}),  # All same
        ([1, 2, 3, 4, 5], 3, {1, 2, 3}),  # All frequency 1
    ]

    for i, (nums, k, expected) in enumerate(test_cases_2):
        patterns = HeapPatterns()
        result = set(patterns.top_k_frequent(nums, k))
        status = "✅" if result == expected else "❌"
        print(
            f"   Test {i + 1}: nums={nums}, k={k} → {result} (expected: {expected}) {status}"
        )

    # Q3: K Closest Points
    print("\n3. K Closest Points to Origin (Medium ★★):")
    test_cases_3 = [
        ([[1, 3], [-2, 2]], 1, [[-2, 2]]),  # Basic case
        ([[3, 3], [5, -1], [-2, 4]], 2, [[3, 3], [-2, 4]]),  # Multiple points
        ([[0, 1], [1, 0]], 2, [[0, 1], [1, 0]]),  # Same distance
        ([[1, 1]], 1, [[1, 1]]),  # Single point
        ([[0, 0], [1, 1], [2, 2]], 1, [[0, 0]]),  # Origin included
        ([[-5, 4], [-6, -2], [4, 6]], 2, [[-6, -2], [-5, 4]]),  # Negative coordinates
    ]

    for i, (points, k, expected) in enumerate(test_cases_3):
        patterns = HeapPatterns()
        result = patterns.k_closest_points(points, k)
        # Sort for comparison since order may vary
        result_sorted = sorted(result)
        expected_sorted = sorted(expected)
        status = "✅" if result_sorted == expected_sorted else "❌"
        print(
            f"   Test {i + 1}: points={points}, k={k} → {result} (expected: {expected}) {status}"
        )

    # Q4: Find Median from Stream
    print("\n4. Find Median from Data Stream (Hard ★★★):")
    test_cases_4 = [
        ([1, 2], 1.5),  # Even count
        ([1, 2, 3], 2.0),  # Odd count
        ([6, 10, 2, 6, 5, 0, 6, 3, 1, 0, 0], 3.0),  # Complex sequence
        ([-1, -2], -1.5),  # Negative numbers
        ([1], 1.0),  # Single element
        ([5, 5, 5, 5], 5.0),  # All same
    ]

    for i, (sequence, expected) in enumerate(test_cases_4):
        patterns = HeapPatterns()
        for num in sequence:
            patterns.add_num(num)
        result = patterns.find_median()
        status = "✅" if abs(result - expected) < 0.001 else "❌"
        print(
            f"   Test {i + 1}: sequence={sequence} → {result} (expected: {expected}) {status}"
        )

    # Q5: Merge K Sorted Arrays
    print("\n5. Merge K Sorted Arrays (Medium ★★):")
    test_cases_5 = [
        ([[1, 4, 5], [1, 3, 4], [2, 6]], [1, 1, 2, 3, 4, 4, 5, 6]),  # Basic case
        ([], []),  # Empty input
        ([[]], []),  # Empty subarray
        ([[1]], [1]),  # Single element
        ([[1, 2, 3], [4, 5, 6]], [1, 2, 3, 4, 5, 6]),  # No overlap
        ([[1, 1, 1], [2, 2, 2]], [1, 1, 1, 2, 2, 2]),  # Duplicates
        ([[-1, 0], [-3, -2], [1, 2]], [-3, -2, -1, 0, 1, 2]),  # Negatives
        ([[1, 4, 5], [], [2, 6]], [1, 2, 4, 5, 6]),  # Mixed empty/non-empty
    ]

    for i, (lists, expected) in enumerate(test_cases_5):
        patterns = HeapPatterns()
        result = patterns.merge_k_sorted_lists(lists)
        status = "✅" if result == expected else "❌"
        print(
            f"   Test {i + 1}: lists={lists} → {result} (expected: {expected}) {status}"
        )

    # Q6: Task Scheduler
    print("\n6. Task Scheduler (Medium ★★):")
    test_cases_6 = [
        (["A", "A", "A", "B", "B", "B"], 2, 8),  # Basic case
        (["A", "A", "A", "B", "B", "B"], 0, 6),  # No cooldown
        (
            ["A", "A", "A", "A", "A", "A", "B", "C", "D", "E", "F", "G"],
            2,
            16,
        ),  # High frequency
        (["A", "B", "C", "D", "E", "F"], 2, 6),  # All different tasks
        (["A"], 0, 1),  # Single task
        (["A", "A"], 1, 3),  # Minimum case with cooldown
        (["A", "A", "A"], 3, 9),  # High cooldown
    ]

    for i, (tasks, n, expected) in enumerate(test_cases_6):
        patterns = HeapPatterns()
        result = patterns.least_interval(tasks, n)
        status = "✅" if result == expected else "❌"
        print(
            f"   Test {i + 1}: tasks={tasks}, n={n} → {result} (expected: {expected}) {status}"
        )

    # Q7: Network Delay Time
    print("\n7. Network Delay Time (Medium ★★):")
    test_cases_7 = [
        ([[2, 1, 1], [2, 3, 1], [3, 4, 1]], 4, 2, 2),  # Basic case
        ([[1, 2, 1]], 2, 1, 1),  # Simple path
        ([[1, 2, 1]], 2, 2, -1),  # Unreachable
        ([[1, 2, 1], [2, 3, 2], [1, 3, 4]], 3, 1, 3),  # Multiple paths
        ([[1, 2, 1], [2, 1, 3]], 2, 2, 3),  # Cycle
        ([], 1, 1, 0),  # Self-loop only
        ([[1, 2, 5], [2, 3, 1], [1, 3, 10]], 3, 1, 6),  # Choose shorter path
    ]

    for i, (times, n, k, expected) in enumerate(test_cases_7):
        patterns = HeapPatterns()
        result = patterns.network_delay_time(times, n, k)
        status = "✅" if result == expected else "❌"
        print(
            f"   Test {i + 1}: times={times}, n={n}, k={k} → {result} (expected: {expected}) {status}"
        )

    # Q8: Heapify
    print("\n8. Build Min Heap from Array (Medium ★★):")

    def is_min_heap(nums):
        """Helper function to verify if array represents a valid min heap."""
        n = len(nums)
        for i in range(n // 2):  # Check all non-leaf nodes
            left_child = 2 * i + 1
            right_child = 2 * i + 2

            # Check left child
            if left_child < n and nums[i] > nums[left_child]:
                return False
            # Check right child
            if right_child < n and nums[i] > nums[right_child]:
                return False
        return True

    test_cases_8 = [
        [4, 1, 3, 2, 16, 9, 10, 14, 8, 7],  # Basic case from problem description
        [3, 2, 1],  # Simple case
        [5],  # Single element
        [1],  # Single element (already heap)
        [1, 2, 3, 4, 5],  # Already sorted (worst case for heapify)
        [5, 4, 3, 2, 1],  # Reverse sorted
        [1, 1, 1, 1, 1],  # All duplicates
        [10, -5, 3, -2, 16, 0, -10, 14, 8, 7],  # With negative numbers
        [],  # Empty array
        [42],  # Single element
    ]

    for i, nums_input in enumerate(test_cases_8):
        patterns = HeapPatterns()
        # Make a copy since heapify modifies in-place
        nums_copy = nums_input.copy()
        result = patterns.heapify(nums_copy)

        # Verify it's a valid min heap
        is_valid = is_min_heap(result) if result else True

        # Verify same elements (sorted)
        same_elements = (
            sorted(nums_input) == sorted(result) if result else len(nums_input) == 0
        )

        status = "✅" if is_valid and same_elements else "❌"

        print(f"   Test {i + 1}: {nums_input}")
        print(f"           → {result}")
        print(
            f"           Valid min-heap: {is_valid}, Same elements: {same_elements} {status}"
        )

    print("\n" + "=" * 80)
    print("COMPREHENSIVE TEST SUMMARY:")
    print("- ✅ All implementations tested with edge cases")
    print("- 🎯 Covers: empty inputs, single elements, duplicates, negatives, extremes")
    print("- 🚀 Ready for Google/Meta/Amazon interviews!")
    print("- 📚 7 Medium + 1 Hard problem patterns mastered")


if __name__ == "__main__":
    demonstrate_patterns()
