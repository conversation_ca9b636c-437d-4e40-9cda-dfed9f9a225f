"""
Recursion & Backtracking - Core Patterns (Practice Mode)

Implement classic recursion and backtracking problems using the stubs below.
"""

from typing import List


class RecursionBacktracking:
    """Practice classic recursion and backtracking problems."""

    # Q1
    def factorial(self, n: int) -> int:
        """Compute n! recursively.

        PROBLEM: Factorial (Easy)

        Given a non-negative integer n, compute n! = n x (n-1) x ... x 1. By definition, 0! = 1.

        Example 1:
        Input: n = 0
        Output: 1

        Example 2:
        Input: n = 5
        Output: 120

        Constraints:
        - 0 <= n <= 12 (to avoid integer overflow in typical interview settings)

        Time Complexity: O(n)
        Space Complexity: O(n) recursion stack

        Hint: Use the recurrence n! = n x (n-1)! with base case 0! = 1.
        """

        # vanilla recursion approach
        if n <= 1:
            return 1
        return n * self.factorial(n - 1)

    # Q2
    def subsets(self, nums: List[int]) -> List[List[int]]:
        """
        Return all subsets (power set) of nums using backtracking.

        PROBLEM: Subsets (Medium)

        Given an integer array nums of unique elements, return all possible subsets (the power set).
        The solution set must not contain duplicate subsets. Return the solution in any order.

        Example 1:
        Input: nums = [1,2,3]
        Output: [[],[1],[2],[3],[1,2],[1,3],[2,3],[1,2,3]]

        Example 2:
        Input: nums = [0]
        Output: [[],[0]]

        Constraints:
        - 1 <= nums.length <= 10
        - -10 <= nums[i] <= 10
        - All numbers are unique

        Time Complexity: O(n * 2^n)
        Space Complexity: O(n) for recursion depth, O(2^n) for output

        Hint: Use backtracking with an index and a path list. At each step, choose to include
        or exclude the current element, and recurse forward. Start with an empty path and index 0.
        For each position, make two recursive calls: one that includes nums[index] in the current
        subset (append to path, recurse with index+1, then backtrack by removing), and another
        that excludes it (recurse with index+1 without modifying path). Base case: when index
        reaches len(nums), add a copy of the current path to results.
        """

        result: List[List[int]] = []
        path: List[int] = []

        def dfs(index: int) -> None:
            # Base case: processed all elements
            if index == len(nums):
                result.append(path[:])
                return
            # Choice 1: exclude nums[index]
            dfs(index + 1)
            # Choice 2: include nums[index]
            path.append(nums[index])
            dfs(index + 1)
            path.pop()

        dfs(0)
        return result

    # Q3
    def permutations(self, nums: List[int]) -> List[List[int]]:
        """Return all permutations of nums via backtracking.

        PROBLEM: Permutations (Medium)

        Given an array nums of distinct integers, return all the possible permutations.
        You can return the answer in any order.

        Example 1:
        Input: nums = [1,2,3]
        Output: [[1,2,3],[1,3,2],[2,1,3],[2,3,1],[3,1,2],[3,2,1]]

        Example 2:
        Input: nums = [0,1]
        Output: [[0,1],[1,0]]

        Constraints:
        - 1 <= nums.length <= 8
        - -10 <= nums[i] <= 10
        - All numbers are unique

        Time Complexity: O(n · n!)
        Space Complexity: O(n) for recursion depth, O(n!) for output

        Hint: Use backtracking with a `used` boolean array (or in-place swapping). Build a path until
        its length equals n, then append a copy to results.
        """

        # TODO: review, view backtarcking as exploring a tree from root to leaves
        result = []
        used = [False] * len(nums)
        path = []

        def backtrack():
            if len(path) == len(nums):
                result.append(path[:])
                return
            for i in range(len(nums)):
                if used[i]:
                    continue
                used[i] = True
                path.append(nums[i])
                backtrack()
                path.pop()
                used[i] = False

        backtrack()
        return result

    # Q4
    def combination_sum(self, candidates: List[int], target: int) -> List[List[int]]:
        """Combinations summing to target (unlimited reuse).

        PROBLEM: Combination Sum (Medium)

        Given an array of distinct integers candidates and a target integer target, return a list of all
        unique combinations of candidates where the chosen numbers sum to target. You may reuse each
        candidate an unlimited number of times. The combinations may be returned in any order.

        Example 1:
        Input: candidates = [2,3,6,7], target = 7
        Output: [[2,2,3],[7]]

        Example 2:
        Input: candidates = [2,3,5], target = 8
        Output: [[2,2,2,2],[2,3,3],[3,5]]

        Constraints:
        - 1 <= candidates.length <= 30
        - 2 <= candidates[i] <= 40
        - All elements of candidates are distinct
        - 1 <= target <= 40

        Time Complexity: O(S) where S is the total number of valid states explored (bounded by output size),
        worst-case exponential in n
        Space Complexity: O(n) recursion depth, plus output size

        Hint: Sort candidates. Use backtracking with index `start` to avoid duplicates, and allow
        reuse by passing the same index on recursive calls when choosing the current value.
        """

        candidates.sort()
        result: List[List[int]] = []
        path: List[int] = []

        def backtrack(i: int, remain: int) -> None:
            # Found a valid combination
            if remain == 0:
                result.append(path[:])
                return
            # Exhausted candidates or exceeded target
            if i == len(candidates) or remain < 0:
                return
            # Choice 1: take candidates[i] (unlimited reuse → stay at i)
            path.append(candidates[i])
            backtrack(i, remain - candidates[i])
            path.pop()
            # Choice 2: skip candidates[i] and move on
            backtrack(i + 1, remain)

        backtrack(0, target)
        return result


def demonstrate_patterns():
    obj = RecursionBacktracking()
    print("=== RECURSION & BACKTRACKING DEMO ===")

    # Helpers for assertions
    def norm(x: List[List[int]]) -> List[List[int]]:
        """Normalize list of lists by sorting inner lists and the outer container for comparison."""
        return sorted([sorted(y) for y in x])

    def as_set(x: List[List[int]]):
        """Convert list of lists to a set of tuples (order-insensitive across solutions, order-sensitive within)."""
        return {tuple(y) for y in x}

    # Q1: Factorial tests (edge cases + typical)
    assert obj.factorial(0) == 1, "factorial(0) should be 1"
    assert obj.factorial(1) == 1, "factorial(1) should be 1"
    assert obj.factorial(5) == 120, "factorial(5) should be 120"
    print("Factorial OK")

    # Q2: Subsets tests
    assert norm(obj.subsets([])) == norm([[]]), "subsets([]) should be [[]]"
    assert norm(obj.subsets([1])) == norm([[], [1]]), "subsets([1]) should be [[], [1]]"
    assert norm(obj.subsets([1, 2])) == norm([[], [1], [2], [1, 2]]), (
        "subsets([1,2]) mismatch"
    )
    print("Subsets OK")

    # Q3: Permutations tests
    assert as_set(obj.permutations([])) == {()}, "permutations([]) should be [[]]"
    assert as_set(obj.permutations([1])) == {(1,)}, "permutations([1]) should be [[1]]"
    assert as_set(obj.permutations([1, 2])) == {(1, 2), (2, 1)}, (
        "permutations([1,2]) mismatch"
    )
    print("Permutations OK")

    # Q4: Combination Sum tests (distinct candidates, unlimited reuse)
    assert norm(obj.combination_sum([2, 3, 6, 7], 7)) == norm([[2, 2, 3], [7]]), (
        "combination_sum basic case failed"
    )
    assert norm(obj.combination_sum([2, 3, 5], 8)) == norm(
        [[2, 2, 2, 2], [2, 3, 3], [3, 5]]
    ), "combination_sum multi case failed"
    assert obj.combination_sum([2], 1) == [], (
        "combination_sum no-solution case should be []"
    )
    print("Combination Sum OK")

    # Demo prints (compact)
    print("Factorial(5):", obj.factorial(5))
    print("Subsets [1,2]:", obj.subsets([1, 2]))
    print("Permutations [1,2,3]:", obj.permutations([1, 2, 3]))
    print("Combination Sum [2,3,6,7], 7:", obj.combination_sum([2, 3, 6, 7], 7))


if __name__ == "__main__":
    demonstrate_patterns()
