"""
Strings & Math Edge - Essential Coding Interview Problems

This module contains 9 carefully selected string and math problems that target
high-signal but less frequently practiced patterns (KMP/Z, rolling hash, bit
manipulation, modular arithmetic, and combinatorics). Each method is framed as an
actual interview-style question with examples, constraints, complexities, and hints.

Problem Difficulty Levels:
- Easy (3): Count Set Bits, Reverse Bits (32-bit), Modular Exponentiation
- Medium (6): KMP String Search, Z-Function, Rabin-Karp, Subsets via Bitmask,
              Modular Inverse, nCr modulo Prime

Implement the TODOs and then run `demonstrate_patterns()` for comprehensive sanity
checks including edge cases.
"""

from __future__ import annotations

from typing import List, Optional


class StringsMathEdge:
    """Essential string and math algorithms for coding interviews.

    Topics covered (Q1-Q9):
    - KMP prefix-function search
    - Z-function
    - Rolling hash (<PERSON><PERSON>-<PERSON>rp)
    - Bit manipulation: count set bits, reverse 32-bit integer, subsets via bitmask
    - Modular arithmetic and combinatorics: fast pow, modular inverse, nCr mod prime
    """

    # ---------- STRING ALGORITHMS ----------
    # Q1 ⭐⭐
    def kmp_search(self, text: str, pattern: str) -> int:
        """`
        PROBLEM: KMP String Search (Medium)

        Given two strings `text` and `pattern`, return the index of the first
        occurrence of `pattern` in `text`, or -1 if `pattern` is not present.
        Use the Knuth-Morris-Pratt (KMP) algorithm.

        Examples:
        - Input: text = "abracadabra", pattern = "cad" → Output: 4
        - Input: text = "aaaaa", pattern = "bba" → Output: -1
        - Input: text = "abc", pattern = "" → Output: 0 (empty pattern found at start)

        Constraints:
        - 0 <= len(text), len(pattern) <= 2 * 10^5
        - Return 0 if pattern is empty

        Time Complexity: O(n + m)
        Space Complexity: O(m) for LPS array

        Hints:
        1) Precompute longest-prefix-suffix (LPS) for pattern
        2) Use LPS to skip comparisons on mismatch
        3) Carefully handle empty pattern and pattern longer than text
        """
        # TODO: to review
        # Solution 1: Brute force solution
        if pattern == "":
            return 0

        if len(text) < len(pattern):
            return -1

        for start_idx in range(len(text) - len(pattern) + 1):
            for i in range(len(pattern)):
                if text[start_idx + i] != pattern[i]:
                    break
            else:
                return start_idx
        return -1

        ## Solution 2: KMP (build LPS, then scan)
        # if pattern == "":
        #     return 0
        # n, m = len(text), len(pattern)
        # if m > n:
        #     return -1
        # def build_lps(pat: str) -> List[int]:
        #     lps: List[int] = [0] * len(pat)
        #     length: int = 0
        #     i: int = 1
        #     while i < len(pat):
        #         if pat[i] == pat[length]:
        #             length += 1
        #             lps[i] = length
        #             i += 1
        #         else:
        #             if length != 0:
        #                 length = lps[length - 1]
        #             else:
        #                 lps[i] = 0
        #                 i += 1
        #     return lps
        # lps: List[int] = build_lps(pattern)
        # i: int = 0
        # j: int = 0
        # while i < n:
        #     if text[i] == pattern[j]:
        #         i += 1
        #         j += 1
        #         if j == m:
        #             return i - j
        #     else:
        #         if j != 0:
        #             j = lps[j - 1]
        #         else:
        #             i += 1
        # return -1

    # Q2 ⭐⭐
    def z_function(self, s: str) -> List[int]:
        """
        PROBLEM: Z-Function of a String (Medium)

        Return the Z-array for string `s` where Z[i] is the length of the longest
        common prefix between `s` and `s[i:]`. By convention Z[0] = 0.

        Examples:
        - Input: s = "aaaaa" → Output: [0, 4, 3, 2, 1]
        - Input: s = "aabxaabx" → Output: [0, 1, 0, 0, 4, 1, 0, 0]
        - Input: s = "" → Output: []

        Constraints:
        - 0 <= len(s) <= 2 * 10^5

        Time Complexity: O(n)
        Space Complexity: O(n)

        Hints:
        1) Maintain [L, R] window of the rightmost Z-box
        2) Use previously computed Z-values to avoid re-comparison
        """
        # Handle empty input
        if not s:
            return []

        n: int = len(s)
        z: List[int] = [0] * n  # z[i] = length of longest prefix starting at i

        # Current Z-box [left, right] matching the prefix
        left: int = 0
        right: int = 0

        for i in range(1, n):  # Z[0] stays 0 by convention
            if i <= right:
                # Reuse knowledge from the mirrored position i-left
                z[i] = min(right - i + 1, z[i - left])

            # Extend the match starting from the current z[i]
            while i + z[i] < n and s[z[i]] == s[i + z[i]]:
                z[i] += 1

            # Update Z-box if we extended beyond current right
            if i + z[i] - 1 > right:
                left = i
                right = i + z[i] - 1

        return z

    # Q3 ⭐⭐
    def rabin_karp_search(
        self, text: str, pattern: str, *, base: int = 256, mod: int = 10**9 + 7
    ) -> int:
        """
        PROBLEM: Rabin-Karp String Search (Medium)

        Return the index of the first occurrence of `pattern` in `text` using a
        rolling hash. If not found, return -1. Collisions must be handled by
        verifying potential matches via direct substring comparison.

        Examples:
        - Input: text = "abracadabra", pattern = "cad" → Output: 4
        - Input: text = "aaaaa", pattern = "bba" → Output: -1
        - Input: text = "abc", pattern = "" → Output: 0

        Constraints:
        - 0 <= len(text), len(pattern) <= 2 * 10^5
        - Use 64-bit-safe arithmetic; apply modulo `mod`

        Time Complexity: O(n) average; O(n*m) worst on adversarial collisions
        Space Complexity: O(1)

        Hints:
        1) Precompute base^(m-1) % mod for window removal
        2) Slide window: remove leading char, add trailing char
        3) On hash match, confirm by comparing substring
        """
        # Handle empty pattern per problem statement
        if pattern == "":
            return 0

        n, m = len(text), len(pattern)
        if m > n:
            return -1

        # Precompute base^(m-1) % mod for removing the leading char
        highest_power: int = 1
        for _ in range(m - 1):
            highest_power = (highest_power * base) % mod

        # Compute initial hashes for pattern and first window of text
        pat_hash: int = 0
        win_hash: int = 0
        for i in range(m):
            pat_hash = (pat_hash * base + ord(pattern[i])) % mod
            win_hash = (win_hash * base + ord(text[i])) % mod

        # Slide the window over the text
        for start in range(n - m + 1):
            # On hash match, verify by direct comparison to avoid collision errors
            if pat_hash == win_hash:
                if text[start : start + m] == pattern:
                    return start

            # Move window: remove leading char, add trailing char
            if start < n - m:
                lead = ord(text[start])
                next_char = ord(text[start + m])
                # Remove leading char's contribution
                win_hash = (win_hash - (lead * highest_power) % mod) % mod
                # Shift window and add trailing char
                win_hash = (win_hash * base + next_char) % mod

        return -1

    # ---------- BIT MANIPULATION ----------
    # Q4 ⭐
    def count_set_bits(self, n: int) -> int:
        """
        PROBLEM: Count Set Bits (Easy)

        Given a non-negative integer n (0 <= n < 2^31), return the number of 1-bits
        in its binary representation.

        Examples:
        - n = 0b0 → 0
        - n = 0b1 → 1
        - n = 0b101101 (45) → 4

        Constraints:
        - 0 <= n < 2^31

        Time Complexity: O(k) where k is the number of set bits (Brian Kernighan)
        Space Complexity: O(1)

        Hint: Repeatedly clear the lowest set bit: n &= (n - 1)
        """
        count = 0

        while n:
            n &= n - 1
            count += 1

        return count

    # Q5 ⭐
    def reverse_bits_32(self, n: int) -> int:
        """
        PROBLEM: Reverse Bits (32-bit Unsigned) (Easy)

        Reverse the bits of a 32-bit unsigned integer `n` and return the result
        as an integer in the range [0, 2^32 - 1].

        Examples:
        - n = 0 → 0
        - n = 1 → 2147483648 (1 << 31)
        - n = 0xFFFFFFFF → 0xFFFFFFFF

        Constraints:
        - 0 <= n <= 2^32 - 1

        Time Complexity: O(1) with 32 iterations
        Space Complexity: O(1)

        Hints:
        1) Iterate 32 times: shift result left, add last bit of n, shift n right
        2) Use bit masks and shifts; ensure unsigned behavior with masking
        """
        # Ensure 32-bit unsigned behavior
        n &= 0xFFFFFFFF

        result = 0
        for _ in range(32):
            # Shift result to make room, take LSB of n, then shift n right
            result = (result << 1) | (n & 1)
            n >>= 1

        return result & 0xFFFFFFFF

    # Q6 ⭐⭐
    def subsets_via_bitmask(self, nums: List[int]) -> List[List[int]]:
        """
        PROBLEM: Generate All Subsets (Power Set) via Bitmask (Medium)

        Given a list of distinct integers `nums`, return all possible subsets
        (the power set). The solution set must not contain duplicate subsets.

        Examples:
        - nums = [] → [[]]
        - nums = [1] → [[], [1]]
        - nums = [1, 2] → [[], [1], [2], [1, 2]]

        Constraints:
        - 0 <= len(nums) <= 16
        - Elements are distinct

        Time Complexity: O(n * 2^n)
        Space Complexity: O(2^n)

        Hint: Enumerate masks from 0..(1<<n)-1 and include nums[j] if j-th bit is set
        """
        # Enumerate all masks 0..(1<<n)-1; include nums[j] if j-th bit is set
        n: int = len(nums)
        total_masks: int = 1 << n
        subsets: List[List[int]] = []

        for mask in range(total_masks):
            subset: List[int] = []
            for j in range(n):
                if mask & (1 << j):  # If bit j is 1, include nums[j]
                    subset.append(nums[j])
            subsets.append(subset)

        return subsets

    # ---------- MODULAR ARITHMETIC & COMBINATORICS ----------
    # Q7 ⭐
    def mod_pow(self, a: int, e: int, mod: int) -> int:
        """
        PROBLEM: Modular Exponentiation (Easy)

        Compute (a^e) mod `mod` for integers a, e >= 0, and mod >= 1 using fast
        exponentiation (binary exponentiation).

        Examples:
        - a=2, e=10, mod=1000 → 24
        - a=2, e=0, mod=13 → 1
        - a=0, e=10, mod=13 → 0

        Constraints:
        - 0 <= a, e
        - 1 <= mod <= 10^9+7

        Time Complexity: O(log e)
        Space Complexity: O(1)

        Hint: Square-and-multiply; take modulo at each step to avoid overflow
        """
        # Validate inputs based on constraints
        if mod <= 0:
            raise ValueError("mod must be >= 1")
        if e < 0:
            raise ValueError("exponent e must be >= 0")

        # Normalize base within modulus to avoid large intermediates
        a %= mod

        # Initialize result as 1 (identity). Handle mod == 1 gracefully (returns 0).
        result: int = 1 % mod

        # Binary exponentiation: square-and-multiply
        while e > 0:
            if e & 1:
                result = (result * a) % mod
            a = (a * a) % mod
            e >>= 1

        return result

    # Q8 ⭐⭐
    def mod_inv(self, a: int, mod: int) -> Optional[int]:
        """
        PROBLEM: Modular Multiplicative Inverse (Medium)

        Return x such that (a * x) % mod == 1 if it exists; otherwise return None.
        If `mod` is prime and gcd(a, mod) == 1, use Fermat's little theorem:
        inverse = a^(mod-2) % mod. For non-prime modulus, use Extended Euclid.

        Examples:
        - a=3, mod=11 → 4 (since 3*4 % 11 = 1)
        - a=10, mod=17 → 12
        - a=2, mod=4 → None (no inverse when gcd(a, mod) != 1)

        Constraints:
        - 0 <= a < 10^18, 1 <= mod < 10^18

        Time Complexity: O(log mod) using fast pow or Extended Euclid
        Space Complexity: O(1)

        Hints:
        1) Check gcd(a, mod) == 1 before attempting inverse for non-prime mod
        2) For prime mod (e.g., 1e9+7), use a^(mod-2) % mod
        """
        # Validate inputs
        if mod <= 0:
            raise ValueError("mod must be >= 1")

        # Reduce a into [0, mod-1]
        a %= mod
        if a == 0:
            return None  # 0 has no multiplicative inverse modulo mod

        # Extended Euclidean Algorithm to solve a*x + mod*y = gcd(a, mod)
        # If gcd == 1, x is the modular inverse of a modulo mod.
        old_r, r = a, mod
        old_s, s = 1, 0  # Coefficients for 'a'
        old_t, t = 0, 1  # Coefficients for 'mod'

        while r != 0:
            q = old_r // r
            old_r, r = r, old_r - q * r
            old_s, s = s, old_s - q * s
            old_t, t = t, old_t - q * t

        gcd_val = old_r
        x_coeff = old_s  # Satisfies: a*x_coeff + mod*y_coeff = gcd_val

        if gcd_val != 1:
            return None  # Inverse doesn't exist when a and mod are not coprime

        # Normalize into [0, mod-1]
        return x_coeff % mod

    # Q9 ⭐⭐
    def nCr_mod(
        self, n: int, r: int, mod: int, *, precompute_limit: Optional[int] = None
    ) -> int:
        """
        PROBLEM: Compute nCr modulo a Prime (Medium)

        Compute C(n, r) % mod where mod is a prime number. If provided,
        `precompute_limit` indicates the maximum n for which factorials and inverse
        factorials should be precomputed. Return 0 if r < 0 or r > n.

        Examples:
        - n=5, r=2, mod=1e9+7 → 10
        - n=0, r=0, mod=1e9+7 → 1
        - n=5, r=6, mod=1e9+7 → 0

        Constraints:
        - 0 <= n <= 10^6 (when using precomputation)
        - mod is prime

        Time Complexity: O(1) per query after O(L) precompute
        Space Complexity: O(L) for factorial tables

        Hints:
        1) Precompute fact[i] and inv_fact[i] up to L using mod inverse
        2) C(n, r) = fact[n] * inv_fact[r] % mod * inv_fact[n-r] % mod
        """
        # Handle invalid or out-of-range r
        if r < 0 or r > n:
            return 0

        if mod <= 1:
            raise ValueError("mod must be a prime >= 2")

        # Use symmetry to minimize computations
        r = min(r, n - r)
        if r == 0:
            return 1 % mod

        # Determine precompute size L
        L: int = max(n, precompute_limit or n)

        # Lazy-init cache: maps (mod, L) -> (fact, inv_fact)
        if not hasattr(self, "_ncr_cache"):
            self._ncr_cache = {}

        cache_key = (mod, L)
        if cache_key not in self._ncr_cache:
            # Build factorials up to L: fact[i] = i! % mod
            fact: List[int] = [1] * (L + 1)
            for i in range(1, L + 1):
                fact[i] = (fact[i - 1] * i) % mod

            # Compute inv_fact[L] via Fermat's little theorem, then backfill
            inv_fact: List[int] = [1] * (L + 1)
            inv_fact[L] = self.mod_pow(fact[L], mod - 2, mod)
            for i in range(L, 0, -1):
                inv_fact[i - 1] = (inv_fact[i] * i) % mod

            self._ncr_cache[cache_key] = (fact, inv_fact)

        fact, inv_fact = self._ncr_cache[cache_key]
        if n > L:
            # If n exceeds cached limit (because precompute_limit < n at earlier cache),
            # rebuild with larger L to cover n.
            L = n
            fact = [1] * (L + 1)
            for i in range(1, L + 1):
                fact[i] = (fact[i - 1] * i) % mod
            inv_fact = [1] * (L + 1)
            inv_fact[L] = self.mod_pow(fact[L], mod - 2, mod)
            for i in range(L, 0, -1):
                inv_fact[i - 1] = (inv_fact[i] * i) % mod
            self._ncr_cache[(mod, L)] = (fact, inv_fact)

        # C(n, r) = fact[n] * inv_fact[r] * inv_fact[n-r] mod
        return ((fact[n] * inv_fact[r]) % mod * inv_fact[n - r]) % mod


def demonstrate_patterns() -> None:
    """Comprehensive demo and edge-case harness for all 9 problems.

    This function prints results and expected values for each problem. Most
    implementations are left as TODOs; use this as a verification harness after
    completing the methods. Order of subsets or points is not enforced; comparisons
    use sets when necessary.
    """
    s = StringsMathEdge()
    print("=== COMPREHENSIVE STRINGS & MATH EDGE TESTING ===\n")

    # Q1: KMP String Search
    print("1. KMP String Search (Medium ★★):")
    kmp_cases = [
        ("abracadabra", "cad", 4),  # Middle
        ("hello", "ll", 2),  # Middle
        ("aaaaa", "bba", -1),  # Not found
        ("abc", "", 0),  # Empty pattern
        ("", "a", -1),  # Empty text
        ("a", "a", 0),  # Single char match
        ("aaaab", "aaab", 1),  # Overlap
        ("abc", "abcd", -1),  # Pattern longer than text
    ]
    for i, (text, pat, expected) in enumerate(kmp_cases):
        got = s.kmp_search(text, pat)
        status = "✅" if got == expected else "❌"
        print(
            f"   Test {i + 1}: text='{text}', pattern='{pat}' → {got} (exp: {expected}) {status}"
        )

    # Q2: Z-Function
    print("\n2. Z-Function (Medium ★★):")
    z_cases = [
        ("", []),
        ("a", [0]),
        ("aaaaa", [0, 4, 3, 2, 1]),
        ("aabxaabx", [0, 1, 0, 0, 4, 1, 0, 0]),
        ("abacaba", [0, 0, 1, 0, 3, 0, 1]),
    ]
    for i, (st, expected) in enumerate(z_cases):
        got = s.z_function(st)
        status = "✅" if got == expected else "❌"
        print(f"   Test {i + 1}: s='{st}' → {got} (exp: {expected}) {status}")

    # Q3: Rabin–Karp String Search
    print("\n3. Rabin–Karp String Search (Medium ★★):")
    rk_cases = [
        ("abracadabra", "cad", 4),
        ("aaaaa", "bba", -1),
        ("abc", "", 0),
        ("", "a", -1),
        ("aaaab", "aaab", 1),
    ]
    for i, (text, pat, expected) in enumerate(rk_cases):
        got = s.rabin_karp_search(text, pat)
        status = "✅" if got == expected else "❌"
        print(
            f"   Test {i + 1}: text='{text}', pattern='{pat}' → {got} (exp: {expected}) {status}"
        )

    # Q4: Count Set Bits
    print("\n4. Count Set Bits (Easy ★):")
    csb_cases = [
        (0, 0),
        (1, 1),
        (0b101101, 4),  # 45
        ((1 << 31) - 1, 31),  # All ones in 31 bits
        (1 << 20, 1),
    ]
    for i, (n, expected) in enumerate(csb_cases):
        got = s.count_set_bits(n)
        status = "✅" if got == expected else "❌"
        print(f"   Test {i + 1}: n={n} → {got} (exp: {expected}) {status}")

    # Q5: Reverse Bits (32-bit)
    print("\n5. Reverse Bits 32-bit (Easy ★):")
    rb_cases = [
        (0, 0),
        (1, 1 << 31),
        (0xFFFFFFFF, 0xFFFFFFFF),
        (0b10, 1 << 30),
        (0b1001, (1 << 31) | (1 << 28)),
    ]
    for i, (n, expected) in enumerate(rb_cases):
        got = s.reverse_bits_32(n)
        status = "✅" if got == expected else "❌"
        print(f"   Test {i + 1}: n={n} → {got} (exp: {expected}) {status}")

    # Q6: Subsets via Bitmask
    print("\n6. Subsets via Bitmask (Medium ★★):")

    def to_set_of_tuples(lst: List[List[int]]):
        return {tuple(sorted(x)) for x in lst}

    subsets_cases = [
        ([], [[]]),
        ([1], [[], [1]]),
        ([1, 2], [[], [1], [2], [1, 2]]),
        ([1, 2, 3], [[], [1], [2], [3], [1, 2], [1, 3], [2, 3], [1, 2, 3]]),
    ]
    for i, (nums, expected) in enumerate(subsets_cases):
        got = s.subsets_via_bitmask(nums)
        status = "✅" if to_set_of_tuples(got) == to_set_of_tuples(expected) else "❌"
        print(f"   Test {i + 1}: nums={nums} → {got} (exp: {expected}) {status}")

    # Q7: Modular Exponentiation
    print("\n7. Modular Exponentiation (Easy ★):")
    mod_pow_cases = [
        (2, 10, 1000, 24),
        (2, 0, 13, 1),
        (0, 10, 13, 0),
        (10, 9, 6, 4),
    ]
    for i, (a, e, mod, expected) in enumerate(mod_pow_cases):
        got = s.mod_pow(a, e, mod)
        status = "✅" if got == expected else "❌"
        print(
            f"   Test {i + 1}: a={a}, e={e}, mod={mod} → {got} (exp: {expected}) {status}"
        )

    # Q8: Modular Inverse
    print("\n8. Modular Inverse (Medium ★★):")
    mod_inv_cases = [
        (3, 11, 4),
        (10, 17, 12),
        (2, 4, None),
        (1, 7, 1),
        (0, 7, None),
    ]
    for i, (a, mod, expected) in enumerate(mod_inv_cases):
        got = s.mod_inv(a, mod)
        status = "✅" if got == expected else "❌"
        print(f"   Test {i + 1}: a={a}, mod={mod} → {got} (exp: {expected}) {status}")

    # Q9: nCr modulo Prime
    print("\n9. nCr modulo Prime (Medium ★★):")
    MOD = 10**9 + 7
    ncr_cases = [
        (5, 2, MOD, None, 10),
        (0, 0, MOD, None, 1),
        (5, 0, MOD, None, 1),
        (5, 6, MOD, None, 0),
        (1000, 1, MOD, None, 1000),
        (10, 3, MOD, 10, 120),  # With precompute limit
    ]
    for i, (n, r, mod, limit, expected) in enumerate(ncr_cases):
        got = s.nCr_mod(n, r, mod, precompute_limit=limit)
        status = "✅" if got == expected else "❌"
        print(
            f"   Test {i + 1}: n={n}, r={r}, mod={mod}, precompute_limit={limit} → {got} (exp: {expected}) {status}"
        )

    print("\n" + "=" * 80)
    print("TEST HARNESS SUMMARY:")
    print("- ✅ When implementations are complete, all tests should pass")
    print("- 🎯 Covers: empty inputs, single elements, overlaps, not-found, boundaries")
    print("- 🧮 Includes: KMP, Z, Rabin-Karp, bit tricks, subsets, mod pow/inv, nCr")


if __name__ == "__main__":
    demonstrate_patterns()
