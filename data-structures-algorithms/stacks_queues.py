"""
Stacks and Queues - Essential Patterns for Coding Interviews

This module covers stack and queue-based algorithms frequently tested
in coding interviews at top tech companies like Google.

🔥 REQUIRED PROBLEMS (Grouped by Topic):

Parentheses & Validation:
- Q1 is_valid_parentheses - Foundation of all stack problems

Monotonic Stack:
- Q2 next_greater_element - Core monotonic stack concept
- Q3 next_greater_element_circular - Important variant
- Q4 largest_rectangle_histogram - Classic hard problem
- Q5 trapping_rain_water - Extremely common with multiple approaches

Monotonic Queue / Deque:
- Q6 max_sliding_window - Essential sliding window + deque pattern

Expression Evaluation:
- Q7 evaluate_reverse_polish - Classic stack evaluation
- Q8 basic_calculator - Important parsing + stack combination

String Decoding / Parsing:
- Q9 decode_string - Common recursive/stack pattern

Design:
- Q10 MinStack (design) - Most common stack design problem


Key Patterns Covered:
1. Monotonic Stack/Queue
2. Valid Parentheses and Variants
3. Next Greater/Smaller Element
4. Expression Evaluation
5. Sliding Window Maximum
6. Design Problems (Min Stack, etc.)

Time/Space Complexity Analysis and Use Cases included for each pattern.
"""

from typing import List, Optional
from collections import deque


class StackQueuePatterns:
    """Collection of essential stack and queue algorithms for coding interviews."""

    # ========== BASIC STACK OPERATIONS ==========
    # Q1 - Done
    def is_valid_parentheses(self, s: str) -> bool:
        """
        🔥 REQUIRED: Check if parentheses are valid and properly closed.

        PROBLEM STATEMENT:
        Given a string containing just the characters '(', ')', '{', '}', '[' and ']',
        determine if the input string is valid. A string is valid if:
        1. Open brackets must be closed by the same type of brackets
        2. Open brackets must be closed in the correct order
        3. Every close bracket has a corresponding open bracket of the same type

        Examples:
        - "()" → True
        - "()[]{}" → True
        - "(]" → False
        - "([)]" → False
        - "{[]}" → True

        Time Complexity: O(n)
        Space Complexity: O(n)

        Classic stack problem: push opening, pop and match closing.
        Essential pattern for 90% of stack problems.
        """

        stack = []
        mapping = {")": "(", "]": "[", "}": "{"}

        for char in s:
            if char in mapping:  # Closing bracket
                if not stack or stack.pop() != mapping[char]:
                    return False
            else:  # Opening bracket
                stack.append(char)

        return not stack

    # ========== MONOTONIC STACK ==========
    # Q2 - Done
    def next_greater_element(self, nums: List[int]) -> List[int]:
        """
        🔥 REQUIRED: Find next greater element for each element in array.

        PROBLEM STATEMENT:
        Given an array of integers, find the next greater element for each element.
        The next greater element is the first element to the right that is larger
        than the current element. If no such element exists, return -1.

        Examples:
        - [4, 5, 2, 25] → [5, 25, 25, -1]
        - [13, 7, 6, 12] → [-1, 12, 12, -1]
        - [1, 3, 2, 4] → [3, 4, 4, -1]

        Time Complexity: O(n)
        Space Complexity: O(n)

        Monotonic decreasing stack: pop smaller elements when larger found.
        Foundation for all monotonic stack problems.
        """
        # TODO: Implement this function for practice.

        result = [-1] * len(nums)
        stack: List[int] = []  # keeps monotonic decreasing stack!

        for i, num in enumerate(nums):
            while stack and nums[stack[-1]] < num:
                idx = stack.pop()
                result[idx] = num
            stack.append(i)

        return result

    # Q3 - Done
    def next_greater_element_circular(self, nums: List[int]) -> List[int]:
        """
        🔥 REQUIRED: Next greater element in circular array.

        PROBLEM STATEMENT:
        Given a circular array (the next element of the last element is the first element),
        find the next greater element for every element. If no such element exists, return -1.

        Examples:
        - [1, 2, 1] → [2, -1, 2] (for index 2, we go back to index 0)
        - [1, 2, 3, 4, 3] → [2, 3, 4, -1, 4]
        - [5, 4, 3, 2, 1] → [-1, 5, 5, 5, 5]

        Time Complexity: O(n)
        Space Complexity: O(n)

        Process array twice to simulate circular behavior.
        Important variant that tests deeper understanding.
        """

        n = len(nums)
        result = [-1] * n
        stack: List[int] = []  # indices, maintain monotonic decreasing by value

        for i in range(2 * n):
            j = i % n  # wrap-around index
            while stack and nums[stack[-1]] < nums[j]:
                idx = stack.pop()
                result[idx] = nums[j]
            if i < n:
                stack.append(j)
        return result

    # Q4
    def largest_rectangle_histogram(self, heights: List[int]) -> int:
        """
        🔥 REQUIRED: Find largest rectangle area in histogram.

        PROBLEM STATEMENT:
        Given an array of integers heights representing the histogram's bar height where
        the width of each bar is 1, return the area of the largest rectangle in the histogram.

        Examples:
        - [2,1,5,6,2,3] → 10 (rectangle with height 5 and width 2)
        - [2,4] → 4 (rectangle with height 2 and width 2, or height 4 and width 1)
        - [1] → 1
        - [0] → 0

        Time Complexity: O(n)
        Space Complexity: O(n)

        Monotonic increasing stack: calculate area when smaller height found.
        Classic hard problem - appears frequently in FAANG interviews.
        """

        heights = heights + [0]
        stack = []
        max_area = 0
        for i, val in enumerate(heights):
            if not stack or val >= heights[stack[-1]]:
                stack.append(i)
            else:
                while stack and heights[stack[-1]] > val:
                    top = stack.pop()
                    width = i if not stack else i - stack[-1] - 1
                    area = width * heights[top]
                    max_area = max(max_area, area)
                stack.append(i)
        return max_area

    # Q5
    def trapping_rain_water(self, height: List[int]) -> int:
        """
        🔥 REQUIRED: Calculate trapped rainwater between bars.

        PROBLEM STATEMENT:
        Given n non-negative integers representing an elevation map where the width
        of each bar is 1, compute how much water it can trap after raining.

        Examples:
        - [0,1,0,2,1,0,1,3,2,1,2,1] → 6
        - [4,2,0,3,2,5] → 9
        - [3,0,2,0,4] → 10

        Visual: heights = [3,0,2,0,4]
        |   |
        |W W|
        |W_W|
        |   |
        Water trapped = 5 units (W represents trapped water)

        Time Complexity: O(n)
        Space Complexity: O(n) for stack approach, O(1) for two pointers

        Stack approach: Store indices of bars in decreasing order.
        Extremely common problem with multiple solution approaches.
        """
        # Method 1: Two-pointer O(n) solution with O(1) space
        # if not height:
        #     return 0

        # left, right = 0, len(height)-1
        # left_max, right_max = 0, 0
        # water = 0

        # while left <= right:
        #     if height[left] <= height[right]:
        #         if height[left] >= left_max:
        #             left_max = height[left]
        #         else:
        #             water += left_max - height[left]
        #         left += 1
        #     else:
        #         if height[right] >= right_max:
        #             right_max = height[right]
        #         else:
        #             water += right_max - height[right]
        #         right -= 1

        # return water

        # Method 2: Stack-based approach (O(n) time, O(n) space)
        # More intuitive explanation:
        # Stack stores indices of bars in decreasing order (monotonic decreasing stack)
        # When we find a bar taller than the bar at stack top, we can calculate water
        # trapped between the current bar and the previous bar in the stack

        stack = []
        water = 0

        for i, current_height in enumerate(height):
            while stack and current_height > height[stack[-1]]:
                trapped_bar_index = stack.pop()

                if not stack:
                    break

                left_boundary = stack[-1]
                right_boundary = i
                width = right_boundary - left_boundary - 1
                water_depth = (
                    min(height[left_boundary], current_height)
                    - height[trapped_bar_index]
                )
                water += width * water_depth
            stack.append(i)

        return water

    # ========== MONOTONIC QUEUE (SLIDING WINDOW MAXIMUM) ==========
    # Q6
    def max_sliding_window(self, nums: List[int], k: int) -> List[int]:
        """
        🔥 REQUIRED: Find maximum in each sliding window of size k.

        PROBLEM STATEMENT:
        Given an array nums and a sliding window of size k which moves from left to right,
        return the maximum element in each window position.

        Examples:
        - nums = [1,3,-1,-3,5,3,6,7], k = 3
          → [3,3,5,5,6,7]
          Window [1,3,-1] → max = 3
          Window [3,-1,-3] → max = 3
          Window [-1,-3,5] → max = 5
          Window [-3,5,3] → max = 5
          Window [5,3,6] → max = 6
          Window [3,6,7] → max = 7

        Time Complexity: O(n)
        Space Complexity: O(k)

        Monotonic decreasing deque: maintain indices of potential maximums.
        Essential sliding window + deque pattern for interviews.
        """

        if not nums or k == 0:
            return []

        queue = deque()
        result = []

        for i, num in enumerate(nums):
            # remove expired
            while queue and queue[0] <= i - k:
                queue.popleft()

            # remove smaller than current values
            while queue and nums[queue[-1]] < num:
                queue.pop()

            # add current value
            queue.append(i)

            # if window complete, start reading out largest value
            if i >= k - 1:
                result.append(nums[queue[0]])

        return result

    # ========== EXPRESSION EVALUATION ==========
    # Q7 - Done
    def evaluate_reverse_polish(self, tokens: List[str]) -> int:
        """
        🔥 REQUIRED: Evaluate Reverse Polish Notation expression.

        REVERSE POLISH NOTATION (RPN):
        Also called "postfix notation" - operators come AFTER operands.

        Examples:
        - Normal (infix):  3 + 4 = 7
        - RPN (postfix):   3 4 + = 7
        - Complex:         (3 + 4) * 5 = 35  →  3 4 + 5 * = 35

        ALGORITHM:
        1. Scan tokens left to right
        2. If number: push to stack
        3. If operator: pop 2 operands, compute, push result back
        4. Final stack contains one element (the answer)

        STEP-BY-STEP EXAMPLE:
        tokens = ["2", "1", "+", "3", "*"]

        Stack: []           Token: "2"    → Push 2
        Stack: [2]          Token: "1"    → Push 1
        Stack: [2, 1]       Token: "+"    → Pop 1,2 → Push (2+1=3)
        Stack: [3]          Token: "3"    → Push 3
        Stack: [3, 3]       Token: "*"    → Pop 3,3 → Push (3*3=9)
        Stack: [9]          Result: 9

        KEY INSIGHTS:
        - Operand order matters: for "a b -", result is a-b (not b-a)
        - Division truncates toward zero: int(6/-132) = 0, not -1
        - Stack perfectly handles the "last operator first" evaluation

        Args:
            tokens: List of strings - numbers and operators ["+", "-", "*", "/"]

        Returns:
            int: Result of RPN expression evaluation

        Examples:
            >>> evaluate_reverse_polish(["2", "1", "+", "3", "*"])
            9
            >>> evaluate_reverse_polish(["4", "13", "5", "/", "+"])
            6
            >>> evaluate_reverse_polish(["10", "6", "9", "3", "+", "-11", "*", "/", "*", "17", "+", "5", "+"])
            22

        Time Complexity: O(n) - Single pass through tokens
        Space Complexity: O(n) - Stack can hold up to n/2 numbers in worst case

        Classic stack problem that tests parsing and evaluation skills.
        Foundation for calculators, compilers, and expression processors.
        """

        stack = []
        operators = {"+", "-", "*", "/"}

        for token in tokens:
            if token in operators:
                # Pop operands (order matters: second operand first, then first operand)
                b = stack.pop()
                a = stack.pop()

                if token == "+":
                    stack.append(a + b)
                elif token == "-":
                    stack.append(a - b)
                elif token == "*":
                    stack.append(a * b)
                else:  # token == '/'
                    # Truncate toward zero for division
                    stack.append(int(a / b))
            else:
                # It's a number, convert and push to stack
                stack.append(int(token))

        assert len(stack) == 1, (
            "Invalid RPN expression - stack should have exactly one element"
        )
        return stack[0]

    # Q8
    def basic_calculator(self, s: str) -> int:
        """
        🔥 REQUIRED: Basic calculator with +, -, and parentheses.

        PROBLEM STATEMENT:
        Given a string s representing a valid expression with integers, '+', '-',
        and parentheses '(' and ')', evaluate the expression and return the result.

        Examples:
        - "1 + 1" → 2
        - " 2-1 + 2 " → 3
        - "(1+(4+5+2)-3)+(6+8)" → 23
        - "2-(5-6)" → 3
        - "-(2+3)" → -5

        Rules:
        - No multiplication or division
        - Expression is always valid
        - May contain spaces (ignore them)
        - Numbers can be multi-digit

        Time Complexity: O(n)
        Space Complexity: O(n)

        Stack to handle parentheses and sign changes.
        Important parsing problem that combines multiple concepts.
        """

        stack = []
        result = 0
        number = 0
        sign = 1  # the sign right before current position 1 for +, -1 for -

        for char in s:
            if char.isdigit():
                number = number * 10 + int(char)
            elif char in "+-":
                result += sign * number
                number = 0
                sign = 1 if char == "+" else -1
            elif char == "(":
                stack.append(result)
                stack.append(sign)
                result = 0
                sign = 1
            elif char == ")":
                result += sign * number
                number = 0  # consumed
                result *= stack.pop()  # Previous sign
                result += stack.pop()  # Previous result
            else:
                continue
        return result + sign * number

    # Q9
    def decode_string(self, s: str) -> str:
        """
        🔥 REQUIRED: Decode string with pattern k[encoded_string].

        PROBLEM STATEMENT:
        Given an encoded string, return its decoded string. The encoding rule is:
        k[encoded_string], where the encoded_string inside the brackets should be
        repeated exactly k times. k is guaranteed to be a positive integer.

        Examples:
        - "3[a]2[bc]" → "aaabcbc"
        - "2[abc]3[cd]ef" → "abcabccdcdcdef"
        - "abc3[cd]xyz" → "abccdcdcdxyz"
        - "2[b3[a]]" → "baaabaaa" (nested brackets)
        - "3[a2[c]]" → "accaccacc"

        Rules:
        - k is always positive
        - encoded_string contains only lowercase letters
        - Brackets are always balanced and valid
        - Can have nested brackets

        Time Complexity: O(n)
        Space Complexity: O(n)

        Stack to handle nested brackets.
        Common recursive/stack pattern with string manipulation.
        """
        stack = []
        current_string = ""
        current_num = 0

        for char in s:
            if char.isdigit():
                current_num = current_num * 10 + int(char)
            elif char == "[":
                # Save current context to stack
                stack.append(current_string)
                stack.append(current_num)
                current_string = ""
                current_num = 0
            elif char == "]":
                # Restore context and decode
                num = stack.pop()
                prev_string = stack.pop()
                current_string = prev_string + num * current_string
            else:  # char.isalpha()
                current_string += char

        return current_string


# ========== DESIGN PROBLEMS ==========
# Q10
class MinStack:
    """
    🔥 REQUIRED: Design a stack that supports retrieving the minimum element in O(1) time.

    PROBLEM STATEMENT:
    Implement a stack with the following operations, all in constant time:
      - push(x): Push element x onto stack.
      - pop(): Remove the element on top of the stack.
      - top(): Get the top element.
      - get_min(): Retrieve the minimum element in the stack.

    WHY THIS MATTERS:
    This is one of the most common stack design problems in interviews, testing your ability to augment data structures for efficient queries.

    APPROACHES:
    1. Two Stacks (Classic):
       - Use one stack to store all values.
       - Use a second stack to keep track of the current minimum after each push.
       - When pushing, also push the new min (min(x, min_stack[-1])) onto the min stack.
       - When popping, pop from both stacks.
       - get_min() is always the top of the min stack.

    2. Single Stack with Pairs:
       - Store tuples (value, current_min) in a single stack.
       - Each push records the value and the minimum at that point.
       - get_min() is always stack[-1][1].

    3. Space-Optimized (Advanced, not shown here):
       - Only push to min stack when a new minimum is encountered.
       - Pop from min stack only when the popped value equals the current min.

    USE CASES:
    - Efficiently track minimums in a dynamic dataset.
    - Useful in problems involving sliding windows, range queries, or undo operations.

    INTERVIEW TIPS:
    - Be ready to explain why two stacks are needed.
    - Discuss trade-offs in space and time.
    - Mention how to extend to get_max() or other aggregates.

    Time Complexity: All operations O(1)
    Space Complexity: O(n)
    """

    def __init__(self):
        self.stack = []
        self.min_stack = []

    def push(self, val: int) -> None:
        """Time: O(1)"""
        self.stack.append(val)
        if not self.min_stack or val <= self.min_stack[-1]:
            self.min_stack.append(val)

    def pop(self) -> None:
        """Time: O(1)"""
        if self.stack:
            val = self.stack.pop()
            if self.min_stack and val == self.min_stack[-1]:
                self.min_stack.pop()

    def top(self) -> Optional[int]:
        """Time: O(1)"""
        return self.stack[-1] if self.stack else None

    def get_min(self) -> Optional[int]:
        """Time: O(1)"""
        return self.min_stack[-1] if self.min_stack else None


def demonstrate_patterns():
    """Demonstrate usage of stack and queue patterns."""
    patterns = StackQueuePatterns()

    print("=== STACKS AND QUEUES DEMONSTRATIONS ===\n")

    # Valid parentheses
    print("1. Valid Parentheses:")
    test_cases = ["()", "()[]{}", "(]", "([)]", "{[]}"]
    for s in test_cases:
        result = patterns.is_valid_parentheses(s)
        print(f"   '{s}' -> {result}")

    # Next greater element
    print("\n2. Next Greater Element:")
    nums = [2, 1, 2, 4, 3, 1]
    result = patterns.next_greater_element(nums)
    print(f"   Array: {nums}")
    print(f"   Next Greater: {result}")

    # Largest rectangle in histogram
    print("\n3. Largest Rectangle in Histogram:")
    heights = [2, 1, 5, 6, 2, 3]
    area = patterns.largest_rectangle_histogram(heights)
    print(f"   Heights: {heights} -> Max Area: {area}")

    # Sliding window maximum
    print("\n4. Sliding Window Maximum:")
    nums = [1, 3, -1, -3, 5, 3, 6, 7]
    k = 3
    result = patterns.max_sliding_window(nums, k)
    print(f"   Array: {nums}, k={k}")
    print(f"   Window maximums: {result}")

    # Basic calculator
    print("\n5. Basic Calculator:")
    calc_cases = ["1 + 1", " 2-1 + 2 ", "(1+(4+5+2)-3)+(6+8)", "2-(5-6)"]
    for expr in calc_cases:
        result = patterns.basic_calculator(expr)
        print(f"   '{expr}' = {result}")

    # Decode string
    print("\n6. Decode String:")
    decode_cases = ["3[a]2[bc]", "2[abc]3[cd]ef", "abc3[cd]xyz", "2[b3[a]]", "3[a2[c]]"]
    for s in decode_cases:
        result = patterns.decode_string(s)
        print(f"   '{s}' -> '{result}'")

    # Min stack
    print("\n7. Min Stack:")
    min_stack = MinStack()
    operations = [3, 0, 4, 2]
    print("   Operations: push(3), push(0), push(4), push(2)")
    for val in operations:
        min_stack.push(val)
        print(f"   After push({val}): min = {min_stack.get_min()}")

    # ================== EMBEDDED TESTS (ASSERTIONS) ==================
    print("\n=== EMBEDDED TESTS (edge cases) ===")

    # 1) is_valid_parentheses
    parentheses_cases = {
        "": True,
        "()": True,
        "()[]{}": True,
        "(]": False,
        "([)]": False,
        "{[]}": True,
        "(": False,
        ")": False,
        "(((())))": True,
        "[": False,
        "([]{})": True,
    }
    for s, expected in parentheses_cases.items():
        assert patterns.is_valid_parentheses(s) == expected, (
            f"is_valid_parentheses failed for '{s}'"
        )
    print("- is_valid_parentheses: OK")

    # 2) next_greater_element
    nge_cases = [
        ([4, 5, 2, 25], [5, 25, 25, -1]),
        ([13, 7, 6, 12], [-1, 12, 12, -1]),
        ([1, 3, 2, 4], [3, 4, 4, -1]),
        ([], []),
        ([1], [-1]),
        ([5, 4, 3, 2, 1], [-1, -1, -1, -1, -1]),
        ([1, 2, 3, 4, 5], [2, 3, 4, 5, -1]),
        ([2, 2, 2], [-1, -1, -1]),
    ]
    for nums_case, expected in nge_cases:
        assert patterns.next_greater_element(nums_case) == expected, (
            f"next_greater_element failed for {nums_case}"
        )
    print("- next_greater_element: OK")

    # 3) next_greater_element_circular
    ngec_cases = [
        ([1, 2, 1], [2, -1, 2]),
        ([1, 2, 3, 4, 3], [2, 3, 4, -1, 4]),
        ([5, 4, 3, 2, 1], [-1, 5, 5, 5, 5]),
        ([1], [-1]),
        ([], []),
    ]
    for nums_case, expected in ngec_cases:
        assert patterns.next_greater_element_circular(nums_case) == expected, (
            f"next_greater_element_circular failed for {nums_case}"
        )
    print("- next_greater_element_circular: OK")

    # 4) largest_rectangle_histogram
    lrh_cases = [
        ([2, 1, 5, 6, 2, 3], 10),
        ([2, 4], 4),
        ([1], 1),
        ([0], 0),
        ([5, 5, 5], 15),
        ([1, 2, 3, 4], 6),
        ([4, 3, 2, 1], 6),
        ([], 0),
    ]
    for heights_case, expected in lrh_cases:
        assert patterns.largest_rectangle_histogram(heights_case) == expected, (
            f"largest_rectangle_histogram failed for {heights_case}"
        )
    print("- largest_rectangle_histogram: OK")

    # 5) trapping_rain_water
    trw_cases = [
        ([0, 1, 0, 2, 1, 0, 1, 3, 2, 1, 2, 1], 6),
        ([4, 2, 0, 3, 2, 5], 9),
        ([], 0),
        ([1], 0),
        ([1, 2, 3, 4], 0),
        ([4, 3, 2, 1], 0),
        ([3, 0, 2, 0, 4], 7),
        ([2, 0, 2], 2),
        ([2, 1, 1, 2], 2),
    ]
    for heights_case, expected in trw_cases:
        assert patterns.trapping_rain_water(heights_case) == expected, (
            f"trapping_rain_water failed for {heights_case}"
        )
    print("- trapping_rain_water: OK")

    # 6) evaluate_reverse_polish
    rpn_cases = [
        (["2", "1", "+", "3", "*"], 9),
        (["4", "13", "5", "/", "+"], 6),
        (["10", "6", "9", "3", "+", "-11", "*", "/", "*", "17", "+", "5", "+"], 22),
        (["6", "-132", "/"], 0),  # trunc toward zero
    ]
    for tokens, expected in rpn_cases:
        assert patterns.evaluate_reverse_polish(tokens) == expected, (
            f"evaluate_reverse_polish failed for {tokens}"
        )
    print("- evaluate_reverse_polish: OK")

    # 7) MinStack
    ms = MinStack()
    assert ms.top() is None and ms.get_min() is None
    ms.push(3)
    assert ms.get_min() == 3 and ms.top() == 3
    ms.push(0)
    assert ms.get_min() == 0 and ms.top() == 0
    ms.push(4)
    assert ms.get_min() == 0 and ms.top() == 4
    ms.push(2)
    assert ms.get_min() == 0 and ms.top() == 2
    ms.pop()
    assert ms.get_min() == 0 and ms.top() == 4
    ms.pop()
    assert ms.get_min() == 0 and ms.top() == 0
    ms.pop()
    assert ms.get_min() == 3 and ms.top() == 3
    ms.pop()
    assert ms.get_min() is None and ms.top() is None
    print("- MinStack: OK")

    # 8) basic_calculator
    calc_cases = [
        ("1 + 1", 2),
        (" 2-1 + 2 ", 3),
        ("(1+(4+5+2)-3)+(6+8)", 23),
        ("2-(5-6)", 3),
        ("-(2+3)", -5),
    ]
    for expr, expected in calc_cases:
        assert patterns.basic_calculator(expr) == expected, (
            f"basic_calculator failed for '{expr}'"
        )
    print("- basic_calculator: OK")

    # 9) decode_string
    decode_cases = [
        ("3[a]2[bc]", "aaabcbc"),
        ("2[abc]3[cd]ef", "abcabccdcdcdef"),
        ("abc3[cd]xyz", "abccdcdcdxyz"),
        ("2[b3[a]]", "baaabaaa"),
        ("3[a2[c]]", "accaccacc"),
    ]
    for s, expected in decode_cases:
        assert patterns.decode_string(s) == expected, f"decode_string failed for '{s}'"
    print("- decode_string: OK")

    # 10) max_sliding_window
    msw_cases = [
        ([1, 3, -1, -3, 5, 3, 6, 7], 3, [3, 3, 5, 5, 6, 7]),
        ([1], 1, [1]),
        ([1, -1], 1, [1, -1]),
        ([9, 11], 2, [11]),
        ([4, -2], 2, [4]),
    ]
    for nums_case, k, expected in msw_cases:
        assert patterns.max_sliding_window(nums_case, k) == expected, (
            f"max_sliding_window failed for {nums_case}, k={k}"
        )
    print("- max_sliding_window: OK")

    print("\n🎉 All stack and queue patterns working correctly!")


if __name__ == "__main__":
    demonstrate_patterns()
