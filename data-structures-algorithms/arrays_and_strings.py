"""
A<PERSON><PERSON> and Strings - Core Patterns for Coding Interviews

This module covers the most important array and string manipulation techniques
frequently tested in coding interviews, especially at companies like Google.

Key Patterns Covered:
1. Two Pointers Technique
2. Sliding Window
3. Prefix Sums
4. Binary Search (on arrays)

Time/Space Complexity Analysis and Use Cases included for each pattern.
"""

from typing import List


class ArrayStringPatterns:
    """Collection of essential array and string algorithms for coding interviews."""
    
    # ========== TWO POINTERS TECHNIQUE ==========
    # Exercise 1
    # PRIORITY: Foundational two-pointers pattern; very common warm-up.
    def two_sum_sorted(self, nums: List[int], target: int) -> List[int]:
        """
        Two Sum on sorted array using two pointers.

        Problem:
            Given a non-decreasing (sorted) array of integers `nums` and an integer
            `target`, return the indices (0-based) of the two numbers such that they
            add up to `target`. Assume exactly one valid pair exists, and you may not
            use the same element twice.

        Args:
            nums: Sorted list of integers (non-decreasing order).
            target: Integer target sum.

        Returns:
            A list with two integers [i, j] (i < j) representing indices in `nums`.

        Example:
            nums = [2, 7, 11, 15], target = 9 -> returns [0, 1]

        Time Complexity:
            O(n)

        Space Complexity:
            O(1)

        Notes:
            Use two pointers starting at both ends; move the left pointer rightward
            if the current sum is too small, otherwise move the right pointer leftward.
        """
        left, right = 0, len(nums) - 1
        
        while left < right:
            current_sum = nums[left] + nums[right]
            if current_sum == target:
                return [left, right]
            elif current_sum < target:
                left += 1
            else:
                right -= 1
        
        return [-1, -1]

        # Key Takeaways:
        # 1. **Two-pointer technique**: Start from opposite ends, move based on comparison
        # 2. **Loop condition**: Use `while left < right` to prevent pointer crossing/overlap
        # 3. **Pointer movement**: Move left pointer right if sum too small, right pointer left if too big
        # 4. **Return consistency**: Use [-1, -1] for "not found" to match expected return type
        # 5. **Variable naming**: `left`/`right` more descriptive than `i`/`j`
        # 6. **Sorted array advantage**: Eliminates need for hashmap, achieves O(1) space
    
    # Exercise 2
    def reverse_string(self, s: List[str]) -> None:
        """
        Reverse string in-place using two pointers.

        Problem:
            Given a list of single-character strings `s`, reverse the list in-place
            so that the characters appear in the opposite order. Do not allocate
            extra arrays for the result.

        Args:
            s: Mutable list of single-character strings.

        Returns:
            None. The input list is modified in-place.

        Example:
            s = ["h", "e", "l", "l", "o"] -> after call, s becomes ["o", "l", "l", "e", "h"]

        Time Complexity:
            O(n)

        Space Complexity:
            O(1)
        """
        left, right = 0, len(s) - 1
        
        while left < right:
            s[left], s[right] = s[right], s[left]
            left += 1
            right -= 1

        # Key Takeaways:
        # 1. **In-place swapping**: Use Python's tuple unpacking for elegant element swapping
        # 2. **Two-pointer convergence**: Pointers move toward each other until they meet/cross
        # 3. **Loop termination**: `left < right` ensures we don't swap the same element twice
        # 4. **Space efficiency**: O(1) space by modifying original array instead of creating new one
        # 5. **Symmetry pattern**: Common technique for problems involving array/string reversal
        # 6. **Edge cases**: Empty arrays and single elements are handled naturally by the loop condition
    
    # Exercise 3
    def is_palindrome(self, s: str) -> bool:
        """
        Check if string is palindrome (ignoring case and non-alphanumeric).

        Problem:
            Given a string `s`, determine if it is a palindrome after converting
            all uppercase letters to lowercase and removing all non-alphanumeric
            characters. An empty string is considered a valid palindrome.

        Args:
            s: Input string to evaluate.

        Returns:
            True if `s` is a valid palindrome under the rules above; otherwise False.

        Examples:
            "A man, a plan, a canal: Panama" -> True
            "race a car" -> False

        Time Complexity:
            O(n)

        Space Complexity:
            O(1)

        Notes:
            Common follow-ups include allowing one deletion (Valid Palindrome II).
        """
        left, right = 0, len(s) - 1
        
        while left < right:
            # Skip non-alphanumeric characters from left
            while left < right and not s[left].isalnum():
                left += 1
            # Skip non-alphanumeric characters from right
            while left < right and not s[right].isalnum():
                right -= 1
            
            # Compare normalized characters
            if s[left].lower() != s[right].lower():
                return False
            
            left += 1
            right -= 1
        
        return True

        # Key Takeaways:
        # 1. **True O(1) space**: Skip invalid chars in-place instead of creating normalized string
        # 2. **Nested while loops**: Inner loops handle character skipping while maintaining bounds
        # 3. **Bounds checking**: Always check `left < right` in inner loops to prevent crossing
        # 4. **Character validation**: `.isalnum()` handles both letters and digits uniformly
        # 5. **Case normalization**: `.lower()` applied during comparison, not preprocessing
        # 6. **Early termination**: Return False immediately on first mismatch for efficiency
    
    # Exercise 4
    # PRIORITY: Classic two-pointers + dedup; high-yield interview question.
    def three_sum(self, nums: List[int]) -> List[List[int]]:
        """
        Find all unique triplets that sum to zero.

        Problem:
            Given an integer array `nums`, return all unique triplets [a, b, c]
            such that a + b + c == 0. Triplets must be returned in non-decreasing
            order within each triplet, and duplicate triplets must not be returned.

        Args:
            nums: List of integers (may contain negatives, zero, positives).

        Returns:
            A list of unique triplets (each a list of three integers) that sum to 0.

        Example:
            nums = [-1, 0, 1, 2, -1, -4] -> [[-1, -1, 2], [-1, 0, 1]]

        Time Complexity:
            O(n^2)

        Space Complexity:
            O(1) excluding the output list

        Notes:
            Sort, fix one element, and use two pointers for the remaining two,
            skipping duplicates throughout to avoid repeated triplets.
        """
        nums.sort()
        result = []
        
        for i in range(len(nums) - 2):
            # Skip duplicates for first element
            if i > 0 and nums[i] == nums[i - 1]:
                continue
            
            # Early termination optimization
            if nums[i] > 0:
                break
            
            left, right = i + 1, len(nums) - 1
            
            while left < right:
                current_sum = nums[i] + nums[left] + nums[right]
                
                if current_sum == 0:
                    result.append([nums[i], nums[left], nums[right]])
                    
                    # Skip duplicates for second and third elements
                    while left < right and nums[left] == nums[left + 1]:
                        left += 1
                    while left < right and nums[right] == nums[right - 1]:
                        right -= 1
                    
                    left += 1
                    right -= 1
                elif current_sum < 0:
                    left += 1
                else:
                    right -= 1
        
        return result

        # Key Takeaways:
        # 1. **Sort first**: Enables two-pointer technique and simplifies duplicate handling
        # 2. **Three-level deduplication**: Skip duplicates for first, second, and third elements
        # 3. **Fix-one pattern**: Fix first element, use two-pointer for remaining two elements
        # 4. **Early termination**: If first element > 0, no valid triplets possible (sorted array)
        # 5. **Nested pointer movement**: Move both pointers simultaneously when sum equals target
        # 6. **Conditional logic structure**: Use elif to ensure mutually exclusive pointer updates
    
    # ========== SLIDING WINDOW TECHNIQUE ==========
    # Exercise 5
    # PRIORITY: Core sliding window (fixed size); quick to implement.
    def max_sum_subarray_of_size_k(self, nums: List[int], k: int) -> int:
        """
        Find maximum sum of subarray of size k (fixed window).

        Problem:
            Given an integer array `nums` and an integer `k` (k > 0), return the
            maximum sum over all contiguous subarrays of length exactly k. If
            `k` is greater than the length of `nums`, return 0 (assumption for this
            implementation).

        Args:
            nums: List of integers.
            k: Length of the fixed-size sliding window.

        Returns:
            The maximum sum of any contiguous subarray of length k.

        Example:
            nums = [2, 1, 5, 1, 3, 2], k = 3 -> 9 (subarray [5, 1, 3])

        Time Complexity:
            O(n)

        Space Complexity:
            O(1)

        Notes:
            Compute the sum of the first window, then slide by removing the left
            element and adding the next right element.
        """
        if len(nums) < k:
            return 0
        
        # Calculate sum of first window
        window_sum = sum(nums[:k])
        max_sum = window_sum
        
        # Slide the window
        for i in range(k, len(nums)):
            window_sum += nums[i] - nums[i - k]
            max_sum = max(max_sum, window_sum)
        
        return max_sum

        # Key Takeaways:
        # 1. **Fixed window sliding**: Maintain constant window size while moving right
        # 2. **Incremental updates**: Add new element, remove old element (O(1) per slide)
        # 3. **Initial window**: Calculate first k elements sum before sliding begins
        # 4. **Index arithmetic**: Use `i - k` to reference the element leaving the window
        # 5. **Edge case handling**: Return early if array smaller than window size
        # 6. **O(n) efficiency**: Avoid O(n*k) brute force by reusing previous calculations
    
    # Exercise 6
    # PRIORITY: Essential variable-size sliding window; very frequently asked.
    def longest_substring_without_repeating(self, s: str) -> int:
        """
        Find length of longest substring without repeating characters.

        Problem:
            Given a string `s`, return the length of the longest substring that
            contains no repeating characters.

        Args:
            s: Input string.

        Returns:
            Length (integer) of the longest substring without duplicate characters.

        Examples:
            s = "abcabcbb" -> 3 (substring "abc")
            s = "bbbbb" -> 1 (substring "b")

        Time Complexity:
            O(n)

        Space Complexity:
            O(min(m, n)), where m is the charset size.

        Notes:
            Use a variable-size sliding window; when a duplicate is encountered,
            shrink the window from the left until the duplicate is removed.
        """
        last_seen = {}
        left = 0
        max_length = 0
        
        for right, ch in enumerate(s):
            if ch in last_seen and last_seen[ch] >= left:
                left = last_seen[ch] + 1
            
            last_seen[ch] = right
            curr_length = right - left + 1
            max_length = max(max_length, curr_length)
        
        return max_length

        # Key Takeaways:
        # 1. **Variable window sliding**: Window expands right, contracts left when duplicates found
        # 2. **HashMap optimization**: Track last seen index to jump left pointer efficiently
        # 3. **Boundary condition**: Check `last_seen[ch] >= left` to ensure relevance to current window
        # 4. **Continuous tracking**: Update hashmap and check max length on every iteration
        # 5. **No explicit duplicate removal**: Left pointer jump automatically excludes duplicates
        # 6. **Empty string handling**: Algorithm naturally returns 0 for empty input
    
    # Exercise 7
    # PRIORITY: Advanced sliding window with frequency maps; high signal.
    def min_window_substring(self, s: str, t: str) -> str:
        """
        Find minimum window substring containing all characters of t.

        Problem:
            Given two strings `s` and `t`, return the minimum window substring of
            `s` such that every character in `t` (including multiplicity) is included
            in the window. If there is no such substring, return an empty string.

        Args:
            s: The source string.
            t: The target string whose characters must all appear in the window.

        Returns:
            The smallest substring of `s` that contains all characters of `t`.

        Example:
            s = "ADOBECODEBANC", t = "ABC" -> "BANC"

        Time Complexity:
            O(|s| + |t|)

        Space Complexity:
            O(|s| + |t|)

        Notes:
            Use a sliding window with frequency maps. Expand until valid, then
            contract from the left to minimize while maintaining validity.
        """
        if not s or not t or len(s) < len(t):
            return ""
        
        # Frequency map of characters in t
        dict_t = {}
        for char in t:
            dict_t[char] = dict_t.get(char, 0) + 1
        
        required = len(dict_t)  # Number of unique chars in t
        formed = 0  # Number of unique chars in current window with desired frequency
        
        window_counts = {}
        left, right = 0, 0
        
        # ans = (window length, left, right)
        ans = float("inf"), None, None
        
        while right < len(s):
            # Add one character from right to the window
            char = s[right]
            window_counts[char] = window_counts.get(char, 0) + 1
            
            # Check if this character's frequency matches the desired count in t
            if char in dict_t and window_counts[char] == dict_t[char]:
                formed += 1
            
            # Try to contract window until it's no longer valid
            while left <= right and formed == required:
                char = s[left]
                
                # Update the result if this window is smaller
                if right - left + 1 < ans[0]:
                    ans = (right - left + 1, left, right)
                
                # Remove char from left
                window_counts[char] -= 1
                if char in dict_t and window_counts[char] < dict_t[char]:
                    formed -= 1
                
                left += 1
            
            right += 1
        
        return "" if ans[0] == float("inf") or ans[1] is None else s[ans[1]:ans[2] + 1]

        # Key Takeaways:
        # 1. **Two-phase sliding window**: Expand right until valid, contract left while maintaining validity
        # 2. **Frequency matching**: Track when window character counts match target requirements
        # 3. **Formed counter optimization**: Count unique characters meeting frequency, not total matches
        # 4. **Nested contraction loop**: Inner while loop minimizes window when condition is satisfied
        # 5. **Result tracking**: Store best window as tuple (length, left, right) for easy comparison
        # 6. **Edge case handling**: Check for empty strings and impossible cases upfront
    
    # ========== PREFIX SUMS ==========
    # Exercise 8
    def range_sum_query(self, nums: List[int]):
        """
        Preprocess array to answer range sum queries in O(1).

        Problem:
            Build a data structure over `nums` to support answering sum queries of
            the form sum of elements in the inclusive range [left, right] in O(1)
            time after an O(n) preprocessing step.

        Args:
            nums: List of integers to preprocess.

        Returns:
            None. Stores internal prefix sums for later calls to `sum_range`.

        Example:
            After `range_sum_query([1,2,3,4])`, `sum_range(1, 3)` should return 9.

        Preprocessing Time:
            O(n)

        Query Time:
            O(1)

        Space:
            O(n)

        Notes:
            Use prefix sums where prefix[i] is the sum of the first i elements.
        """
        self.prefix_sums = [0]  # Start with 0 for empty sum
        
        for num in nums:
            self.prefix_sums.append(self.prefix_sums[-1] + num)

        # Key Takeaways:
        # 1. **Zero-indexed prefix**: Start with [0] to handle empty prefix elegantly
        # 2. **Cumulative building**: Each prefix sum includes all previous elements
        # 3. **One-pass preprocessing**: Build entire prefix array in O(n) time
        # 4. **Index mapping**: prefix_sums[i] represents sum of first i elements
        # 5. **Range query formula**: sum[left:right] = prefix[right+1] - prefix[left]
        # 6. **Space-time tradeoff**: Use O(n) space to achieve O(1) query time
    
    # Exercise 9
    def sum_range(self, left: int, right: int) -> int:
        """
        Query range sum [left, right] inclusive.

        Problem:
            After calling `range_sum_query(nums)` to preprocess, return the sum of
            elements in the inclusive index range [left, right]. Indices are 0-based.

        Args:
            left: Left index of the range (inclusive, 0-based).
            right: Right index of the range (inclusive, 0-based).

        Returns:
            Integer sum of elements from index left through right.

        Example:
            If nums = [1,2,3,4] was preprocessed, sum_range(1, 3) -> 9.

        Notes:
            Expected to be called only after `range_sum_query` has been invoked to
            build prefix sums.
        """
        return self.prefix_sums[right + 1] - self.prefix_sums[left]

        # Key Takeaways:
        # 1. **Simple subtraction formula**: prefix[right+1] - prefix[left] gives range sum
        # 2. **Index offset handling**: +1 on right accounts for inclusive range ending
        # 3. **Zero-based indexing**: Left boundary maps directly to prefix array index
        # 4. **Constant time complexity**: Single arithmetic operation regardless of range size
        # 5. **Preprocessing dependency**: Relies on prefix sums being pre-computed
        # 6. **Mathematical insight**: Leverages prefix sum difference to isolate range
    
    # Exercise 10
    # PRIORITY: Prefix sums + hashmap; common and insightful.
    def subarray_sum_equals_k(self, nums: List[int], k: int) -> int:
        """
        Count subarrays with sum equal to k.

        Problem:
            Given an integer array `nums` and an integer `k`, return the total number
            of continuous subarrays whose sum equals `k`.

        Args:
            nums: List of integers.
            k: Target sum for the subarrays.

        Returns:
            The count of contiguous subarrays summing to `k`.

        Examples:
            nums = [1, 1, 1], k = 2 -> 2 ([1,1] at indices [0,1] and [1,2])

        Time Complexity:
            O(n)

        Space Complexity:
            O(n)

        Notes:
            Track frequencies of prefix sums; for each running sum `curr`, add the
            frequency of `curr - k` to the answer.
        """
        count = 0
        prefix_sum = 0
        prefix_freq = {0: 1}

        for num in nums:
            prefix_sum += num
            if prefix_sum - k in prefix_freq:
                count += prefix_freq[prefix_sum - k]
            prefix_freq[prefix_sum] = prefix_freq.get(prefix_sum, 0) + 1

        return count

        # Key Takeaways:
        # 1. **Prefix sum + hashmap pattern**: Track frequency of prefix sums for O(n) solution
        # 2. **Empty prefix initialization**: {0: 1} handles subarrays starting from index 0
        # 3. **Difference lookup**: Find prefix_sum - k to identify valid subarray endings
        # 4. **Frequency counting**: Multiple occurrences of same prefix sum = multiple subarrays
        # 5. **Order matters**: Check for valid subarrays before updating frequency map
        # 6. **Mathematical insight**: If prefix[i] - prefix[j] = k, then subarray[j+1:i] sums to k
    
    # ========== BINARY SEARCH ==========
    # Exercise 11
    # PRIORITY: Fundamental binary search template; basis for many variants.
    def binary_search(self, nums: List[int], target: int) -> int:
        """
        Classic binary search on sorted array.

        Problem:
            Given a sorted array of integers `nums` (ascending) and an integer
            `target`, return the index of `target` if present; otherwise return -1.

        Args:
            nums: Sorted list of integers in ascending order.
            target: Integer value to search for.

        Returns:
            Index of `target` in `nums` if found; otherwise -1.

        Time Complexity:
            O(log n)

        Space Complexity:
            O(1)

        Notes:
            Use the standard template with `left <= right` and mid calculation
            `left + (right - left) // 2` to avoid overflow.
        """
        left, right = 0, len(nums) - 1
        
        while left <= right:
            mid = left + (right - left) // 2
            
            if nums[mid] == target:
                return mid
            elif nums[mid] < target:
                left = mid + 1
            else:
                right = mid - 1
        
        return -1
        
        # Key Takeaways:
        # 1. Use `left <= right` (not `left < right`) for inclusive bounds
        # 2. Calculate mid as `left + (right - left) // 2` to prevent overflow
        # 3. Always update pointers with `mid ± 1` to avoid infinite loops
        # 4. Standard comparison: `nums[mid] < target` (compare array element first)
        # 5. Initialize with `left, right = 0, len(nums) - 1` (inclusive bounds)
        # 6. Time: O(log n), Space: O(1) - halves search space each iteration
    
    # Exercise 12
    # PRIORITY: Binary search boundaries (lower/upper bound); very interview-relevant.
    def find_first_and_last_position(self, nums: List[int], target: int) -> List[int]:
        """
        Find first and last position of target in sorted array.

        Problem:
            Given a sorted array `nums` (ascending) and a target value `target`,
            return the indices [first, last] where `target` appears. If `target`
            is not present, return [-1, -1].

        Args:
            nums: Sorted list of integers in ascending order.
            target: Integer value to locate.

        Returns:
            A list [first, last] of the first and last occurrence indices, or
            [-1, -1] if not found.

        Time Complexity:
            O(log n)

        Space Complexity:
            O(1)

        Notes:
            Perform two binary searches: one to find the leftmost index and one to
            find the rightmost index of `target`.
        """
        def find_leftmost():
            left, right = 0, len(nums) - 1
            result = -1
            while left <= right:
                mid = left + (right - left) // 2
                if nums[mid] == target:
                    result = mid  # Found target, but continue searching left
                    right = mid - 1
                elif nums[mid] < target:
                    left = mid + 1
                else:
                    right = mid - 1
            return result

        def find_rightmost():
            left, right = 0, len(nums) - 1
            result = -1
            while left <= right:
                mid = left + (right - left) // 2
                if nums[mid] == target:
                    result = mid  # Found target, but continue searching right
                    left = mid + 1
                elif nums[mid] < target:
                    left = mid + 1
                else:
                    right = mid - 1
            return result

        return [find_leftmost(), find_rightmost()] 
    

    # Exercise 13
    # PRIORITY: Binary search in rotated array; staple mid-level problem.
    def search_rotated_sorted_array(self, nums: List[int], target: int) -> int:
        """
        Search in rotated sorted array.

        Problem:
            Given an array `nums` of distinct integers sorted in ascending order,
            but rotated at an unknown pivot, search for a given `target` value.
            If `target` exists, return its index; otherwise return -1.

        Args:
            nums: Rotated, sorted list of distinct integers.
            target: Integer value to search for.

        Returns:
            Index of `target` if found; otherwise -1.

        Time Complexity:
            O(log n)

        Space Complexity:
            O(1)

        Notes:
            At least one half (left or right of mid) remains sorted. Decide which
            half is sorted and narrow the search accordingly.
        """
        # TODO(required): Implement this function for practice.
        
        left, right = 0, len(nums)-1

        while left <= right:
            mid = left + (right - left) // 2
            if target == nums[mid]:
                return mid 
            
            # left is sorted
            if nums[left] <= nums[mid]:
                if nums[left] <= target < nums[mid]:
                    right = mid - 1
                else:
                    left = mid + 1
            else:
                if nums[mid] < target <= nums[right]:
                    left = mid + 1
                else: 
                    right = mid - 1

        return -1 



def demonstrate_patterns():
    """Demonstrate usage of array and string patterns."""
    patterns = ArrayStringPatterns()
    
    print("=== ARRAYS AND STRINGS DEMONSTRATIONS ===\n")
    
    # Two Pointers
    print("1. Two Pointers - Two Sum on sorted array:")
    test_cases_2sum = [
        ([2, 7, 11, 15], 9),
        ([1, 3, 4, 6], 8),
        ([-2, -1, 0, 3], 1)
    ]
    for nums, target in test_cases_2sum:
        result = patterns.two_sum_sorted(nums.copy(), target)
        print(f"   Array: {nums}, Target: {target} -> Indices: {result}")

    print("\n1b. Two Pointers - Reverse String:")
    test_strings = ["hello", "world", ["a", "b", "c", "d"]]
    for i, s in enumerate(test_strings):
        original = s.copy() if isinstance(s, list) else s
        if isinstance(s, str):
            s = list(s)
            patterns.reverse_string(s)
            result = ''.join(s)
        else:
            patterns.reverse_string(s)
            result = s
        print(f"   Test {i+1}: '{original}' -> '{result}'")

    print("\n1c. Two Pointers - Valid Palindrome:")
    palindrome_tests = [
        "A man, a plan, a canal: Panama",
        "race a car",
        "abba",
        "hello"
    ]
    for test in palindrome_tests:
        result = patterns.is_palindrome(test)
        print(f"   '{test}' -> {result}")

    # Sliding Window
    print("\n2. Sliding Window - Longest substring without repeating:")
    sliding_window_tests = [
        "abcabcbb",
        "bbbbb",
        "pwwkew",
        "abba"
    ]
    for s in sliding_window_tests:
        length = patterns.longest_substring_without_repeating(s)
        print(f"   String: '{s}' -> Length: {length}")

    print("\n2b. Sliding Window - Maximum sum subarray of size k:")
    window_tests = [
        ([2, 1, 5, 1, 3, 2], 3),
        ([4, 2, 1, 7, 8, 3], 2),
        ([1, 4, 2, 10, 2, 3, 1, 0, 20], 4)
    ]
    for nums, k in window_tests:
        max_sum = patterns.max_sum_subarray_of_size_k(nums, k)
        print(f"   Array: {nums}, k={k} -> Max Sum: {max_sum}")

    print("\n2c. Sliding Window - Minimum window substring:")
    min_window_tests = [
        ("ADOBECODEBANC", "ABC"),
        ("a", "a"),
        ("a", "aa")
    ]
    for s, t in min_window_tests:
        result = patterns.min_window_substring(s, t)
        print(f"   String: '{s}', Target: '{t}' -> Window: '{result}'")

    # Prefix Sums
    print("\n3. Prefix Sums - Subarray sum equals k:")
    prefix_sum_tests = [
        ([1, 1, 1], 2),
        ([1, 2, 3, 4, 5], 5),
        ([3, 4, 7, 2, -3, 1, 4, 2], 7)
    ]
    for nums, k in prefix_sum_tests:
        count = patterns.subarray_sum_equals_k(nums, k)
        print(f"   Array: {nums}, k={k} -> Count: {count}")

    print("\n3b. Prefix Sums - Range sum query:")
    # Initialize prefix sums
    range_sum_nums = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
    patterns.range_sum_query(range_sum_nums)
    range_tests = [(1, 3), (0, 4), (5, 9)]
    for left, right in range_tests:
        sum_val = patterns.sum_range(left, right)
        print(f"   Range [{left}, {right}] in {range_sum_nums} -> Sum: {sum_val}")

    # Binary Search
    print("\n4. Binary Search - Search in rotated sorted array:")
    binary_search_tests = [
        ([4, 5, 6, 7, 0, 1, 2], 0),
        ([4, 5, 6, 7, 0, 1, 2], 3),
        ([1], 0)
    ]
    for nums, target in binary_search_tests:
        index = patterns.search_rotated_sorted_array(nums, target)
        print(f"   Array: {nums}, Target: {target} -> Index: {index}")

    print("\n4b. Binary Search - Find first and last position:")
    first_last_tests = [
        ([5, 7, 7, 8, 8, 10], 8),
        ([5, 7, 7, 8, 8, 10], 6),
        ([], 0)
    ]
    for nums, target in first_last_tests:
        positions = patterns.find_first_and_last_position(nums, target)
        print(f"   Array: {nums}, Target: {target} -> Positions: {positions}")

    print("\n4c. Binary Search - Basic binary search:")
    basic_binary_tests = [
        ([1, 2, 3, 4, 5], 3),
        ([1, 2, 3, 4, 5], 6),
        ([], 1)
    ]
    for nums, target in basic_binary_tests:
        index = patterns.binary_search(nums, target)
        print(f"   Array: {nums}, Target: {target} -> Index: {index}")

    # Three Sum
    print("\n5. Three Sum - Find triplets that sum to zero:")
    three_sum_test_cases = [
        [-1, 0, 1, 2, -1, -4],
        [0, 1, 1],
        [0, 0, 0],
        [-2, 0, 1, 1, 2]
    ]
    for nums in three_sum_test_cases:
        triplets = patterns.three_sum(nums)
        print(f"   Array: {nums} -> Triplets: {triplets}")

    # --- Inline Assertions (quick sanity tests) ---
    # Two Pointers - two_sum_sorted
    assert patterns.two_sum_sorted([2, 7, 11, 15], 9) == [0, 1]
    assert patterns.two_sum_sorted([-3, -1, 0, 5, 9], 8) == [1, 4]
    assert patterns.two_sum_sorted([1, 2, 3, 4], 100) == [-1, -1]

    # Two Pointers - reverse_string
    _s = list("hello")
    patterns.reverse_string(_s)
    assert _s == list("olleh")
    _s = ["a"]
    patterns.reverse_string(_s)
    assert _s == ["a"]
    _s = []
    patterns.reverse_string(_s)
    assert _s == []

    # Two Pointers - is_palindrome
    assert patterns.is_palindrome("A man, a plan, a canal: Panama") is True
    assert patterns.is_palindrome("race a car") is False
    assert patterns.is_palindrome("") is True
    assert patterns.is_palindrome("abba") is True

    # Sliding Window - max_sum_subarray_of_size_k
    assert patterns.max_sum_subarray_of_size_k([2,1,5,1,3,2], 3) == 9
    assert patterns.max_sum_subarray_of_size_k([4,2,1,7,8,3], 2) == 15
    assert patterns.max_sum_subarray_of_size_k([1,2], 3) == 0
    assert patterns.max_sum_subarray_of_size_k([-1,-2,-3,-4], 2) == -3

    # Sliding Window - longest_substring_without_repeating
    assert patterns.longest_substring_without_repeating("abcabcbb") == 3
    assert patterns.longest_substring_without_repeating("bbbbb") == 1
    assert patterns.longest_substring_without_repeating("pwwkew") == 3
    assert patterns.longest_substring_without_repeating("") == 0
    assert patterns.longest_substring_without_repeating("abba") == 2

    # Sliding Window - min_window_substring
    assert patterns.min_window_substring("ADOBECODEBANC", "ABC") == "BANC"
    assert patterns.min_window_substring("a", "a") == "a"
    assert patterns.min_window_substring("a", "aa") == ""
    assert patterns.min_window_substring("aa", "aa") == "aa"

    # Prefix Sums - range_sum_query + sum_range
    _nums = [1,2,3,4,5,6]
    patterns.range_sum_query(_nums)
    assert patterns.sum_range(0, 0) == 1
    assert patterns.sum_range(0, 5) == sum(_nums)
    assert patterns.sum_range(1, 3) == 2 + 3 + 4
    assert patterns.sum_range(5, 5) == 6

    # Prefix Sums - subarray_sum_equals_k
    assert patterns.subarray_sum_equals_k([1,1,1], 2) == 2
    assert patterns.subarray_sum_equals_k([1,2,3], 3) == 2  # [1,2], [3]
    assert patterns.subarray_sum_equals_k([3,4,7,2,-3,1,4,2], 7) == 4
    assert patterns.subarray_sum_equals_k([0,0,0], 0) == 6

    # Binary Search - binary_search
    assert patterns.binary_search([1,2,3,4,5], 3) == 2
    assert patterns.binary_search([1,2,3,4,5], 6) == -1
    assert patterns.binary_search([], 1) == -1
    assert patterns.binary_search([1], 1) == 0

    # Binary Search - find_first_and_last_position
    assert patterns.find_first_and_last_position([5,7,7,8,8,10], 8) == [3, 4]
    assert patterns.find_first_and_last_position([5,7,7,8,8,10], 6) == [-1, -1]
    assert patterns.find_first_and_last_position([], 0) == [-1, -1]
    assert patterns.find_first_and_last_position([1], 1) == [0, 0]
    assert patterns.find_first_and_last_position([2,2,2,2], 2) == [0, 3]

    # Binary Search - search_rotated_sorted_array
    assert patterns.search_rotated_sorted_array([4,5,6,7,0,1,2], 0) == 4
    assert patterns.search_rotated_sorted_array([4,5,6,7,0,1,2], 3) == -1
    assert patterns.search_rotated_sorted_array([1], 0) == -1
    assert patterns.search_rotated_sorted_array([1], 1) == 0
    assert patterns.search_rotated_sorted_array([1,3], 3) == 1
    assert patterns.search_rotated_sorted_array([1,3,5], 5) == 2

    # Three Sum - order-insensitive comparison
    def _as_set(triplets: list[list[int]]):
        return {tuple(t) for t in triplets}
    assert _as_set(patterns.three_sum([-1,0,1,2,-1,-4])) == {(-1,-1,2), (-1,0,1)}
    assert _as_set(patterns.three_sum([0,1,1])) == set()
    assert _as_set(patterns.three_sum([0,0,0])) == {(0,0,0)}
    assert _as_set(patterns.three_sum([-2,0,1,1,2])) == {(-2,0,2), (-2,1,1)}

    print("\nAll inline tests passed. ✅")

if __name__ == "__main__":
    demonstrate_patterns()
