"""
Intervals and Sweep Line - Essential Coding Interview Problems

This module contains 8 carefully selected interval problems that cover all essential
patterns frequently tested at top tech companies (Google, Facebook, Amazon, etc.).

Problem Difficulty Levels:
- Easy (1): Meeting Rooms I
- Medium (5): Merge Intervals, Insert Interval, Meeting Rooms II,
              Non-overlapping Intervals, Interval List Intersections
- Medium-Hard (1): Car Pooling
- Hard (1): Employee Free Time

Each problem includes detailed question descriptions, constraints, examples,
and hints for optimal solutions.
"""

from typing import List


class IntervalPatterns:
    """Essential interval algorithms for coding interviews."""

    # ========== EASY PROBLEMS ==========
    # Q1
    def can_attend_meetings(self, intervals: List[List[int]]) -> bool:
        """
        PROBLEM: Meeting Rooms (Easy)

        Given an array of meeting time intervals where intervals[i] = [start_i, end_i],
        determine if a person could attend all meetings.

        Example 1:
        Input: intervals = [[0,30],[5,10],[15,20]]
        Output: false
        Explanation: Cannot attend [0,30] and [5,10] simultaneously

        Example 2:
        Input: intervals = [[7,10],[2,4]]
        Output: true

        Constraints:
        - 0 <= intervals.length <= 10^4
        - intervals[i].length == 2
        - 0 <= start_i < end_i <= 10^6

        Time Complexity: O(n log n) - sorting
        Space Complexity: O(1)
        """

        if not intervals:  # Handle empty case
            return True

        intervals.sort(key=lambda x: x[0])

        for i in range(1, len(intervals)):
            if intervals[i - 1][1] > intervals[i][0]:
                return False

        return True

    # ========== MEDIUM PROBLEMS ==========
    # Q2
    def merge_intervals(self, intervals: List[List[int]]) -> List[List[int]]:
        """
        PROBLEM: Merge Intervals (Medium)

        Given an array of intervals where intervals[i] = [start_i, end_i],
        merge all overlapping intervals.

        Example 1:
        Input: intervals = [[1,3],[2,6],[8,10],[15,18]]
        Output: [[1,6],[8,10],[15,18]]
        Explanation: [1,3] and [2,6] overlap, so merge them into [1,6]

        Example 2:
        Input: intervals = [[1,4],[4,5]]
        Output: [[1,5]]

        Constraints:
        - 1 <= intervals.length <= 10^4
        - intervals[i].length == 2
        - 0 <= start_i <= end_i <= 10^4

        Time Complexity: O(n log n) - sorting
        Space Complexity: O(1) excluding output
        """

        if not intervals:
            return intervals

        intervals.sort(key=lambda x: x[0])

        merged = [intervals[0]]

        for i in range(1, len(intervals)):
            current_interval = intervals[i]
            if current_interval[0] <= merged[-1][-1]:
                merged[-1][-1] = max(current_interval[-1], merged[-1][-1])
            else:
                merged.append(current_interval)

        return merged

    # Q3
    def insert_interval(
        self, intervals: List[List[int]], new_interval: List[int]
    ) -> List[List[int]]:
        """
        PROBLEM: Insert Interval (Medium)

        You are given an array of non-overlapping intervals sorted by start time.
        Insert a new interval and merge overlapping intervals if necessary.

        Example 1:
        Input: intervals = [[1,3],[6,9]], newInterval = [2,5]
        Output: [[1,5],[6,9]]

        Example 2:
        Input: intervals = [[1,2],[3,5],[6,7],[8,10],[12,16]], newInterval = [4,8]
        Output: [[1,2],[3,10],[12,16]]

        Constraints:
        - 0 <= intervals.length <= 10^4
        - intervals[i].length == newInterval.length == 2
        - 0 <= start_i <= end_i <= 10^5
        - intervals is sorted by start_i in ascending order

        Time Complexity: O(n)
        Space Complexity: O(n)
        """

        if not intervals:
            return [new_interval]

        result = []
        i = 0

        # Add all intervals that end before new_interval starts
        while i < len(intervals) and intervals[i][1] < new_interval[0]:
            result.append(intervals[i])
            i += 1

        # Merge overlapping intervals with new_interval
        while i < len(intervals) and intervals[i][0] <= new_interval[1]:
            new_interval[0] = min(new_interval[0], intervals[i][0])
            new_interval[1] = max(new_interval[1], intervals[i][1])
            i += 1

        result.append(new_interval)

        # Add remaining intervals
        while i < len(intervals):
            result.append(intervals[i])
            i += 1

        return result

    # Q4
    def min_meeting_rooms(self, intervals: List[List[int]]) -> int:
        """
        PROBLEM: Meeting Rooms II (Medium)

        Given an array of meeting time intervals, find the minimum number of
        conference rooms required.

        Example 1:
        Input: intervals = [[0,30],[5,10],[15,20]]
        Output: 2
        Explanation: Need 2 rooms for [0,30] and [5,10]

        Example 2:
        Input: intervals = [[7,10],[2,4]]
        Output: 1

        Constraints:
        - 1 <= intervals.length <= 10^4
        - 0 <= start_i < end_i <= 10^6

        Time Complexity: O(n log n)
        Space Complexity: O(n)

        Hint: Use sweep line algorithm - track all start/end events separately
        """

        times = []
        for interval in intervals:
            times.append((interval[0], "start"))
            times.append((interval[-1], "end"))

        times.sort(key=lambda x: (x[0], x[1] == "start"))

        max_overlap = 0
        overlapping = 0

        for time in times:
            if time[-1] == "start":
                overlapping += 1
                max_overlap = max(max_overlap, overlapping)
            else:
                overlapping -= 1

        return max_overlap

    # Q5
    def erase_overlap_intervals(self, intervals: List[List[int]]) -> int:
        """
        PROBLEM: Non-overlapping Intervals (Medium)

        Given an array of intervals, return the minimum number of intervals
        you need to remove to make the rest of the intervals non-overlapping.

        Example 1:
        Input: intervals = [[1,2],[2,3],[3,4],[1,3]]
        Output: 1
        Explanation: Remove [1,3] to make rest non-overlapping

        Example 2:
        Input: intervals = [[1,2],[1,2],[1,2]]
        Output: 2

        Constraints:
        - 1 <= intervals.length <= 10^5
        - intervals[i].length == 2
        - -5 * 10^4 <= start_i < end_i <= 5 * 10^4

        Time Complexity: O(n log n)
        Space Complexity: O(1)

        Hint: Greedy approach - sort by end time, keep intervals with earliest endings
        """

        if not intervals:
            return 0

        n_removed = 0

        intervals.sort(key=lambda x: x[-1])  # sort by endings, keep earlier endings!
        result = []
        result.append(intervals[0])

        for i in range(1, len(intervals)):
            if intervals[i][0] >= result[-1][-1]:  # no overlap, continue
                result.append(intervals[i])
                continue
            else:  # there is overlap, skip
                n_removed += 1

        return n_removed

    # Q6
    def interval_intersection(
        self, first_list: List[List[int]], second_list: List[List[int]]
    ) -> List[List[int]]:
        """
        PROBLEM: Interval List Intersections (Medium)

        You are given two lists of closed intervals. Each list of intervals is
        pairwise disjoint and sorted. Return the intersection of these two lists.

        Example:
        Input: firstList = [[0,2],[5,10],[13,23],[24,25]]
               secondList = [[1,5],[8,12],[15,24],[25,26]]
        Output: [[1,2],[5,5],[8,10],[15,23],[24,24],[25,25]]

        Constraints:
        - 0 <= firstList.length, secondList.length <= 1000
        - firstList.length + secondList.length >= 1
        - 0 <= start_i < end_i <= 10^9

        Time Complexity: O(m + n)
        Space Complexity: O(1) excluding output

        Hint: Two pointers - advance pointer of interval that ends earlier
        """

        result = []
        i = j = 0

        while i < len(first_list) and j < len(second_list):
            # Find intersection between first_list[i] and second_list[j]
            start = max(first_list[i][0], second_list[j][0])
            end = min(first_list[i][1], second_list[j][1])

            # If there's an intersection, add it
            if start <= end:
                result.append([start, end])

            # Move pointer of interval that ENDS earlier!
            if first_list[i][1] < second_list[j][1]:
                i += 1
            else:
                j += 1

        return result

    # ========== MEDIUM-HARD PROBLEMS ==========
    # Q7
    def car_pooling(self, trips: List[List[int]], capacity: int) -> bool:
        """
        PROBLEM: Car Pooling (Medium-Hard)

        There is a car with capacity empty seats. The vehicle only drives east.
        Given trips[i] = [numPassengers_i, from_i, to_i], return true if it's
        possible to pick up and drop off all passengers for all trips.

        Example 1:
        Input: trips = [[2,1,5],[3,3,7]], capacity = 4
        Output: false
        Explanation: Need 5 seats at location 3, but capacity is only 4

        Example 2:
        Input: trips = [[2,1,5],[3,3,7]], capacity = 5
        Output: true

        Constraints:
        - 1 <= trips.length <= 1000
        - trips[i].length == 3
        - 1 <= numPassengers_i <= 100
        - 0 <= from_i < to_i <= 1000
        - 1 <= capacity <= 10^5

        Time Complexity: O(n log n)
        Space Complexity: O(n)

        Hint: Use sweep line - track passenger changes at each location
        """

        # Simple Canonical Sweep Line Algorithm
        events = []

        # Create events: (location, passenger_change)
        for passengers, start, end in trips:
            events.append((start, passengers))  # Pick up
            events.append((end, -passengers))  # Drop off

        # Sort by location, then by change (negative first for tie-breaking)
        events.sort(key=lambda x: (x[0], x[1]))

        passengers = 0
        for location, change in events:
            passengers += change
            if passengers > capacity:
                return False

        return True

        # Alternative: Difference Array (more efficient for limited coordinate range)
        """
        diff = [0] * 1001  # 0 to 1000 inclusive
        
        for num_passengers, start, end in trips:
            diff[start] += num_passengers      # Pick up passengers
            diff[end] -= num_passengers        # Drop off passengers
        
        current_passengers = 0
        for passengers_change in diff:
            current_passengers += passengers_change
            if current_passengers > capacity:
                return False
        
        return True
        """

    # ========== HARD PROBLEMS ==========
    # Q8
    def employee_free_time(self, schedule: List[List[List[int]]]) -> List[List[int]]:
        """
        PROBLEM: Employee Free Time (Hard)

        We are given a list schedule of employees, which represents the working time
        for each employee. Each employee has a list of non-overlapping intervals.
        Return the list of finite intervals representing common, positive-length
        free time for all employees, also in sorted order.

        Example 1:
        Input: schedule = [[[1,3],[6,7]],[[2,4]],[[2,5],[9,12]]]
        Output: [[5,6],[7,9]]
        Explanation: All employees are free during [5,6] and [7,9]

        Example 2:
        Input: schedule = [[[1,3],[4,6]],[[2,5],[7,9]]]
        Output: [[6,7]]

        Constraints:
        - 1 <= schedule.length, schedule[i].length <= 50
        - 0 <= schedule[i][j][0] < schedule[i][j][1] <= 10^8

        Time Complexity: O(n log n) where n is total intervals
        Space Complexity: O(n)

        Hints:
        1. Flatten all intervals from all employees into one list
        2. Sort by start time and merge overlapping intervals
        3. Find gaps between merged intervals - these are free times
        4. Alternative: Use sweep line to track when all employees are busy
        """

        # Canonical sweep line: track busy count over [prev_time, t)
        events: List[tuple[int, int]] = []
        for emp in schedule:
            for start, end in emp:
                events.append((start, 1))
                events.append((end, -1))

        if not events:
            return []

        # Sort by time; for ties, process ends (-1) before starts (+1)
        events.sort(key=lambda e: (e[0], e[1]))

        free: List[List[int]] = []
        busy = 0
        prev_time = events[0][0]

        # Process all events grouped by time
        i = 0
        n = len(events)
        while i < n:
            t = events[i][0]

            # If no one was busy on [prev_time, t), that's a common free interval
            if busy == 0 and t > prev_time:
                free.append([prev_time, t])

            # Apply all deltas at time t
            while i < n and events[i][0] == t:
                busy += events[i][1]
                i += 1

            prev_time = t

        return free


def demonstrate_patterns():
    """Demonstrate test cases for the 8 essential interval problems."""
    patterns = IntervalPatterns()

    print("=== ESSENTIAL INTERVAL PROBLEMS DEMONSTRATIONS ===\n")
    print("Note: All functions return placeholder values until implemented.\n")

    # Easy Problem
    print("1. Meeting Rooms (Easy):")
    meetings = [[0, 30], [5, 10], [15, 20]]
    result = patterns.can_attend_meetings(meetings)
    print(f"   Input: {meetings}")
    print(f"   Can attend all meetings: {result} (Expected: False)")

    # Edge cases for Meeting Rooms
    tests_meetings = [
        ([], True),  # empty
        ([[1, 2]], True),  # single
        ([[1, 3], [3, 5]], True),  # touching
        ([[4, 5], [1, 2]], True),  # unsorted, no overlap
        ([[0, 5], [4, 6]], False),  # overlap
    ]
    for t, exp in tests_meetings:
        print(
            f"   Edge Test can_attend_meetings({t}) -> {patterns.can_attend_meetings(t)} (Expected: {exp})"
        )

    # Medium Problems
    print("\n2. Merge Intervals (Medium):")
    intervals = [[1, 3], [2, 6], [8, 10], [15, 18]]
    result = patterns.merge_intervals(intervals)
    print(f"   Input: {intervals}")
    print(f"   Merged: {result} (Expected: [[1,6],[8,10],[15,18]])")

    # Edge cases for Merge Intervals
    tests_merge = [
        ([], []),
        ([[1, 4]], [[1, 4]]),
        ([[1, 2], [2, 3]], [[1, 3]]),  # touching merge
        ([[1, 10], [2, 3], [4, 8]], [[1, 10]]),  # nested
        ([[5, 7], [1, 2], [3, 4]], [[1, 2], [3, 4], [5, 7]]),  # disjoint unsorted
    ]
    for t, exp in tests_merge:
        res = patterns.merge_intervals([row[:] for row in t])
        print(f"   Edge Test merge_intervals({t}) -> {res} (Expected: {exp})")

    print("\n3. Insert Interval (Medium):")
    intervals = [[1, 3], [6, 9]]
    new_interval = [2, 5]
    result = patterns.insert_interval(intervals, new_interval)
    print(f"   Intervals: {intervals}, New: {new_interval}")
    print(f"   Result: {result} (Expected: [[1,5],[6,9]])")

    # Edge cases for Insert Interval
    tests_insert = [
        ([], [2, 3], [[2, 3]]),
        ([[5, 7]], [1, 2], [[1, 2], [5, 7]]),  # before all
        ([[1, 2], [5, 7]], [8, 10], [[1, 2], [5, 7], [8, 10]]),  # after all
        ([[1, 2], [5, 7]], [2, 5], [[1, 7]]),  # touching both ends -> merge
        ([[1, 3], [6, 9]], [4, 5], [[1, 3], [4, 5], [6, 9]]),  # fits between
        ([[1, 3], [6, 9]], [2, 7], [[1, 9]]),  # merges both sides
    ]
    for arr, new_int, exp in tests_insert:
        res = patterns.insert_interval([row[:] for row in arr], new_int[:])
        print(
            f"   Edge Test insert_interval({arr}, {new_int}) -> {res} (Expected: {exp})"
        )

    print("\n4. Meeting Rooms II (Medium):")
    meetings = [[0, 30], [5, 10], [15, 20]]
    result = patterns.min_meeting_rooms(meetings)
    print(f"   Meetings: {meetings}")
    print(f"   Rooms needed: {result} (Expected: 2)")

    # Edge cases for Meeting Rooms II
    tests_rooms = [
        ([], 0),
        ([[1, 5], [5, 10]], 1),  # touching
        ([[1, 4], [2, 5], [7, 9]], 2),
        ([[1, 10], [1, 3], [1, 2]], 3),  # simultaneous starts
    ]
    for t, exp in tests_rooms:
        print(
            f"   Edge Test min_meeting_rooms({t}) -> {patterns.min_meeting_rooms([row[:] for row in t])} (Expected: {exp})"
        )

    print("\n5. Non-overlapping Intervals (Medium):")
    intervals = [[1, 2], [2, 3], [3, 4], [1, 3]]
    result = patterns.erase_overlap_intervals(intervals)
    print(f"   Intervals: {intervals}")
    print(f"   Remove count: {result} (Expected: 1)")

    # Edge cases for Non-overlapping Intervals
    tests_erase = [
        ([[1, 2], [2, 3], [3, 4]], 0),
        ([[1, 2], [1, 2], [1, 2]], 2),
        ([[1, 3], [2, 4], [3, 5]], 1),
        ([[1, 2]], 0),
    ]
    for t, exp in tests_erase:
        print(
            f"   Edge Test erase_overlap_intervals({t}) -> {patterns.erase_overlap_intervals([row[:] for row in t])} (Expected: {exp})"
        )

    print("\n6. Interval List Intersections (Medium):")
    first_list = [[0, 2], [5, 10], [13, 23], [24, 25]]
    second_list = [[1, 5], [8, 12], [15, 24], [25, 26]]
    result = patterns.interval_intersection(first_list, second_list)
    print(f"   First: {first_list}")
    print(f"   Second: {second_list}")
    print(f"   Intersections: {result}")
    print("   Expected: [[1,2],[5,5],[8,10],[15,23],[24,24],[25,25]]")

    # Edge cases for Interval List Intersections
    tests_intersections = [
        ([], [[1, 2]], []),
        ([[1, 2]], [], []),
        ([[1, 2]], [[3, 4]], []),
        ([[1, 2]], [[2, 3]], [[2, 2]]),  # touching point
        ([[1, 5]], [[2, 3]], [[2, 3]]),
    ]
    for a, b, exp in tests_intersections:
        res = patterns.interval_intersection(
            [row[:] for row in a], [row[:] for row in b]
        )
        print(
            f"   Edge Test interval_intersection({a}, {b}) -> {res} (Expected: {exp})"
        )

    # Medium-Hard Problem
    print("\n7. Car Pooling (Medium-Hard):")
    trips = [[2, 1, 5], [3, 3, 7]]
    capacity = 4
    result = patterns.car_pooling(trips, capacity)
    print(f"   Trips: {trips}, Capacity: {capacity}")
    print(f"   Possible: {result} (Expected: False)")

    # Edge cases for Car Pooling
    tests_carpool = [
        ([], 3, True),  # no trips
        ([[2, 1, 3], [2, 3, 5]], 2, True),  # drop then pick at same location
        ([[2, 1, 5], [2, 2, 6]], 3, False),  # exceed capacity overlap
        ([[3, 2, 7], [3, 7, 9], [8, 3, 9]], 11, True),
        ([[1, 0, 1], [1, 1, 2], [1, 2, 3]], 1, True),  # sequential trips
    ]
    for t, cap, exp in tests_carpool:
        res = patterns.car_pooling([row[:] for row in t], cap)
        print(f"   Edge Test car_pooling({t}, {cap}) -> {res} (Expected: {exp})")

    # Hard Problem
    print("\n8. Employee Free Time (Hard):")
    schedule = [[[1, 3], [6, 7]], [[2, 4]], [[2, 5], [9, 12]]]
    result = patterns.employee_free_time(schedule)
    print(f"   Schedule: {schedule}")
    print(f"   Free time: {result} (Expected: [[5,6],[7,9]])")

    # Edge cases for Employee Free Time (finite intervals only, positive length)
    tests_eft = [
        ([[[1, 3]]], []),  # one employee, no finite free between events
        ([[[1, 3]], [[3, 5]]], []),  # touching at 3, no positive free
        ([[[1, 2], [2, 3]], [[0, 1], [3, 4]]], []),  # only zero-length gaps
        ([[[1, 3]], [[5, 7]]], [[3, 5]]),  # clear gap
        ([[[1, 2]], [[2, 3]], [[4, 5]]], [[3, 4]]),  # gap between groups
    ]
    for sched, exp in tests_eft:
        res = patterns.employee_free_time(sched)
        print(f"   Edge Test employee_free_time({sched}) -> {res} (Expected: {exp})")

    print("\n" + "=" * 60)
    print("SUMMARY:")
    print("- 1 Easy problem (Meeting Rooms)")
    print("- 5 Medium problems (Merge, Insert, Rooms II, Non-overlap, Intersection)")
    print("- 1 Medium-Hard problem (Car Pooling)")
    print("- 1 Hard problem (Employee Free Time)")
    print("- All essential interval patterns covered")
    print("- Ready for coding interview practice!")


if __name__ == "__main__":
    demonstrate_patterns()
