"""
Graphs - Essential Patterns for Coding Interviews

This module covers graph algorithms frequently tested in coding interviews
at top tech companies like Google.

Key Patterns Covered (curated essentials):
1. DFS (Iterative)
2. BFS
3. Grid DFS (Number of Islands)
4. Graph Coloring and Bipartite Detection
5. Undirected Cycle Detection
6. Topological Sort (Kahn's Algorithm)
7. Union-Find (Disjoint Set Union) and Connected Components
8. Shortest Path (Dijkstra)
9. Minimum Spanning Tree (Kruskal's)
10. Unweighted Shortest Path (BFS distances)
11. Directed Cycle Detection (Directed)
12. Topological Sort (DFS)
13. Multi-source BFS (grid/graph)
14. 0-1 BFS (weights in {0,1})
15. Course Schedule II (Topological Sort for DAG)

Time/Space Complexity Analysis and Use Cases included for each pattern.
"""

# Practice Order Guide
# Q1 (⭐) DFS (iterative)
# Q2 (⭐) BFS
# Q3 (⭐⭐) Number of Islands (Grid DFS)
# Q4 (⭐⭐) Is Bipartite (BFS Coloring)
# Q5 (⭐⭐) Cycle Detection (Undirected)
# Q6 (⭐⭐) Topological Sort (Kahn)
# Q7 (⭐⭐) Connected Components (Union-Find)
# Q8 (⭐⭐⭐) Dijkstra's Shortest Path
# Q9 (⭐⭐) Kruskal's MST
# Q10 (⭐) BFS Shortest Path (Unweighted)
# Q11 (⭐⭐) Cycle Detection (Directed)
# Q12 (⭐⭐) Topological Sort (DFS)
# Q13 (⭐⭐) Multi-source BFS (Grid)
# Q14 (⭐⭐⭐) 0-1 BFS
# Q15 (⭐⭐) Course Schedule II

from collections import defaultdict, deque
import heapq
from typing import List, Dict, Tuple, Optional


class GraphPatterns:
    """Collection of essential graph algorithms for coding interviews."""

    # ========== GRAPH TRAVERSALS ==========
    # Q1 (⭐) - DFS (iterative)
    def dfs_iterative(self, graph: Dict[int, List[int]], start: int) -> List[int]:
        """
        Implement iterative depth-first search traversal of an undirected graph.

        Problem:
        Given an undirected graph represented as an adjacency list and a starting node,
        traverse the graph using depth-first search and return the order of nodes visited.
        Use an iterative implementation with a stack.

        Parameters:
        - graph: Dictionary mapping each node to its list of adjacent nodes
        - start: Starting node for the traversal

        Returns:
        List of nodes in the order they were visited during DFS traversal

        Example:
        Input: graph = {0: [1, 2], 1: [0, 3], 2: [0], 3: [1]}, start = 0
        Output: [0, 2, 1, 3] (actual order depends on adjacency list ordering)

        Constraints:
        - 1 ≤ number of nodes ≤ 1000
        - Graph is connected and contains no self-loops
        - No duplicate edges between nodes

        Complexity:
        - Time: O(V + E) where V is vertices and E is edges
        - Space: O(V) for the visited set and stack
        """

        visited = set()
        result = []
        stack = [start]
        visited.add(start)  # visit it when adding to stack

        while stack:
            node = stack.pop()
            result.append(node)

            for neighbor in graph.get(node, []):
                if neighbor not in visited:
                    visited.add(neighbor)
                    stack.append(neighbor)
        return result

    # Q2 (⭐) - BFS
    def bfs(self, graph: Dict[int, List[int]], start: int) -> List[int]:
        """
        Implement breadth-first search traversal of an undirected graph.

        Problem:
        Given an undirected graph represented as an adjacency list and a starting node,
        traverse the graph level by level using breadth-first search. Return the order
        of nodes visited.

        Parameters:
        - graph: Dictionary mapping each node to its list of adjacent nodes
        - start: Starting node for the traversal

        Returns:
        List of nodes in the order they were visited during BFS traversal

        Example:
        Input: graph = {0: [1, 2], 1: [0, 3], 2: [0], 3: [1]}, start = 0
        Output: [0, 1, 2, 3] (level 0: [0], level 1: [1,2], level 2: [3])

        Constraints:
        - 1 ≤ number of nodes ≤ 1000
        - Graph is connected and contains no self-loops
        - No duplicate edges between nodes

        Complexity:
        - Time: O(V + E) where V is vertices and E is edges
        - Space: O(V) for the visited set and queue
        """

        visited = set()
        result = []
        queue = deque([start])
        visited.add(start)

        while queue:
            node = queue.popleft()
            result.append(node)

            for neighbor in graph.get(node, []):
                if neighbor not in visited:
                    visited.add(neighbor)
                    queue.append(neighbor)

        return result

    # Q3 (⭐⭐) - Number of Islands (Grid DFS)
    def number_of_islands(self, grid: List[List[str]]) -> int:
        """
        Count the number of islands in a 2D grid.

        Problem:
        Given a 2D grid where '1' represents land and '0' represents water,
        count the number of islands. An island is formed by connecting adjacent
        land cells horizontally or vertically. All grid edges are surrounded by water.

        Parameters:
        - grid: 2D list where each cell is either '1' (land) or '0' (water)

        Returns:
        Integer count of distinct islands in the grid

        Examples:
        Input: [["1","1","1","1","0"],
                ["1","1","0","1","0"],
                ["1","1","0","0","0"],
                ["0","0","0","0","0"]]
        Output: 1 (one connected landmass)

        Input: [["1","1","0","0","0"],
                ["1","1","0","0","0"],
                ["0","0","1","0","0"],
                ["0","0","0","1","1"]]
        Output: 3 (three separate islands)

        Constraints:
        - 1 ≤ grid.length, grid[i].length ≤ 300
        - grid[i][j] ∈ {'0', '1'}

        Complexity:
        - Time: O(m * n) where m, n are grid dimensions
        - Space: O(m * n) worst case for DFS recursion stack
        """

        # # check base case
        # if len(grid) < 1 or len(grid[0]) < 1:
        #     return 0

        # # make a copy of the input to mark visited island
        # _grid = [row[:] for row in grid]  # Deep copy
        # n_row = len(grid)
        # n_col = len(grid[0])

        # # create counter
        # counter = 0

        # # create a helper function find_neighoring_land to find neighbors with value "1"
        # def find_neighoring_land(pos):
        #     land_neighbors = []
        #     row, col = pos
        #     directions = [(-1, 0), (1, 0), (0, -1), (0, 1)]  # up, down, left, right

        #     for dr, dc in directions:
        #         nr, nc = row + dr, col + dc
        #         if 0 <= nr < n_row and 0 <= nc < n_col and _grid[nr][nc] == "1":
        #             land_neighbors.append((nr, nc))
        #     return land_neighbors

        # # create a dfs function starting from a location
        # def dfs(start_pos):
        #     stack = [start_pos]

        #     while stack:
        #         top = stack.pop()
        #         # Mark current position as visited FIRST
        #         if _grid[top[0]][top[1]] == "1":
        #             _grid[top[0]][top[1]] = "0"
        #             valid_neighbors = find_neighoring_land(top)
        #             for neighbor in valid_neighbors:
        #                 stack.append(neighbor)

        # # for loop through each grid, when encounter a new "1", add counter, and run dfs
        # for i_row in range(n_row):
        #     for j_col in range(n_col):
        #         if _grid[i_row][j_col] == "1":
        #             counter += 1
        #             dfs((i_row, j_col))

        # # return counter
        # return counter

        # --- Solution (commented out) ---
        if not grid or not grid[0]:
            return 0

        rows, cols = len(grid), len(grid[0])
        islands = 0

        def dfs(r, c):  # for any position, validation inside, conquer 1
            if r < 0 or r >= rows or c < 0 or c >= cols or grid[r][c] != "1":
                return

            grid[r][c] = "0"  # Mark as visited

            # Visit all 4 directions
            for dr, dc in [(0, 1), (1, 0), (0, -1), (-1, 0)]:
                dfs(r + dr, c + dc)

        for r in range(rows):
            for c in range(cols):
                if grid[r][c] == "1":  # meet island for the first time.
                    islands += 1
                    dfs(r, c)  # conquer island

        return islands

    # ========== BIPARTITE AND COLORING ==========
    # Q4 (⭐⭐) - Is Bipartite (BFS Coloring)
    def is_bipartite(self, graph: List[List[int]]) -> bool:
        """
        Determine if an undirected graph is bipartite.

        Problem:
        Given an undirected graph, determine if it can be colored using two colors
        such that no adjacent nodes have the same color. This is equivalent to
        checking if the graph is bipartite (can be partitioned into two independent sets).

        Parameters:
        - graph: Adjacency list where graph[i] contains all neighbors of node i

        Returns:
        True if the graph is bipartite, False otherwise

        Examples:
        Input: graph = [[1,2,3],[0,2],[0,1,3],[0,2]]
        Output: False (forms odd cycles, cannot be 2-colored)

        Input: graph = [[1,3],[0,2],[1,3],[0,2]]
        Output: True (can partition into {0,2} and {1,3})

        Constraints:
        - 1 ≤ graph.length ≤ 100
        - 0 ≤ graph[i].length < graph.length
        - 0 ≤ graph[i][j] ≤ graph.length - 1
        - graph[i] does not contain i (no self-loops)
        - All neighbor lists contain unique values

        Complexity:
        - Time: O(V + E) where V is vertices and E is edges
        - Space: O(V) for coloring array and BFS queue
        """

        # colors = {}
        # not_visited = set(list(range(len(graph))))

        # while len(colors) < len(graph):
        #     starting_node = not_visited.pop()
        #     queue = deque([starting_node])
        #     colors[starting_node] = 0
        #     while queue:
        #         top = queue.popleft()
        #         neighbors = graph[top]
        #         for neighbor in neighbors:
        #             color = colors.get(neighbor, None)
        #             if color is not None:
        #                 if color == colors[top]:
        #                     return False
        #             else:
        #                 colors[neighbor] = 1 - colors[top]
        #                 not_visited.remove(neighbor)
        #                 queue.append(neighbor)

        # assert len(colors) == len(graph)

        # return True

        # --- Solution (commented out) ---
        n = len(graph)
        color = [-1] * n

        # TODO: learning: use a for loop to make sure we don't miss anything, similar
        # to last question
        for start in range(n):
            if color[start] == -1:
                queue = deque([start])
                color[start] = 0

                while queue:
                    node = queue.popleft()

                    for neighbor in graph[node]:
                        if color[neighbor] == -1:
                            color[neighbor] = 1 - color[node]  # mark on enqueue
                            queue.append(neighbor)
                        elif color[neighbor] == color[node]:  # no bipartite
                            return False

        return True

    # ========== CYCLE DETECTION ==========
    # Q5 (⭐⭐) - Cycle Detection (Undirected)
    def has_cycle_undirected(self, graph: Dict[int, List[int]]) -> bool:
        """
        Detect if an undirected graph contains a cycle.

        Problem:
        Given an undirected graph represented as an adjacency list, determine
        whether the graph contains any cycles. A cycle exists when there is a
        path that starts and ends at the same vertex.

        Parameters:
        - graph: Dictionary mapping each node to its list of adjacent nodes

        Returns:
        True if the graph contains at least one cycle, False otherwise

        Examples:
        Input: graph = {0: [1, 2], 1: [0, 2], 2: [0, 1]}
        Output: True (triangle cycle: 0-1-2-0)

        Input: graph = {0: [1], 1: [0, 2], 2: [1]}
        Output: False (tree structure, no cycles)

        Constraints:
        - 1 ≤ number of nodes ≤ 1000
        - No self-loops or multiple edges between nodes
        - Graph may be disconnected

        Complexity:
        - Time: O(V + E) where V is vertices and E is edges
        - Space: O(V) for visited set and recursion stack
        """

        visited = set()

        def dfs(node, parent):
            """DFS helper to detect cycle from current node."""
            visited.add(node)

            for neighbor in graph.get(node, []):
                if neighbor not in visited:
                    if dfs(neighbor, node):
                        return True
                elif neighbor != parent:  # Back edge to non-parent = cycle
                    return True

            return False

        # Handle disconnected components
        for node in graph:
            if node not in visited:
                if dfs(node, None):  # Use None instead of -1
                    return True

        return False

    # ========== TOPOLOGICAL SORT ==========
    # Q6 (⭐⭐) - Topological Sort (Kahn)
    def topological_sort_kahn(self, n: int, edges: List[Tuple[int, int]]) -> List[int]:
        """
        Find a topological ordering of a directed acyclic graph using Kahn's algorithm.

        Problem:
        Given a directed acyclic graph (DAG) with n nodes and a list of directed edges,
        find a linear ordering of the nodes such that for every directed edge (u, v),
        node u appears before node v in the ordering. Use Kahn's algorithm (BFS-based).

        Parameters:
        - n: Number of nodes in the graph (labeled 0 to n-1)
        - edges: List of directed edges as (u, v) tuples meaning u → v

        Returns:
        List of nodes in topological order, or empty list if cycle detected

        Example:
        Input: n = 6, edges = [(5,2), (5,0), (4,0), (4,1), (2,3), (3,1)]
        Output: [4, 5, 0, 2, 3, 1] (one valid topological ordering)

        Constraints:
        - 1 ≤ n ≤ 2000
        - 0 ≤ len(edges) ≤ n * (n-1)
        - Graph must be acyclic for valid solution

        Complexity:
        - Time: O(V + E) where V is vertices and E is edges
        - Space: O(V + E) for adjacency list and in-degree array
        """

        graph = defaultdict(list)
        in_degree = [0] * n
        for u, v in edges:
            graph[u].append(v)
            in_degree[v] += 1
        queue = deque([i for i in range(n) if in_degree[i] == 0])
        result: List[int] = []
        while queue:
            node = queue.popleft()
            result.append(node)
            for nei in graph[node]:
                in_degree[nei] -= 1
                if in_degree[nei] == 0:
                    queue.append(nei)
        return result if len(result) == n else []

    # ========== UNION-FIND (DISJOINT SET UNION) ==========
    class UnionFind:
        """
        Union-Find (Disjoint Set Union) data structure with path compression and union by rank.

        A highly optimized data structure for tracking connected components in a graph.
        Supports near-constant time union and find operations through two key optimizations:

        Optimizations:
        - Path Compression: Flattens trees during find operations by making nodes point directly to root
        - Union by Rank: Keeps trees balanced by attaching smaller trees under larger tree roots

        Attributes:
        - parent: Array where parent[i] points to the parent of node i (or itself if root)
        - rank: Array tracking approximate height/depth of each tree for balancing
        - components: Counter tracking the total number of disjoint components

        Methods:
        - find(x): Returns root of component containing x with path compression
        - union(x, y): Merges components containing x and y, returns True if merge occurred
        - connected(x, y): Checks if x and y belong to the same component

        Common Use Cases:
        - Detecting cycles in undirected graphs
        - Finding connected components
        - Kruskal's minimum spanning tree algorithm
        - Dynamic connectivity queries
        - Percolation problems

        Time Complexity: O(α(n)) per operation, where α is the inverse Ackermann function
        Space Complexity: O(n) for parent, rank arrays and component counter

        Example:
        >>> uf = UnionFind(5)  # Creates 5 separate components: {0}, {1}, {2}, {3}, {4}
        >>> uf.union(0, 1)     # Merge components: {0,1}, {2}, {3}, {4}
        >>> uf.union(2, 3)     # Merge components: {0,1}, {2,3}, {4}
        >>> uf.connected(0, 1) # Returns True
        >>> uf.connected(1, 2) # Returns False
        >>> uf.components      # Returns 3
        """

        def __init__(self, n: int):
            self.parent = list(range(n))
            self.rank = [0] * n
            self.components = n

        def find(self, x: int) -> int:
            """Find root with path compression."""
            if self.parent[x] != x:  # root test
                self.parent[x] = self.find(self.parent[x])
            return self.parent[x]

        def union(self, x: int, y: int) -> bool:
            """Union by rank. Returns True if union performed."""
            root_x, root_y = self.find(x), self.find(y)

            if root_x == root_y:
                return False

            if self.rank[root_x] >= self.rank[root_y]:
                parent_root, child_root = root_x, root_y
            else:
                parent_root, child_root = root_y, root_x

            self.parent[child_root] = parent_root

            if self.rank[root_x] == self.rank[root_y]:
                self.rank[parent_root] += 1

            self.components -= 1
            return True

        def connected(self, x: int, y: int) -> bool:
            """Check if two nodes are connected."""
            return self.find(x) == self.find(y)

    # Q7 (⭐⭐) - Connected Components (Union-Find)
    def number_of_connected_components(self, n: int, edges: List[List[int]]) -> int:
        """
        Count connected components in an undirected graph using Union-Find.

        Problem:
        Given n nodes labeled 0 to n-1 and a list of undirected edges,
        determine how many connected components exist in the graph.
        Use Union-Find data structure for efficient component tracking.

        Parameters:
        - n: Number of nodes in the graph
        - edges: List of undirected edges where each edge connects two nodes

        Returns:
        Integer count of connected components in the graph

        Examples:
        Input: n = 5, edges = [[0,1],[1,2],[3,4]]
        Output: 2 (components: {0,1,2} and {3,4}, node 5 isolated)

        Input: n = 5, edges = [[0,1],[1,2],[2,3],[3,4]]
        Output: 1 (all nodes connected in single component)

        Constraints:
        - 1 ≤ n ≤ 2000
        - 0 ≤ edges.length ≤ 5000
        - Each edge connects two distinct nodes
        - No duplicate edges

        Complexity:
        - Time: O(E x α(n)) where O is inverse Ackermann function
        - Space: O(n) for Union-Find parent and rank arrays
        """

        uf = self.UnionFind(n)

        for u, v in edges:
            uf.union(u, v)

        return uf.components

    # ========== SHORTEST PATH ALGORITHMS ==========
    # Q8 (⭐⭐⭐) - Dijkstra's Shortest Path
    def dijkstra(
        self, graph: Dict[int, List[Tuple[int, int]]], start: int
    ) -> Dict[int, float]:
        """
        Find shortest paths from a source node to all reachable nodes using Dijkstra's algorithm.

        Problem:
        Given a weighted directed graph with non-negative edge weights and a starting node,
        compute the shortest path distances from the start node to all other reachable nodes.
        Use Dijkstra's algorithm with a priority queue.

        Parameters:
        - graph: Adjacency list where graph[node] = [(neighbor, weight), ...]
        - start: Source node for shortest path calculation

        Returns:
        Dictionary mapping each reachable node to its shortest distance from start

        Example:
        Input: graph = {1: [(2, 1), (3, 4)], 2: [(3, 2), (4, 5)], 3: [(4, 1)], 4: []}
               start = 1
        Output: {1: 0, 2: 1, 3: 3, 4: 4}
        Explanation: Shortest paths are 1→2 (cost 1), 1→2→3 (cost 3), 1→2→3→4 (cost 4)

        Constraints:
        - 1 ≤ number of nodes ≤ 100
        - All edge weights are non-negative
        - Graph may contain unreachable nodes

        Complexity:
        - Time: O((V + E) log V) using binary heap priority queue
        - Space: O(V) for distances map and priority queue
        """

        # dijkstra algorithm idea: maintain a conquered set of nodes that have been
        # visisted, among eges connecting the conquered and unvisisted, select the
        # node with the min distance, denote it as visisted, add min distance, and then
        # add the connecting egdges to the queue and update the heap

        distances = {start: 0.0}
        heap = [(0.0, start)]

        while heap:
            dist, nxt = heapq.heappop(heap)

            if dist != distances.get(nxt, float("inf")):  # stale nodes
                continue

            # for all unvisisted nei of nxt, update their distance and add to heap
            for _nei, _dist in graph.get(nxt, []):
                # if _nei not in distances:
                #     heapq.heappush(heap, (dist + _dist, _nei))
                if _dist < 0:
                    raise ValueError
                nd = dist + float(_dist)
                if nd < distances.get(
                    _nei, float("inf")
                ):  # for each node, only add better distance nodes
                    distances[_nei] = nd
                    heapq.heappush(heap, (nd, _nei))

        return distances

        # --- Solution (commented out) ---
        # distances = defaultdict(lambda: float('inf'))
        # distances[start] = 0
        # pq = [(0, start)]
        # visited = set()
        #
        # while pq:
        #     current_dist, u = heapq.heappop(pq)
        #
        #     if u in visited:
        #         continue
        #
        #     visited.add(u)
        #
        #     for v, weight in graph.get(u, []):
        #         if v not in visited:
        #             new_dist = current_dist + weight
        #             if new_dist < distances[v]:
        #                 distances[v] = new_dist
        #                 heapq.heappush(pq, (new_dist, v))
        #
        # return dict(distances)

    # ========== MINIMUM SPANNING TREE ==========
    # Q9 (⭐⭐) - Kruskal's MST
    def kruskal_mst(
        self, n: int, edges: List[Tuple[int, int, int]]
    ) -> List[Tuple[int, int, int]]:
        """
        Find minimum spanning tree using Kruskal's algorithm with Union-Find.

        Problem:
        Given n nodes and a list of weighted edges, find the minimum spanning tree
        that connects all nodes with minimum total edge weight. Use Kruskal's
        algorithm: sort edges by weight and greedily add edges that don't create cycles.

        Parameters:
        - n: Number of nodes in the graph (labeled 0 to n-1)
        - edges: List of weighted edges as (node1, node2, weight) tuples

        Returns:
        List of edges forming the minimum spanning tree

        Example:
        Input: n = 4, edges = [(0,1,10), (0,2,6), (0,3,5), (1,3,15), (2,3,4)]
        Output: [(2,3,4), (0,3,5), (0,2,6)] (total weight: 15)
        Explanation: MST includes cheapest edges that connect all nodes without cycles

        Constraints:
        - 1 ≤ n ≤ 1000
        - Graph is connected (spanning tree exists)
        - All edge weights are positive
        - No duplicate edges

        Complexity:
        - Time: O(E log E) for sorting edges, O(E * o(n)) for Union-Find operations
        - Space: O(n) for Union-Find data structure
        """

        result = []
        uf = self.UnionFind(n)

        edges.sort(key=lambda x: x[2])  # sort increasing order

        for e in edges:
            u, v = e[0], e[1]
            if uf.connected(u, v):
                continue
            result.append(e)
            uf.union(u, v)

        return result

        # --- Solution (commented out) ---
        # edges.sort(key=lambda x: x[2])  # Sort by weight
        # uf = self.UnionFind(n)
        # mst = []
        #
        # for u, v, weight in edges:
        #     if uf.union(u, v): # will be false if they are connected
        #         mst.append((u, v, weight))
        #         if len(mst) == n - 1:
        #             break
        #
        # return mst

    # ========== ADDITIONAL HIGH-SIGNAL PATTERNS ==========
    # Q10 (⭐) - BFS Shortest Path (Unweighted)
    def bfs_shortest_path(
        self, graph: Dict[int, List[int]], start: int, target: int
    ) -> List[int]:
        """Find shortest path between two nodes in an unweighted graph using BFS.

        Uses Breadth-First Search to find the shortest path (minimum number of edges)
        between two nodes in an unweighted graph. BFS guarantees finding the shortest
        path because it explores nodes level by level, ensuring that when the target
        is first encountered, it's via the shortest possible route.

        Args:
            graph (Dict[int, List[int]]): Adjacency list representation where keys are
                node identifiers and values are lists of connected nodes.
            start (int): The starting node for path finding.
            target (int): The destination node to reach.

        Returns:
            List[int]: A list of nodes representing the shortest path from start to
                target (inclusive). Returns an empty list if no path exists or if
                start/target nodes are not in the graph.

        Raises:
            KeyError: If start or target nodes are not present in the graph.
            TypeError: If graph is not a dictionary or contains invalid types.

        Example:
            >>> graph = {0: [1, 2], 1: [0, 3], 2: [0], 3: [1]}
            >>> bfs_shortest_path(graph, 0, 3)
            [0, 1, 3]

            >>> graph = {0: [1], 1: [2], 2: []}
            >>> bfs_shortest_path(graph, 0, 2)
            [0, 1, 2]

            >>> # Disconnected components
            >>> graph = {0: [1], 1: [0], 2: [3], 3: [2]}
            >>> bfs_shortest_path(graph, 0, 2)
            []

        Note:
            - Graph is assumed to be unweighted (all edges have equal weight)
            - Graph can be directed or undirected based on adjacency list structure
            - Self-loops and multiple edges between same nodes are handled naturally
            - If start == target, returns [start]

        Time Complexity:
            O(V + E) where V is the number of vertices and E is the number of edges.
            Each vertex and edge is visited at most once during the BFS traversal.

        Space Complexity:
            O(V) for the visited set, BFS queue, and parent tracking dictionary.
            In the worst case, all vertices may be stored in these data structures.
        """

        if start == target:
            return [start]

        # assuming visit marking happens when enqueque
        visited = set([start])
        parent: Dict[int, Optional[int]] = {start: None}
        queue = deque([start])

        while queue:
            visiting = queue.popleft()
            for nei in graph.get(visiting, []):
                if nei not in visited:
                    visited.add(nei)
                    queue.append(nei)
                    parent[nei] = visiting
                    if nei == target:
                        result = []
                        node = nei
                        while node is not None:
                            result.append(node)
                            node = parent[node]
                        return list(reversed(result))
        return []

        # --- Solution (commented out) ---
        # if start == target:
        #     return [start]

        # visited = set([start])
        # parent: Dict[int, Optional[int]] = {start: None}
        # queue: deque[int] = deque([start])

        # while queue:
        #     current = queue.popleft()

        #     for neighbor in graph.get(current, []):
        #         if neighbor not in visited:
        #             visited.add(neighbor)
        #             parent[neighbor] = current

        #             # Found target - reconstruct path
        #             if neighbor == target:
        #                 path: List[int] = []
        #                 node: Optional[int] = neighbor
        #                 while node is not None:
        #                     path.append(node)
        #                     node = parent[node]
        #                 return list(reversed(path))

        #             queue.append(neighbor)

        # # No path found
        # return []

    # Q11 (⭐⭐) - Cycle Detection (Directed)
    def has_cycle_directed(self, n: int, edges: List[Tuple[int, int]]) -> bool:
        """
        Detect if a directed graph contains a cycle using DFS with three-coloring.

        Problem:
        Given a directed graph with n nodes and a list of directed edges,
        determine if the graph contains any cycles. Use DFS with three states:
        white (unvisited), gray (visiting), black (finished). Handle disconnected
        components by iterating through all unvisited nodes.

        Parameters:
        - n: Number of nodes in the graph (labeled 0 to n-1)
        - edges: List of directed edges as (u, v) tuples meaning u → v

        Returns:
        True if the directed graph contains at least one cycle, False otherwise

        Example:
        Input: n = 3, edges = [(0, 1), (1, 2), (2, 0)]
        Output: True (cycle: 0 → 1 → 2 → 0)

        Input: n = 3, edges = [(0, 1), (1, 2)]
        Output: False (no cycles, forms a DAG)

        Input: n = 4, edges = [(0, 1), (2, 3), (3, 2)]
        Output: True (disconnected graph with cycle in component {2, 3})

        Constraints:
        - 0 ≤ n ≤ 1000
        - 0 ≤ edges.length ≤ n *= (n-1)
        - Graph may be disconnected

        Complexity:
        - Time: O(V + E) where V is vertices and E is edges
        - Space: O(V) for coloring array and recursion stack
        """

        colors = [0] * n

        graph = defaultdict(list)
        for u, v in edges:
            graph[u].append(v)

        def dfs(u):  # if cycle
            colors[u] = 1
            for v in graph[u]:
                if colors[v] == 1:
                    return True
                elif colors[v] == 0 and dfs(v):
                    return True
                # skip value 2 since they are completed
            colors[u] = 2
            return False

        for node in range(n):
            if colors[node] == 0:
                if dfs(node):
                    return True
        return False

        # --- Solution (commented out) ---
        # graph = defaultdict(list)
        # for u, v in edges:
        #     graph[u].append(v)
        # color = [0] * n  # 0=unvisited,1=visiting,2=done
        # def dfs(u: int) -> bool:
        #     color[u] = 1
        #     for v in graph[u]:
        #         if color[v] == 1:
        #             return True
        #         if color[v] == 0 and dfs(v):
        #             return True
        #     color[u] = 2
        #     return False
        # for u in range(n):
        #     if color[u] == 0 and dfs(u):
        #         return True
        # return False

    # Q12 (⭐⭐) - Topological Sort (DFS)
    def topological_sort_dfs(self, n: int, edges: List[Tuple[int, int]]) -> List[int]:
        """
        Find topological ordering using DFS post-order traversal.

        Problem:
        Given a directed acyclic graph (DAG) with n nodes and directed edges,
        compute a topological ordering using DFS. Process nodes in post-order
        and reverse the result to get valid topological ordering.

        Parameters:
        - n: Number of nodes in the graph (labeled 0 to n-1)
        - edges: List of directed edges as (u, v) tuples meaning u → v

        Returns:
        List of nodes in topological order, or empty list if cycle detected

        Example:
        Input: n = 4, edges = [(0, 1), (0, 2), (1, 3), (2, 3)]
        Output: [0, 2, 1, 3] or [0, 1, 2, 3] (both valid orderings)

        Constraints:
        - 0 ≤ n ≤ 1000
        - 0 ≤ edges.length ≤ n * (n-1)
        - Graph must be acyclic for valid solution

        Complexity:
        - Time: O(V + E) where V is vertices and E is edges
        - Space: O(V + E) for adjacency list and recursion stack
        """

        colors = [0] * n
        path = []

        graph = defaultdict(list)
        for u, v in edges:
            graph[u].append(v)

        def dfs(u):
            colors[u] = 1
            for nb in graph[u]:
                if colors[nb] == 1:
                    return True
                if colors[nb] == 0 and dfs(nb):
                    return True

            colors[u] = 2
            path.append(u)
            return False

        for node in range(n):
            if colors[node] == 0 and dfs(node):
                return []

        return list(reversed(path))

        # --- Solution (commented out) ---
        # graph = defaultdict(list)
        # for u, v in edges:
        #     graph[u].append(v)
        # color = [0] * n
        # order: List[int] = []
        #
        # def dfs(u: int) -> bool:
        #     color[u] = 1
        #     for v in graph[u]:
        #         if color[v] == 1:
        #             return True  # Cycle detected
        #         if color[v] == 0 and dfs(v):
        #             return True  # Propagate cycle detection
        #     color[u] = 2
        #     order.append(u)
        #     return False
        #
        # for u in range(n):
        #     if color[u] == 0 and dfs(u):
        #         return []  # Return empty list if cycle found
        # return list(reversed(order))

    # Q13 (⭐⭐) - Multi-source BFS (Grid)
    def multi_source_bfs_grid(
        self, grid: List[List[int]], sources: List[Tuple[int, int]], blocked: int = 1
    ) -> List[List[int]]:
        """
        Compute the shortest Manhattan distance from each cell to its nearest source
        using a multi-source BFS (all sources start in the queue at distance 0).

        Behavior:
        - Treat any cell equal to `blocked` as a wall (impassable).
        - Explore 4-directionally (up, down, left, right) only; no diagonals.
        - Out-of-bounds or blocked source coordinates are ignored.
        - The first time a cell is dequeued is its minimum distance from the nearest source.

        Parameters:
        - grid: 2D list of ints; shape m x n. Cells with value `blocked` are walls.
        - sources: List of (row, col) tuples for source locations.
        - blocked: Grid value that represents a wall (default: 1).

        Returns:
        - dist: 2D list of ints of shape m x n where:
          - dist[r][c] == 0 for source cells;
          - dist[r][c] is the minimum number of steps to the closest source via 4-neighbor moves;
          - dist[r][c] == -1 if unreachable from all sources.
        - For an empty grid (no rows or no columns), returns []. If `sources` is empty,
          the result is an m x n matrix filled with -1.

        Example:
        Input: grid = [[0,0,1,0], [0,1,0,0], [0,0,0,1], [1,0,0,0]]
               sources = [(0,0), (3,3)], blocked = 1
        Output: [[0,1,-1,3], [1,-1,2,2], [2,3,1,-1], [-1,2,1,0]]

        Constraints:
        - 1 ≤ m, n ≤ 300
        - 0 ≤ len(sources) ≤ min(m * n, 100)

        Complexity:
        - Time: O(m * n)
        - Space: O(m * n)
        """
        # Handle empty grid
        if not grid or not grid[0]:
            return []

        m, n = len(grid), len(grid[0])

        # Initialize distance grid with -1 (unvisited/unreachable)
        dist = [[-1] * n for _ in range(m)]

        # Prime queue with valid, in-bounds, non-blocked sources
        queue = deque()
        for r, c in sources:
            if 0 <= r < m and 0 <= c < n and grid[r][c] != blocked and dist[r][c] == -1:
                dist[r][c] = 0
                queue.append((r, c))

        # If no valid sources, distances remain -1
        if not queue:
            return dist

        offsets = [(1, 0), (-1, 0), (0, 1), (0, -1)]
        while queue:
            r, c = queue.popleft()
            for dr, dc in offsets:
                nr, nc = r + dr, c + dc
                if not (0 <= nr < m and 0 <= nc < n):
                    continue
                if grid[nr][nc] == blocked or dist[nr][nc] != -1:
                    continue
                dist[nr][nc] = dist[r][c] + 1
                queue.append((nr, nc))

        return dist

        # --- Solution (commented out) ---
        # if not grid or not grid[0]:
        #     return []
        # m, n = len(grid), len(grid[0])
        # dist = [[-1] * n for _ in range(m)]
        # q: Deque[Tuple[int, int]] = deque()
        # for r, c in sources:
        #     if 0 <= r < m and 0 <= c < n and grid[r][c] != blocked:
        #         dist[r][c] = 0
        #         q.append((r, c))
        # dirs = [(1,0),(-1,0),(0,1),(0,-1)]
        # while q:
        #         r, c = q.popleft()
        #         for dr, dc in dirs:
        #             nr, nc = r + dr, c + dc
        #             if 0 <= nr < m and 0 <= nc < n and grid[nr][nc] != blocked and dist[nr][nc] == -1:
        #                 dist[nr][nc] = dist[r][c] + 1
        #                 q.append((nr, nc))
        # return dist

    # Q14 (⭐⭐⭐) - 0-1 BFS
    def zero_one_bfs(
        self, graph: Dict[int, List[Tuple[int, int]]], start: int
    ) -> Dict[int, int]:
        """
        Compute shortest-path distances from a source in a graph with edge weights in {0, 1}.

        Uses the 0-1 BFS technique with a deque:
        - Push 0-weight transitions to the front (appendleft)
        - Push 1-weight transitions to the back (append)
        This achieves linear complexity in the number of vertices and edges.

        Args:
            graph: Adjacency list where graph[u] = [(v, w), ...] and w ∈ {0, 1}.
            start: Source node for distance computation.

        Returns:
            A mapping from each reachable node to its shortest distance from start.
            Unreachable nodes are omitted from the result.

        Example:
            Input:
                graph = {0: [(1, 0), (2, 1)], 1: [(3, 1)], 2: [(1, 0), (3, 0)], 3: []}
                start = 0
            Output:
                {0: 0, 1: 0, 2: 1, 3: 1}
            Explanation:
                0→1 has cost 0; 0→2 has cost 1; 0→1→3 and 0→2→3 both have cost 1.

        Constraints:
            - All edge weights must be 0 or 1 (no negatives, no weights > 1).
            - Graph may be directed or undirected per adjacency representation.

        Complexity:
            - Time: O(V + E)
            - Space: O(V)
        """

        # general algorithm: use bfs with queue, add 0 distance to front, 1 distance to back

        # create distance dict
        # create a deque, add start with distance 0 to dequeue

        # while queue is not empty
        # pop from queue
        # for each neighbor:
        # if not visisted:
        # if distance is 0, update distance and to front queue
        # if distance is 1, update distance and add to back of queue

        # return distance

        distance = {start: 0}
        queue = deque([start])

        while queue:
            current = queue.popleft()
            for nei, weight in graph.get(current, []):
                nd = distance[current] + weight
                # it is ok if it is visisted, as long as we can get a better distance
                if nd < distance.get(nei, float("inf")):
                    distance[nei] = nd
                    if weight == 0:
                        queue.appendleft(nei)
                    else:
                        queue.append(nei)
        return distance

        # --- Solution (commented out) ---
        # from collections import deque
        # INF = 10**18
        # dist: Dict[int, int] = defaultdict(lambda: INF)
        # dist[start] = 0
        # dq: Deque[int] = deque([start])
        # while dq:
        #     u = dq.popleft()
        #     for v, w in graph.get(u, []):
        #         nd = dist[u] + w
        #         if nd < dist[v]:
        #             dist[v] = nd
        #             if w == 0:
        #                 dq.appendleft(v)
        #             else:
        #                 dq.append(v)
        # return dict(dist)

    # Q15 (⭐⭐) - Course Schedule II
    def course_schedule_ii(self, n: int, prerequisites: List[List[int]]) -> List[int]:
        """
        Course Schedule II — return any valid ordering of courses.

        You are given `n` courses labeled 0..n-1 and a list of prerequisite pairs
        `prerequisites`, where each pair `[a, b]` means you must take course `b`
        before course `a` (directed edge `b → a`). Return any ordering of all
        courses that satisfies the prerequisites. If no such ordering exists due
        to a cycle, return an empty list.

        Args:
            n: Number of courses (0..n-1).
            prerequisites: List of edges `[a, b]` meaning `b` must precede `a`.

        Returns:
            A list of length `n` representing a valid course order, or `[]` if
            no valid ordering exists.

        Examples:
            - n = 2, prerequisites = [[1, 0]] → [0, 1]
            - n = 2, prerequisites = [[1, 0], [0, 1]] → []

        Constraints:
            - 1 ≤ n ≤ 2000
            - 0 ≤ len(prerequisites) ≤ 5000
            - 0 ≤ a, b < n and a ≠ b
            - No duplicate prerequisite pairs

        Edge cases to handle:
            - Courses with no prerequisites (still appear in the order)
            - Disconnected components (multiple valid answers possible)
            - Cycles (must return [])

        Approach (hint):
            Use Kahn's algorithm (BFS topological sort): build in-degrees, enqueue
            all zero in-degree nodes, repeatedly pop, append to order, and decrement
            neighbors' in-degrees. If the final order has fewer than `n` nodes, a
            cycle exists.

        Complexity:
            - Time: O(V + E)
            - Space: O(V + E)
        """

        graph = defaultdict(list)
        in_degrees = [0] * n
        order = []

        for v, u in prerequisites:
            graph[u].append(v)
            in_degrees[v] += 1

        candidates = set()
        for course, in_degree in enumerate(in_degrees):
            if in_degree == 0:
                candidates.add(course)

        while candidates:
            nxt = candidates.pop()
            order.append(nxt)
            for nei in graph[nxt]:
                in_degrees[nei] -= 1
                if in_degrees[nei] == 0:
                    candidates.add(nei)
        if len(order) != n:
            return []
        return order

        # # Build adjacency list and in-degree
        # graph = defaultdict(list)
        # in_degree = [0] * n

        # for ai, bi in prerequisites:
        #     graph[bi].append(ai)
        #     in_degree[ai] += 1

        # # Kahn's algorithm: start with nodes of in-degree 0
        # queue = deque([i for i in range(n) if in_degree[i] == 0])
        # order = []

        # while queue:
        #     node = queue.popleft()
        #     order.append(node)

        #     for neighbor in graph[node]:
        #         in_degree[neighbor] -= 1
        #         if in_degree[neighbor] == 0:
        #             queue.append(neighbor)

        # # If order length != n, cycle detected
        # return order if len(order) == n else []


def demonstrate_patterns():
    """Demonstrate usage of graph patterns."""
    patterns = GraphPatterns()

    print("=== GRAPHS DEMONSTRATIONS ===\n")

    # Q1 (⭐) DFS
    print("Q1 (⭐) DFS:")
    graph_traversal = {
        0: [1, 2],
        1: [0, 3, 4],
        2: [0, 5, 6],
        3: [1],
        4: [1],
        5: [2],
        6: [2],
    }
    print(f"   Graph: {graph_traversal}")
    print(f"   DFS from 0: {patterns.dfs_iterative(graph_traversal, 0)}")

    # Q2 (⭐) BFS
    print("\nQ2 (⭐) BFS:")
    print(f"   BFS from 0: {patterns.bfs(graph_traversal, 0)}")

    # Q3 (⭐⭐) Number of Islands
    print("\nQ3 (⭐⭐) Number of Islands:")
    grid = [
        ["1", "1", "1", "1", "0"],
        ["1", "1", "0", "1", "0"],
        ["1", "1", "0", "0", "0"],
        ["0", "0", "0", "0", "0"],
    ]
    grid_copy = [row[:] for row in grid]
    islands = patterns.number_of_islands(grid_copy)
    print(f"   Islands: {islands}")

    # Q4 (⭐⭐) Bipartite Check
    print("\nQ4 (⭐⭐) Bipartite Check:")
    graph_bip = [[1, 3], [0, 2], [1, 3], [0, 2]]
    is_bip = patterns.is_bipartite(graph_bip)
    print(f"   Graph: {graph_bip}")
    print(f"   Is bipartite: {is_bip}")

    # Q5 (⭐⭐) Cycle Detection (Undirected)
    print("\nQ5 (⭐⭐) Cycle Detection (Undirected):")
    graph_cycle = {0: [1, 2], 1: [0, 2], 2: [0, 1]}
    has_cycle = patterns.has_cycle_undirected(graph_cycle)
    print(f"   Graph: {graph_cycle}")
    print(f"   Has cycle: {has_cycle}")

    # Q6 (⭐⭐) Topological Sort
    print("\nQ6 (⭐⭐) Topological Sort:")
    edges_ts = [(5, 2), (5, 0), (4, 0), (4, 1), (2, 3), (3, 1)]
    topo_order = patterns.topological_sort_kahn(6, edges_ts)
    print(f"   Edges: {edges_ts}")
    print(f"   Topological order: {topo_order}")

    # Q7 (⭐⭐) Connected Components
    print("\nQ7 (⭐⭐) Connected Components:")
    n = 5
    edges_cc = [[0, 1], [1, 2], [3, 4]]
    components = patterns.number_of_connected_components(n, edges_cc)
    print(f"   Nodes: {n}, Edges: {edges_cc}")
    print(f"   Connected components: {components}")

    # Q8 (⭐⭐⭐) Dijkstra's Shortest Path
    print("\nQ8 (⭐⭐⭐) Dijkstra's Shortest Path:")
    weighted_graph = {0: [(1, 4), (2, 1)], 1: [(3, 1)], 2: [(1, 2), (3, 5)], 3: []}
    distances = patterns.dijkstra(weighted_graph, 0)
    print(f"   Graph: {weighted_graph}")
    print(f"   Distances from 0: {distances}")

    # Q9 (⭐⭐) Kruskal's MST
    print("\nQ9 (⭐⭐) Kruskal's MST:")
    n_mst = 4
    edges_mst = [(0, 1, 10), (0, 2, 6), (0, 3, 5), (1, 3, 15), (2, 3, 4)]
    mst = patterns.kruskal_mst(n_mst, edges_mst)
    print(f"   Nodes: {n_mst}, Edges: {edges_mst}")
    print(f"   MST edges: {mst}")

    # Q10 (⭐) BFS Shortest Path (Unweighted)
    print("\nQ10 (⭐) BFS Shortest Path (Unweighted):")
    path_0_to_3 = patterns.bfs_shortest_path(graph_traversal, 0, 3)
    print(f"   Shortest path 0 -> 3: {path_0_to_3}")

    # Q11 (⭐⭐) Cycle Detection (Directed)
    print("\nQ11 (⭐⭐) Cycle Detection (Directed):")
    edges_dir_cycle = [(0, 1), (1, 2), (2, 0)]
    has_dir_cycle = patterns.has_cycle_directed(3, edges_dir_cycle)
    print(f"   Edges: {edges_dir_cycle}")
    print(f"   Has directed cycle: {has_dir_cycle}")

    # Q12 (⭐⭐) Topological Sort (DFS)
    print("\nQ12 (⭐⭐) Topological Sort (DFS):")
    topo_order_dfs = patterns.topological_sort_dfs(6, edges_ts)
    print(f"   Edges: {edges_ts}")
    print(f"   Topological order (DFS): {topo_order_dfs}")

    # Q13 (⭐⭐) Multi-source BFS (Grid)
    print("\nQ13 (⭐⭐) Multi-source BFS (Grid):")
    grid_ms = [
        [0, 0, 1, 0],
        [0, 1, 0, 0],
        [0, 0, 0, 1],
        [1, 0, 0, 0],
    ]
    sources = [(0, 0), (3, 3)]
    dist_grid = patterns.multi_source_bfs_grid(grid_ms, sources, blocked=1)
    print(f"   Sources: {sources}")
    print(f"   Distance grid: {dist_grid}")

    # Q14 (⭐⭐) 0-1 BFS
    print("\nQ14 (⭐⭐) 0-1 BFS:")
    graph_01 = {
        0: [(1, 0), (2, 1)],
        1: [(3, 1)],
        2: [(1, 0), (3, 0)],
        3: [],
    }
    dist_01 = patterns.zero_one_bfs(graph_01, 0)
    print(f"   Graph: {graph_01}")
    print(f"   Distances (0-1 BFS) from 0: {dist_01}")

    # Q15 (⭐⭐) Course Schedule II
    print("\nQ15 (⭐⭐) Course Schedule II:")
    n_courses = 4
    prerequisites = [[1, 0], [2, 0], [3, 1], [3, 2]]
    order_courses = patterns.course_schedule_ii(n_courses, prerequisites)
    print(f"   Prerequisites: {prerequisites}")
    print(f"   Course order: {order_courses}")


if __name__ == "__main__":
    demonstrate_patterns()
