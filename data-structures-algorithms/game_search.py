"""
Game Tree Search - Minimax and Alpha-Beta Pruning (Practice Mode)
"""

from typing import List, Optional, Tuple


class MinimaxAlphaBeta:
    """Practice stubs for game-tree search algorithms.

    Includes minimax with alpha-beta pruning and simple move ordering.
    """

    def minimax_tictactoe(
        self, board: List[List[str]], player: str
    ) -> Tuple[int, Optional[Tuple[int, int]]]:
        """
        PROBLEM: Minimax with Alpha-Beta Pruning for Tic-Tac-Toe (Medium-Hard)

        Implement minimax algorithm with alpha-beta pruning to find the optimal move
        for a player in 3x3 tic-tac-toe. Return both the game outcome score and the
        best move to achieve that outcome.

        Args:
            board: 3x3 grid where board[i][j] is:
                   - "X" for X player's move
                   - "O" for O player's move
                   - "" (empty string) for available position
            player: Current player's symbol ("X" or "O")

        Returns:
            Tuple[int, Optional[Tuple[int, int]]]:
            - score: Game outcome from `player`'s perspective:
                * +1 if `player` wins with optimal play
                * -1 if `player` loses with optimal play
                * 0 if game ends in draw with optimal play
            - move: Best move as (row, col) tuple, or None if game is terminal

        Examples:
            # Game in progress - X to move
            board = [["X", "O", ""],
                     ["", "X", "O"],
                     ["", "", ""]]
            score, move = minimax_tictactoe(board, "X")
            # Returns (1, (2, 0)) - X can win by playing bottom-left

            # Terminal position - O wins
            board = [["X", "O", "X"],
                     ["O", "O", "O"],
                     ["X", "X", ""]]
            score, move = minimax_tictactoe(board, "X")
            # Returns (-1, None) - X has already lost

        Constraints:
            - board is always 3x3
            - board[i][j] in {"X", "O", ""}
            - player in {"X", "O"}
            - Board represents valid tic-tac-toe position
            - At most 9 moves in any game

        Algorithm Details:
            1. Check if current position is terminal (win/loss/draw)
            2. If terminal, return evaluation score
            3. For each valid move:
               - Make move recursively
               - Get opponent's best response using minimax
               - Undo move
            4. Return best score for current player
            5. Use alpha-beta pruning to eliminate branches that can't improve result

        Key Insights:
            - Tic-tac-toe is a solved game - perfect play leads to draw
            - Alpha-beta can reduce search space from O(9!) to much smaller tree
            - Move ordering (center, corners, edges) improves pruning effectiveness
            - Game tree depth ≤ 9, so even brute force is feasible

        Time Complexity: O(b^d) worst case, where b=branching factor, d=depth
                        - Without pruning: O(9!) ≈ O(362,880)
                        - With good pruning: Often O(b^(d/2)) ≈ O(3^4.5) ≈ O(140)
        Space Complexity: O(d) = O(9) for recursion stack

        Implementation Hints:
            1. Create helper function to check win conditions (3 in row/col/diagonal)
            2. Use alpha-beta parameters: alpha (best for MAX), beta (best for MIN)
            3. Alternate between maximizing and minimizing players each level
            4. Consider move ordering: center > corners > edges for better pruning
            5. Handle draw condition: no winner and board full
        """

        # Helper: check if a player has won
        def has_won(sym: str) -> bool:
            lines = []
            # Rows and columns
            for i in range(3):
                lines.append([board[i][0], board[i][1], board[i][2]])  # each row
                lines.append([board[0][i], board[1][i], board[2][i]])  # each col
            # Diagonals
            lines.append([board[0][0], board[1][1], board[2][2]])
            lines.append([board[0][2], board[1][1], board[2][0]])
            return any(all(cell == sym for cell in line) for line in lines)

        # Helper: check if board is full (draw when no winner)
        def is_full() -> bool:
            return all(board[r][c] != "" for r in range(3) for c in range(3))

        # Helper: generate available moves with heuristic ordering
        def generate_moves() -> List[Tuple[int, int]]:
            # Preferred order: center, corners, edges
            candidates = [
                (1, 1),
                (0, 0),
                (0, 2),
                (2, 0),
                (2, 2),
                (0, 1),
                (1, 0),
                (1, 2),
                (2, 1),
            ]
            return [(r, c) for (r, c) in candidates if board[r][c] == ""]

        original_player = player
        opponent = "O" if original_player == "X" else "X"

        # Evaluate terminal states from original player's perspective
        def evaluate_terminal() -> Optional[int]:
            if has_won(original_player):
                return 1
            if has_won(opponent):
                return -1
            if is_full():
                return 0
            return None  # Non-terminal

        # Minimax with alpha-beta pruning
        def search(
            current: str, alpha: int, beta: int
        ) -> Tuple[int, Optional[Tuple[int, int]]]:
            """Recursively compute best outcome using minimax with alpha-beta pruning.

            Args:
                current: Symbol of the side to move ("X" or "O").
                alpha: Lower bound on the achievable score for the maximizer
                    along the current path (best guaranteed value for MAX).
                beta: Upper bound on the achievable score for the minimizer
                    along the current path (best guaranteed value for MIN).

            Returns:
                Tuple of (score, best_move) from the perspective of the
                original player where score ∈ {-1, 0, 1} and best_move is
                (row, col) or None at terminal nodes.
            """
            term = evaluate_terminal()
            if term is not None:
                return term, None  # Terminal node: utility is known; no move

            # True if this node represents the original player's turn
            is_maximizing = current == original_player
            # Initialize outside possible utilities (-1, 0, 1) to act like ±∞
            best_score = -10 if is_maximizing else 10
            best_move: Optional[Tuple[int, int]] = None

            # Try legal moves in heuristic order to improve pruning
            for r, c in generate_moves():
                # Make move on the board
                board[r][c] = current
                # Alternate to the opponent for the next ply
                next_player = (
                    opponent if current == original_player else original_player
                )

                # Evaluate the child position
                score, _ = search(next_player, alpha, beta)

                # Undo move to restore board state
                board[r][c] = ""

                if is_maximizing:
                    if score > best_score:
                        best_score, best_move = score, (r, c)
                    # Update alpha: best score MAX can force so far
                    alpha = max(alpha, best_score)
                    if beta <= alpha:
                        break  # Beta cut-off: MIN has a better alternative
                else:
                    if score < best_score:
                        best_score, best_move = score, (r, c)
                    # Update beta: best score MIN can force so far
                    beta = min(beta, best_score)
                    if beta <= alpha:
                        break  # Alpha cut-off: MAX has a better alternative

            return best_score, best_move

        return search(player, alpha=-10, beta=10)


def run_minimax_tests() -> None:
    """Run concise, high-signal tests for minimax with alpha-beta on tic-tac-toe.

    Tests focus on canonical scenarios: immediate win, terminal loss, optimal
    opening, forced block, and terminal draw. Uses assertions for verification
    and prints a brief summary suitable for quick practice runs.
    """

    engine = MinimaxAlphaBeta()

    # Test 1: Immediate win available for X (play at (0,2))
    board1 = [
        ["X", "X", ""],
        ["O", "O", ""],
        ["", "", ""],
    ]
    score1, move1 = engine.minimax_tictactoe([row[:] for row in board1], "X")
    assert score1 == 1 and move1 == (0, 2)

    # Test 2: Terminal loss for X (O already wins)
    board2 = [
        ["X", "O", "X"],
        ["O", "O", "O"],
        ["X", "X", ""],
    ]
    score2, move2 = engine.minimax_tictactoe([row[:] for row in board2], "X")
    assert score2 == -1 and move2 is None

    # Test 3: Optimal opening from empty board is draw with best play; prefer center
    board3 = [["", "", ""], ["", "", ""], ["", "", ""]]
    score3, move3 = engine.minimax_tictactoe([row[:] for row in board3], "X")
    assert score3 == 0 and move3 == (1, 1)

    # Test 4: Forced block — X must block O's win at (0,2)
    board4 = [
        ["O", "O", ""],
        ["X", "", ""],
        ["", "", "X"],
    ]
    score4, move4 = engine.minimax_tictactoe([row[:] for row in board4], "X")
    assert move4 == (0, 2)

    # Test 5: Terminal draw board
    board5 = [
        ["X", "O", "X"],
        ["X", "O", "O"],
        ["O", "X", "X"],
    ]
    score5, move5 = engine.minimax_tictactoe([row[:] for row in board5], "X")
    assert score5 == 0 and move5 is None

    print("=== Minimax Alpha-Beta Tic-Tac-Toe: All self-tests passed ✅ ===")


if __name__ == "__main__":
    run_minimax_tests()
