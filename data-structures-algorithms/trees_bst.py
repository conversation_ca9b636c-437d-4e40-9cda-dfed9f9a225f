"""
Trees and Binary Search Trees - Essential Patterns for Coding Interviews

This module covers tree-based algorithms frequently tested in coding interviews
at top tech companies like Google.

Key Patterns Covered:
1. Tree Traversals (Inorder, Preorder, Level-order)
2. Binary Search Tree Operations
3. Tree Construction and Serialization
4. Lowest Common Ancestor
5. Path and Tree Properties

Time/Space Complexity Analysis and Use Cases included for each pattern.
"""

from typing import Optional, List
from collections import deque


class TreeNode:
    """Definition for a binary tree node."""

    def __init__(
        self,
        val: int = 0,
        left: Optional["TreeNode"] = None,
        right: Optional["TreeNode"] = None,
    ):
        self.val = val
        self.left = left
        self.right = right

    def __repr__(self):
        """String representation for debugging."""

        def _build_tree_string(node, curr_index, include_index=False, delimiter="-"):
            if node is None:
                return [], 0, 0, 0

            line1 = []
            line2 = []
            if include_index:
                node_repr = f"{node.val}({curr_index})"
            else:
                node_repr = str(node.val)

            new_root_width = gap_size = len(node_repr)

            l_box, l_box_width, l_root_start, l_root_end = (
                _build_tree_string(
                    node.left, 2 * curr_index + 1, include_index, delimiter
                )
                if node.left
                else ([], 0, 0, 0)
            )
            r_box, r_box_width, r_root_start, r_root_end = (
                _build_tree_string(
                    node.right, 2 * curr_index + 2, include_index, delimiter
                )
                if node.right
                else ([], 0, 0, 0)
            )

            if l_box_width > 0:
                l_root = (l_root_start + l_root_end) // 2 + 1
                line1.append(" " * (l_root + 1))
                line1.append(delimiter * (l_box_width - l_root))
                line2.append(" " * l_root + "/")
                line2.append(" " * (l_box_width - l_root))
                new_root_start = l_box_width + 1
                gap_size += 1
            else:
                new_root_start = 0

            line1.append(node_repr)
            line2.append(" " * new_root_width)

            if r_box_width > 0:
                r_root = (r_root_start + r_root_end) // 2
                line1.append(delimiter * r_root)
                line1.append(" " * (r_box_width - r_root + 1))
                line2.append(" " * r_root + "\\")
                line2.append(" " * (r_box_width - r_root))
                gap_size += 1

            new_root_end = new_root_start + new_root_width - 1

            gap = " " * gap_size
            new_box = ["".join(line1), "".join(line2)]
            for i in range(max(len(l_box), len(r_box))):
                left_line = l_box[i] if i < len(l_box) else " " * l_box_width
                right_line = r_box[i] if i < len(r_box) else " " * r_box_width
                new_box.append(left_line + gap + right_line)

            return new_box, len(new_box[0]), new_root_start, new_root_end

        lines = _build_tree_string(self, 0, False, "-")[0]
        return "\n" + "\n".join(lines)


class TreePatterns:
    """Collection of essential tree algorithms for coding interviews."""

    # ========== TREE TRAVERSALS ==========
    # Q1 ⭐⭐
    def inorder_traversal(self, root: Optional[TreeNode]) -> List[int]:
        """
        PROBLEM: Binary Tree Inorder Traversal (Medium)

        Given the root of a binary tree, return the inorder traversal of its nodes' values.
        Inorder traversal visits nodes in Left -> Root -> Right order.

        Example 1:
        Input: root = [1,null,2,3]
        Output: [1,3,2]

        Example 2:
        Input: root = []
        Output: []

        Constraints:
        - The number of nodes in the tree is in range [0, 100]
        - -100 <= Node.val <= 100

        Time Complexity: O(n)
        Space Complexity: O(h) where h is height

        Hint: For BST, inorder traversal gives sorted order
        """

        result = []

        # use recursion
        def inorder(root):
            if root:
                inorder(root.left)  # First: LEFT subtree
                result.append(root.val)  # Second: ROOT
                inorder(root.right)  # Third: RIGHT subtree

        inorder(root)
        return result

    # Q2 ⭐⭐
    def preorder_traversal(self, root: Optional[TreeNode]) -> List[int]:
        """
        PROBLEM: Binary Tree Preorder Traversal (Medium)

        Given the root of a binary tree, return the preorder traversal of its nodes' values.
        Preorder traversal visits nodes in Root -> Left -> Right order.

        Example 1:
        Input: root = [1,null,2,3]
        Output: [1,2,3]

        Example 2:
        Input: root = []
        Output: []

        Constraints:
        - The number of nodes in the tree is in range [0, 100]
        - -100 <= Node.val <= 100

        Time Complexity: O(n)
        Space Complexity: O(h)

        Hint: Useful for tree serialization and copying trees
        """

        result = []

        def preorder(node):
            if node:
                result.append(node.val)
                preorder(node.left)
                preorder(node.right)

        preorder(root)
        return result

    # Q3 ⭐⭐
    def level_order_traversal(self, root: Optional[TreeNode]) -> List[List[int]]:
        """
        PROBLEM: Binary Tree Level Order Traversal (Medium)

        Given the root of a binary tree, return the level order traversal of its nodes' values.
        (i.e., from left to right, level by level).

        Example 1:
        Input: root = [3,9,20,null,null,15,7]
        Output: [[3],[9,20],[15,7]]

        Example 2:
        Input: root = [1]
        Output: [[1]]

        Constraints:
        - The number of nodes in the tree is in range [0, 2000]
        - -1000 <= Node.val <= 1000

        Time Complexity: O(n)
        Space Complexity: O(w) where w is maximum width

        Hint: Use BFS with queue, process each level separately
        """

        if not root:
            return []

        result = []
        queue = deque([root])

        while queue:
            level_size = len(queue)  # varies not necessarily full
            level_values = []

            for _ in range(level_size):
                node = queue.popleft()
                level_values.append(node.val)

                if node.left:
                    queue.append(node.left)
                if node.right:
                    queue.append(node.right)

            result.append(level_values)

        return result

    # ========== BST OPERATIONS ==========
    # Q4 ⭐
    def search_bst(self, root: Optional[TreeNode], val: int) -> Optional[TreeNode]:
        """
        PROBLEM: Search in a Binary Search Tree (Easy)

        Given the root of a binary search tree and a value, find the node in the BST
        that the node's value equals the given value.

        Example 1:
        Input: root = [4,2,7,1,3], val = 2
        Output: [2,1,3]

        Example 2:
        Input: root = [4,2,7,1,3], val = 5
        Output: []

        Constraints:
        - The number of nodes in the tree is in range [1, 5000]
        - 1 <= Node.val <= 10^7
        - root is a binary search tree

        Time Complexity: O(h) where h is height
        Space Complexity: O(1) iterative, O(h) recursive

        Hint: Use BST property to eliminate half the tree at each step
        """

        while root and root.val != val:
            root = root.left if val < root.val else root.right
        return root

    # Q5 ⭐⭐
    def insert_into_bst(self, root: Optional[TreeNode], val: int) -> Optional[TreeNode]:
        """
        PROBLEM: Insert into a Binary Search Tree (Medium)

        Given the root of a binary search tree and a value to insert, insert the value
        into the BST and return the root of the BST after insertion.

        Example 1:
        Input: root = [4,2,7,1,3], val = 5
        Output: [4,2,7,1,3,5]

        Example 2:
        Input: root = [40,20,60,10,30,50,70], val = 25
        Output: [40,20,60,10,30,50,70,null,null,25]


        Constraints:
        - The number of nodes in the tree is in range [0, 10^4]
        - -10^8 <= Node.val <= 10^8
        - All values are unique

        Time Complexity: O(h)
        Space Complexity: O(h) for recursion

        Hint: Compare with current node value and recurse left or right
        Hint 2: If root is None, create a new node and return it; otherwise return the original root after insertion
        """

        if not root:
            return TreeNode(val)

        if val < root.val:
            root.left = self.insert_into_bst(root.left, val)
        elif val > root.val:
            root.right = self.insert_into_bst(root.right, val)

        return root

    # Q6 ⭐⭐
    def validate_bst(self, root: Optional[TreeNode]) -> bool:
        """
        PROBLEM: Validate Binary Search Tree (Medium)

        Given the root of a binary tree, determine if it is a valid binary search tree.
        A valid BST has left subtree values < root < right subtree values.

        Example 1:
        Input: root = [2,1,3]
        Output: true

        Example 2:
        Input: root = [5,1,4,null,null,3,6]
        Output: false
        Explanation: Root is 5 but right child 4 < 5

        Constraints:
        - The number of nodes in the tree is in range [1, 10^4]
        - -2^31 <= Node.val <= 2^31 - 1

        Time Complexity: O(n)
        Space Complexity: O(h)

        Hint: Use min/max bounds for each subtree, not just parent comparison
        Hint 2: Alternatively, use inorder traversal and ensure values strictly increase
        """

        # Method 1
        prev = float("-inf")

        def inorder(node: Optional[TreeNode]) -> bool:
            nonlocal prev
            if not node:
                return True
            if not inorder(node.left):
                return False
            if node.val <= prev:  # strictly increasing (no duplicates)
                return False
            prev = node.val  # update last seen value
            return inorder(node.right)

        return inorder(root)

        # # Method 2: Review
        # def is_valid(node, min_val, max_val):
        #     if not node:
        #         return True

        #     if node.val <= min_val or node.val >= max_val:
        #         return False

        #     return is_valid(node.left, min_val, node.val) and is_valid(
        #         node.right, node.val, max_val
        #     )

        # return is_valid(root, float("-inf"), float("inf"))

    # Q7 ⭐⭐
    def kth_smallest_bst(self, root: Optional[TreeNode], k: int) -> int:
        """
        PROBLEM: Kth Smallest Element in BST (Medium)

        Given the root of a binary search tree and integer k, return the kth smallest
        value (1-indexed) of all values in the tree.

        Example 1:
        Input: root = [3,1,4,null,2], k = 1
        Output: 1

        Example 2:
        Input: root = [5,3,6,2,4,null,null,1], k = 3
        Output: 3

        Constraints:
        - The number of nodes in the tree is n
        - 1 <= k <= n <= 10^4
        - 0 <= Node.val <= 10^4

        Time Complexity: O(h + k)
        Space Complexity: O(h)

        Hint: Inorder traversal of BST gives sorted order, stop at kth element
        """

        if k < 1:
            return -1

        counter = 0

        def inorder(node: Optional[TreeNode]):
            nonlocal counter
            if not node:
                return None

            left_result = inorder(node.left)
            if left_result is not None:
                return left_result

            counter += 1
            if counter == k:  # early stopping
                return node.val

            return inorder(node.right)

        result = inorder(root)
        return -1 if result is None else result

    # ========== TREE CONSTRUCTION ==========
    # Q8 ⭐⭐
    def build_tree_preorder_inorder(
        self, preorder: List[int], inorder: List[int]
    ) -> Optional[TreeNode]:
        """
        PROBLEM: Construct Binary Tree from Preorder and Inorder Traversal (Medium)

        Given two arrays preorder and inorder where preorder is the preorder traversal
        and inorder is the inorder traversal of the same tree, construct and return the binary tree.

        Example 1:
        Input: preorder = [3,9,20,15,7], inorder = [9,3,15,20,7]
        Output: [3,9,20,null,null,15,7]

        Example 2:
        Input: preorder = [-1], inorder = [-1]
        Output: [-1]

        Constraints:
        - 1 <= preorder.length <= 3000
        - inorder.length == preorder.length
        - -3000 <= preorder[i], inorder[i] <= 3000
        - All elements are unique

        Time Complexity: O(n)
        Space Complexity: O(n)

        Hint: First element of preorder is root, use inorder to split left/right subtrees
        """

        if not preorder or not inorder:
            return None

        # Build index map for inorder
        inorder_map = {val: i for i, val in enumerate(inorder)}
        self.preorder_idx = 0

        def build(
            left, right
        ):  # left and right index of inorder, keep track of root serparately
            if left > right:
                return None

            # Root is current preorder element
            root_val = preorder[self.preorder_idx]
            self.preorder_idx += 1
            root = TreeNode(root_val)

            # Find root position in inorder
            root_idx = inorder_map[root_val]

            # Build left and right subtrees
            root.left = build(left, root_idx - 1)
            root.right = build(root_idx + 1, right)

            return root

        return build(0, len(inorder) - 1)

    # Q9 ⭐⭐⭐
    def serialize_tree(self, root: Optional[TreeNode]) -> str:
        """
        PROBLEM: Serialize and Deserialize Binary Tree (Hard)

        Design an algorithm to serialize and deserialize a binary tree.
        Serialization is converting a tree to a single string so that it can be sent over
        the network and the original tree structure can be recovered.

        Example 1:
        Input: root = [1,2,3,null,null,4,5]
        Output: "1,2,null,null,3,4,null,null,5,null,null"

        Example 2:
        Input: root = []
        Output: "null"

        Constraints:
        - The number of nodes in the tree is in range [0, 10^4]
        - -1000 <= Node.val <= 1000

        Time Complexity: O(n)
        Space Complexity: O(n)

        Hint: Use preorder traversal with null markers for serialization
        """

        # basically traversal with preorder (in-order doesn't work) and join them to a string
        # TODO: review
        tokens = []

        def dfs(node):
            if node is None:
                tokens.append("null")
                return
            tokens.append(str(node.val))  # preorder
            dfs(node.left)
            dfs(node.right)

        dfs(root)
        return ",".join(tokens)

    def deserialize_tree(self, data: str) -> Optional[TreeNode]:
        """
        Deserialize string back to binary tree.

        Time Complexity: O(n)
        Space Complexity: O(n)

        Hint: Use iterator to track position in serialized string
        """
        values = iter(data.split(","))

        def deserialize():
            val = next(values)
            if val == "null":
                return None

            node = TreeNode(int(val))
            node.left = deserialize()
            node.right = deserialize()
            return node

        return deserialize()

    # ========== LOWEST COMMON ANCESTOR ==========
    # Q10 ⭐⭐
    def lca_binary_tree(
        self, root: Optional[TreeNode], p: TreeNode, q: TreeNode
    ) -> Optional[TreeNode]:
        """
        PROBLEM: Lowest Common Ancestor of a Binary Tree (Medium)

        Given a binary tree, find the lowest common ancestor (LCA) of two given nodes.
        The LCA is the lowest node that has both p and q as descendants.

        Example 1:
        Input: root = [3,5,1,6,2,0,8,null,null,7,4], p = 5, q = 1
        Output: 3
        Explanation: LCA of nodes 5 and 1 is 3

        Example 2:
        Input: root = [3,5,1,6,2,0,8,null,null,7,4], p = 5, q = 4
        Output: 5

        Constraints:
        - The number of nodes in the tree is in range [2, 10^5]
        - All Node.val are unique
        - p != q
        - p and q exist in the tree

        Time Complexity: O(n)
        Space Complexity: O(h)

        Hint: Recurse on left and right; if both return non-null, the current node is
        the LCA. Otherwise, return the non-null result.
        """

        def pq_in(node):
            if node is None:
                return None
            if node is p or node is q:
                return node

            left = pq_in(node.left)
            right = pq_in(node.right)
            if left and right:
                return node  # found it!
            # if one is not null, return it, esle return
            return left or right  # pass along, both None or only one is not null

        return pq_in(root)

    # ========== PATH PROBLEMS ==========
    # Q11 ⭐⭐
    def has_path_sum(self, root: Optional[TreeNode], target_sum: int) -> bool:
        """
        PROBLEM: Path Sum (Easy)

        Given the root of a binary tree and an integer targetSum, return true if the tree
        has a root-to-leaf path such that adding up all values equals targetSum.

        Example 1:
        Input: root = [5,4,8,11,null,13,4,7,2,null,null,null,1], targetSum = 22
        Output: true

        Example 2:
        Input: root = [1,2,3], targetSum = 5
        Output: false

        Constraints:
        - The number of nodes in the tree is in range [0, 5000]
        - -1000 <= Node.val <= 1000
        - -1000 <= targetSum <= 1000

        Time Complexity: O(n)
        Space Complexity: O(h)

        Hint: Reduce target by current node value, check if leaf equals remaining target
        """

        if not root:
            return False

        if not root.left and not root.right:  # Leaf node
            return root.val == target_sum

        remaining = target_sum - root.val
        return self.has_path_sum(root.left, remaining) or self.has_path_sum(
            root.right, remaining
        )

    # Q12 ⭐⭐⭐
    def binary_tree_maximum_path_sum(self, root: Optional[TreeNode]) -> int:
        """
        PROBLEM: Binary Tree Maximum Path Sum (Hard)

        A path in a binary tree is a sequence of nodes where each pair of adjacent nodes
        has an edge. The path does not need to pass through the root. Return the maximum
        sum of any non-empty path.

        Example 1:
        Input: root = [1,2,3]
        Output: 6
        Explanation: Optimal path is 2 -> 1 -> 3 with sum 6

        Example 2:
        Input: root = [-10,9,20,null,null,15,7]
        Output: 42
        Explanation: Optimal path is 15 -> 20 -> 7 with sum 42

        Constraints:
        - The number of nodes in the tree is in range [1, 3 * 10^4]
        - -1000 <= Node.val <= 1000

        Time Complexity: O(n)
        Space Complexity: O(h)

        Hint: For each node, consider max path passing through it as root
        """

        max_len = float("-inf")

        def max_path(node):
            nonlocal max_len
            if node is None:
                return 0

            # Hint:
            # - Compute one-armed gains from children (clamp negatives to 0).
            # - Update global best with split path through this node: left_gain + node.val + right_gain.
            # - Return only a one-armed path to parent: node.val + max(left_gain, right_gain).
            max_left = max(0, max_path(node.left))
            max_right = max(0, max_path(node.right))

            max_len = max(max_len, max_left + max_right + node.val)
            return node.val + max(max_left, max_right)

        _ = max_path(root)

        return int(max_len)

    # Q13 ⭐⭐
    def diameter_of_binary_tree(self, root: Optional[TreeNode]) -> int:
        """
        PROBLEM: Diameter of Binary Tree (Easy)

        Given the root of a binary tree, return the length of the diameter of the tree.
        The diameter is the length of the longest path between any two nodes in the tree.

        Example 1:
        Input: root = [1,2,3,4,5]
        Output: 3
        Explanation: Path is [4,2,1,3] or [5,2,1,3]

        Example 2:
        Input: root = [1,2]
        Output: 1

        Constraints:
        - The number of nodes in the tree is in range [1, 10^4]
        - -100 <= Node.val <= 100

        Time Complexity: O(n)
        Space Complexity: O(h)

        Hint: Diameter at each node is sum of max depths of left and right subtrees
        """

        diameter = 0

        def depth(
            node,
        ):
            nonlocal diameter
            if not node:
                return 0
            left_depth = depth(node.left)
            right_depth = depth(node.right)

            diameter = max(diameter, left_depth + right_depth)
            return 1 + max(left_depth, right_depth)

        _ = depth(root)

        return diameter

    # ========== TREE PROPERTIES ==========
    # Q14 ⭐⭐
    def is_balanced(self, root: Optional[TreeNode]) -> bool:
        """
        PROBLEM: Balanced Binary Tree (Easy)

        Given a binary tree, determine if it is height-balanced.
        A height-balanced tree is one where the depth of left and right subtrees
        of every node differ by at most 1.

        Example 1:
        Input: root = [3,9,20,null,null,15,7]
        Output: true

        Example 2:
        Input: root = [1,2,2,3,3,null,null,4,4]
        Output: false

        Constraints:
        - The number of nodes in the tree is in range [0, 5000]
        - -10^4 <= Node.val <= 10^4

        Time Complexity: O(n)
        Space Complexity: O(h)

        Hint: Return -1 for unbalanced subtree, actual height for balanced subtree
        """

        def depth(node) -> int:
            if node is None:
                return 0

            left_depth = depth(node.left)
            if left_depth == -1:  # TODO: pass -1 if not balanced
                return -1

            right_depth = depth(node.right)
            if right_depth == -1:
                return -1

            if abs(left_depth - right_depth) > 1:
                return -1

            return 1 + max(left_depth, right_depth)

        return depth(root) != -1


def demonstrate_patterns():
    """Demonstrate usage of tree patterns."""
    patterns = TreePatterns()

    print("=== TREES AND BST DEMONSTRATIONS ===\n")

    # Create test tree:    3
    #                    /   \
    #                   9     20
    #                        /  \
    #                       15   7
    root = TreeNode(3)
    root.left = TreeNode(9)
    root.right = TreeNode(20)
    root.right.left = TreeNode(15)
    root.right.right = TreeNode(7)

    print(f"Test tree:{root}")

    # Traversals
    print("\n1. Tree Traversals:")
    print(f"   Inorder: {patterns.inorder_traversal(root)}")
    print(f"   Preorder: {patterns.preorder_traversal(root)}")
    print(f"   Level-order: {patterns.level_order_traversal(root)}")
    # Edge cases
    print(f"   Inorder (empty): {patterns.inorder_traversal(None)}")
    print(f"   Level-order (empty): {patterns.level_order_traversal(None)}")

    # BST operations
    print("\n2. BST Validation:")
    bst_root = TreeNode(5)
    bst_root.left = TreeNode(3)
    bst_root.right = TreeNode(8)
    bst_root.left.left = TreeNode(1)
    bst_root.left.right = TreeNode(4)
    print(f"   Is valid BST: {patterns.validate_bst(bst_root)}")
    # Invalid BST
    invalid = TreeNode(5)
    invalid.left = TreeNode(1)
    invalid.right = TreeNode(4)
    invalid.right.left = TreeNode(3)
    invalid.right.right = TreeNode(6)
    print(f"   Is valid BST (invalid case): {patterns.validate_bst(invalid)}")
    # Duplicate value case (should be False with strict BST)
    dup = TreeNode(2)
    dup.left = TreeNode(2)
    print(f"   Is valid BST (duplicate left): {patterns.validate_bst(dup)}")

    # Path sum
    print("\n3. Path Sum:")
    target = 22
    has_path = patterns.has_path_sum(root, target)
    print(f"   Has path sum {target}: {has_path}")
    print(f"   Has path sum 0 on empty: {patterns.has_path_sum(None, 0)}")
    neg_root = TreeNode(-2)
    neg_root.right = TreeNode(-3)
    print(f"   Has path sum -5 on [-2,null,-3]: {patterns.has_path_sum(neg_root, -5)}")

    # Tree construction & serialization
    print("\n4. Tree Construction:")
    serialized = patterns.serialize_tree(root)
    print(f"   Serialized: {serialized}")
    # Deserialize and re-serialize roundtrip
    des = patterns.deserialize_tree(serialized)
    print(f"   Roundtrip equal: {patterns.serialize_tree(des) == serialized}")
    print(f"   Serialize empty: {patterns.serialize_tree(None)}")
    # Build tree from preorder/inorder and verify traversals
    preorder = [3, 9, 20, 15, 7]
    inorder = [9, 3, 15, 20, 7]
    built = patterns.build_tree_preorder_inorder(preorder, inorder)
    print(
        f"   Build OK (preorder match): {patterns.preorder_traversal(built) == preorder}"
    )
    print(
        f"   Build OK (inorder match): {patterns.inorder_traversal(built) == inorder}"
    )

    # Tree properties
    print("\n5. Tree Properties:")
    print(f"   Is balanced: {patterns.is_balanced(root)}")
    # Unbalanced example
    unb = TreeNode(1)
    unb.left = TreeNode(2)
    unb.left.left = TreeNode(3)
    print(f"   Is balanced (unbalanced chain): {patterns.is_balanced(unb)}")
    print(f"   Diameter: {patterns.diameter_of_binary_tree(root)}")
    print(f"   Max path sum: {patterns.binary_tree_maximum_path_sum(root)}")

    # 6. BST Kth Smallest
    print("\n6. Kth Smallest in BST:")
    print(f"   k=3 in bst_root: {patterns.kth_smallest_bst(bst_root, 3)}")
    print(f"   k out of range -> -1: {patterns.kth_smallest_bst(bst_root, 100)}")

    # 7. LCA in Binary Tree
    print("\n7. LCA in Binary Tree:")

    def find(node: Optional[TreeNode], val: int) -> Optional[TreeNode]:
        if not node:
            return None
        if node.val == val:
            return node
        left = find(node.left, val)
        return left if left else find(node.right, val)

    lca_tree = create_tree_from_array([3, 5, 1, 6, 2, 0, 8, None, None, 7, 4])
    p = find(lca_tree, 5)
    q = find(lca_tree, 1)
    lca = patterns.lca_binary_tree(lca_tree, p, q) if p and q else None
    print(f"   LCA(5,1) value (expected 3): {lca.val if lca else None}")
    p2 = find(lca_tree, 5)
    q2 = find(lca_tree, 4)
    lca2 = patterns.lca_binary_tree(lca_tree, p2, q2) if p2 and q2 else None
    print(f"   LCA(5,4) value (expected 5): {lca2.val if lca2 else None}")


# Helper function to create tree from level-order array
def create_tree_from_array(arr: List[Optional[int]]) -> Optional[TreeNode]:
    """Create binary tree from level-order array representation."""
    if not arr:
        return None

    root_val = arr[0]
    assert root_val is not None
    root = TreeNode(root_val)
    queue = deque([root])
    i = 1

    while queue and i < len(arr):
        node = queue.popleft()

        if i < len(arr) and arr[i] is not None:
            left_val = arr[i]
            assert left_val is not None
            node.left = TreeNode(left_val)
            queue.append(node.left)
        i += 1

        if i < len(arr) and arr[i] is not None:
            right_val = arr[i]
            assert right_val is not None
            node.right = TreeNode(right_val)
            queue.append(node.right)
        i += 1

    return root


if __name__ == "__main__":
    demonstrate_patterns()
