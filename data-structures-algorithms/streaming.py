"""
Streaming & Sketching Data Structures - Essential Coding Interview Problems

This module contains 1 carefully selected streaming data structure problem that covers
essential patterns for handling large-scale data streams and approximate algorithms,
frequently tested at top tech companies (Google, Facebook, Amazon, etc.).

Problem Difficulty Levels:
- Medium (1): LRU Cache

Each problem includes detailed question descriptions, constraints, examples,
and hints for optimal solutions. These data structures are crucial for system design
interviews and real-world applications dealing with massive data streams.
"""

from __future__ import annotations

import collections
from dataclasses import dataclass


class LRUCacheV1:
    """Essential streaming data structure for coding interviews."""

    # Q1 ⭐⭐
    def __init__(self, capacity: int):
        """
                PROBLEM: LRU Cache (Medium)

                Design a data structure that follows the constraints of a Least Recently Used (LRU) cache.

                Implement the LRUCache class:
                - LRUCache(int capacity) Initialize the LRU cache with positive size capacity
                - int get(int key) Return the value of the key if exists, otherwise return -1
                - void put(int key, int value) Update the value of the key if exists. Otherwise,
                  add the key-value pair to the cache. If the number of keys exceeds the capacity
                  from this operation, evict the least recently used key.

                The functions get and put must each run in O(1) average time complexity.

                Example 1:
                Input: ["LRUCache", "put", "put", "get", "put", "get", "put", "get", "get", "get"]
                       [[2], [1, 1], [2, 2], [1], [3, 3], [2], [4, 4], [1], [3], [4]]
                Output: [null, null, null, 1, null, -1, null, -1, 3, 4]

                Explanation:
                LRUCache lRUCache = new LRUCache(2);
                lRUCache.put(1, 1); # cache is {1=1}
                lRUCache.put(2, 2); # cache is {1=1, 2=2}
                lRUCache.get(1);    # return 1
                lRUCache.put(3, 3); # LRU key was 2, evicts key 2, cache is {1=1, 3=3}
                lRUCache.get(2);    # returns -1 (not found)
                lRUCache.put(4, 4); # LRU key was 1, evicts key 1, cache is {4=4, 3=3}
                lRUCache.get(1);    # return -1 (not found)
                lRUCache.get(3);    # return 3
                lRUCache.get(4);    # return 4

                Constraints:
                - 1 <= capacity <= 3000
                - 0 <= key <= 10^4
                - 0 <= value <= 10^5
                - At most 2 * 10^5 calls will be made to get and put
        `
                Time Complexity: O(1) for both get and put operations
                Space Complexity: O(capacity)

                Hints:
                1. Use hashmap for O(1) key lookup
                2. Use doubly linked list for O(1) insertion/deletion
                3. HashMap points to list nodes for direct access
                4. Move accessed nodes to head, evict from tail
                5. Maintain head and tail dummy nodes for easier edge case handling
        """
        if capacity <= 0:
            raise ValueError("capacity must be positive")
        self.capacity = capacity
        # Use OrderedDict to maintain access order for O(1) operations
        self._cache: collections.OrderedDict[int, int] = collections.OrderedDict()

    def get(self, key: int) -> int:
        """Return value if present; otherwise -1. Mark as recently used."""
        val = self._cache.get(key)
        if val is None:
            return -1
        # Move key to the end to mark as recently used
        self._cache.move_to_end(key)
        return val

    def put(self, key: int, value: int) -> None:
        """Insert or update value; evict LRU if over capacity."""
        if key in self._cache:
            # Update and move to end (recently used)
            self._cache[key] = value
            self._cache.move_to_end(key)
        else:
            # Insert new
            self._cache[key] = value  # already at the end
            # Evict least-recently-used if over capacity
            if len(self._cache) > self.capacity:
                # Popitem(last=False) pops the first item (LRU)
                self._cache.popitem(last=False)


@dataclass
class _Node:
    """Doubly linked list node for LRU entries.

    Attributes:
        key: Cache key (None for sentinel nodes).
        value: Stored integer value.
        prev: Previous node in the list.
        next: Next node in the list.
    """

    key: int | None
    value: int
    prev: "_Node | None" = None
    next: "_Node | None" = None


class LRUCache:
    """LRU Cache implemented with hashmap + doubly linked list.

    This version achieves O(1) average time for get/put by mapping keys to
    linked-list nodes and maintaining recency by moving nodes on access.
    """

    # Q1 ⭐⭐
    def __init__(self, capacity: int):
        """Initialize the cache.

        Args:
            capacity: Maximum number of entries (must be > 0).

        Raises:
            ValueError: If capacity <= 0.
        """
        if capacity <= 0:
            raise ValueError("capacity must be positive")
        self.capacity = capacity
        self._map: dict[int, _Node] = {}
        # Sentinels to avoid edge-case checks
        self._head: _Node = _Node(None, 0)  # LRU side
        self._tail: _Node = _Node(None, 0)  # MRU side
        self._head.next = self._tail
        self._tail.prev = self._head

    def _add_to_tail(self, node: _Node) -> None:
        """Add node right before tail (mark as MRU)."""
        prev = self._tail.prev  # type: ignore[assignment]
        assert prev is not None
        node.prev = prev
        node.next = self._tail
        prev.next = node
        self._tail.prev = node

    def _remove(self, node: _Node) -> None:  # already know which node is it
        """Remove node from the list in O(1)."""
        prev = node.prev
        nxt = node.next
        assert prev is not None and nxt is not None
        prev.next = nxt
        nxt.prev = prev
        node.prev = None
        node.next = None

    def _move_to_tail(self, node: _Node) -> None:
        """Move an existing node to MRU position."""
        self._remove(node)
        self._add_to_tail(node)

    def _pop_head(self) -> _Node:
        """Pop the LRU node (first real node after head)."""
        first = self._head.next
        assert first is not None and first is not self._tail
        self._remove(first)
        return first

    def get(self, key: int) -> int:
        """Return value if present; otherwise -1. Marks as recently used.

        Args:
            key: Cache key.

        Returns:
            Value for the key, or -1 if not found.
        """
        node = self._map.get(key)
        if node is None:
            return -1
        self._move_to_tail(node)
        return node.value

    def put(self, key: int, value: int) -> None:
        """Insert or update value; evict least-recently used if over capacity.

        Args:
            key: Cache key.
            value: Value to store.
        """
        node = self._map.get(key)
        if node is not None:
            node.value = value
            self._move_to_tail(node)
            return

        new_node = _Node(key, value)
        self._map[key] = new_node
        self._add_to_tail(new_node)

        if len(self._map) > self.capacity:
            evicted = self._pop_head()
            assert evicted.key is not None
            del self._map[evicted.key]
