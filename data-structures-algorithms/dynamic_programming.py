"""Dynamic Programming - Essential Coding Interview Problems

This module contains 12 carefully selected dynamic programming problems that cover all essential
patterns frequently tested at top tech companies (Google, DeepMind, Facebook, Amazon, etc.).

Problem Difficulty Levels:
- Easy (3): <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> Stairs, House Robber
- Medium (8): Unique Paths, Min Path Sum, LCS, Palindromic Substring, LIS, Coin Change, 0/1 Knapsack, Partition Sum
- Hard (1): Edit Distance

Each problem includes detailed question descriptions, constraints, examples,
hints for optimal solutions, and comprehensive test cases.
"""

# from functools import lru_cache
from typing import List
from collections import Counter  # noqa: F401 - Will be used in student implementations


class DynamicProgrammingPatterns:
    """Essential dynamic programming algorithms for coding interviews."""

    # Q1 ⭐
    def fibonacci(self, n: int) -> int:
        """
        PROBLEM: Fibonacci Number (Easy)

        The Fibonacci numbers, commonly denoted F(n) form a sequence, called the Fi<PERSON>acci sequence,
        such that each number is the sum of the two preceding ones, starting from 0 and 1.
        That is, F(0) = 0, F(1) = 1, F(n) = F(n - 1) + F(n - 2), for n > 1.

        Given n, calculate F(n).

        Example 1:
        Input: n = 2
        Output: 1
        Explanation: F(2) = F(1) + F(0) = 1 + 0 = 1.

        Example 2:
        Input: n = 3
        Output: 2
        Explanation: F(3) = F(2) + F(1) = 1 + 1 = 2.

        Example 3:
        Input: n = 4
        Output: 3
        Explanation: F(4) = F(3) + F(2) = 2 + 1 = 3.

        Constraints:
        - 0 <= n <= 30

        Time Complexity: O(n)
        Space Complexity: O(1) optimized version

        Hint: Use space-optimized approach with two variables to track previous values
        """

        if n <= 1:
            return n

        prev, current = 0, 1

        for _ in range(n - 1):
            prev, current = current, prev + current

        return current

        # --- Solution (commented out) ---
        # if n <= 1:
        #     return n
        #
        # # Space optimized version
        # prev2, prev1 = 0, 1
        #
        # for i in range(2, n + 1):
        #     current = prev1 + prev2
        #     prev2, prev1 = prev1, current
        #
        # return prev1

    # Q2 ⭐
    def climbing_stairs(self, n: int) -> int:
        """
        PROBLEM: Climbing Stairs (Easy)

        You are climbing a staircase. It takes n steps to reach the top.
        Each time you can either climb 1 or 2 steps. In how many distinct ways can you climb to the top?

        Example 1:
        Input: n = 2
        Output: 2
        Explanation: There are two ways to climb to the top.
        1. 1 step + 1 step
        2. 2 steps

        Example 2:
        Input: n = 3
        Output: 3
        Explanation: There are three ways to climb to the top.
        1. 1 step + 1 step + 1 step
        2. 1 step + 2 steps
        3. 2 steps + 1 step

        Constraints:
        - 1 <= n <= 45

        Time Complexity: O(n)
        Space Complexity: O(1)

        Hint: This is identical to Fibonacci sequence. ways(n) = ways(n-1) + ways(n-2)
        """

        if n <= 2:
            return n
        prev, current = 1, 2
        for _ in range(n - 2):
            prev, current = current, prev + current

        return current

        # --- Solution (commented out) ---
        # if n <= 2:
        #     return n
        #
        # prev2, prev1 = 1, 2
        #
        # for i in range(3, n + 1):
        #     current = prev1 + prev2
        #     prev2, prev1 = prev1, current
        #
        # return prev1

    # Q3 ⭐⭐
    def house_robber(self, nums: List[int]) -> int:
        """
        PROBLEM: House Robber (Medium)

        You are a professional robber planning to rob houses along a street. Each house has a certain
        amount of money stashed, the only constraint stopping you from robbing each of them is that
        adjacent houses have security systems connected and it will automatically contact the police
        if two adjacent houses were broken into on the same night.

        Given an integer array nums representing the amount of money of each house, return the maximum
        amount of money you can rob tonight without alerting the police.

        Example 1:
        Input: nums = [1,2,3,1]
        Output: 4
        Explanation: Rob house 1 (money = 1) then rob house 3 (money = 3). Total = 1 + 3 = 4.

        Example 2:
        Input: nums = [2,7,9,3,1]
        Output: 12
        Explanation: Rob house 1 (money = 2), rob house 3 (money = 9) and rob house 5 (money = 1).
        Total amount = 2 + 9 + 1 = 12.

        Constraints:
        - 1 <= nums.length <= 100
        - 0 <= nums[i] <= 400

        Time Complexity: O(n)
        Space Complexity: O(1)

        Hint: For each house, decide whether to rob it or not. DP: rob(i) = max(rob(i-1), rob(i-2) + nums[i])
        """

        if not nums:
            return 0

        if len(nums) == 1:
            return nums[0]

        prev2, prev1 = nums[0], max(nums[0], nums[1])

        for i in range(2, len(nums)):
            current = max(prev1, prev2 + nums[i])
            prev2, prev1 = prev1, current

        return prev1

        # # version 2
        # if not nums:
        #     return 0
        # cache = {}
        # def recursion(idx):
        #     if idx in cache:
        #         return cache[idx]
        #     if idx < 0:
        #         result = 0
        #     elif idx == 0:
        #         result = nums[0]
        #     else:
        #         result = max(recursion(idx-2) + nums[idx], recursion(idx-1))
        #     cache[idx] = result
        #     return result
        # return recursion(len(nums)-1)

        # --- Solution (commented out) ---
        # if not nums:
        #     return 0
        # if len(nums) == 1:
        #     return nums[0]
        #
        # prev2, prev1 = nums[0], max(nums[0], nums[1])
        #
        # for i in range(2, len(nums)):
        #     current = max(prev1, prev2 + nums[i])
        #     prev2, prev1 = prev1, current
        #
        # return prev1

    # Q4 ⭐⭐
    def unique_paths(self, m: int, n: int) -> int:
        """
        PROBLEM: Unique Paths (Medium)

        There is a robot on an m x n grid. The robot is initially located at the top-left corner
        (i.e., grid[0][0]). The robot tries to move to the bottom-right corner (i.e., grid[m - 1][n - 1]).
        The robot can only move either down or right at any point in time.

        Given the two integers m and n, return the number of possible unique paths that the robot
        can take to reach the bottom-right corner.

        Example 1:
        Input: m = 3, n = 7
        Output: 28

        Example 2:
        Input: m = 3, n = 2
        Output: 3
        Explanation: From the top-left corner, there are a total of 3 ways to reach the bottom-right corner:
        1. Right -> Down -> Down
        2. Down -> Down -> Right
        3. Down -> Right -> Down

        Constraints:
        - 1 <= m, n <= 100

        Time Complexity: O(m * n)
        Space Complexity: O(n) space-optimized

        Hint: Grid DP where dp[i][j] = dp[i-1][j] + dp[i][j-1]. Can optimize space to O(n)
        """

        # need to use the optimized version
        dp = [[1] * n for _ in range(m)]

        for i in range(1, m):
            for j in range(1, n):
                dp[i][j] = dp[i - 1][j] + dp[i][j - 1]

        return dp[-1][-1]

        # --- Solution (commented out) ---
        # dp = [1] * n
        #
        # for i in range(1, m):
        #     for j in range(1, n):
        #         dp[j] += dp[j - 1]
        #
        # return int(dp[n - 1])

    # Q5 ⭐⭐
    def min_path_sum(self, grid: List[List[int]]) -> int:
        """
        PROBLEM: Minimum Path Sum (Medium)

        Given a m x n grid filled with non-negative numbers, find a path from top left to bottom right,
        which minimizes the sum of all numbers along its path.

        Note: You can only move either down or right at any point in time.

        Example 1:
        Input: grid = [[1,3,1],[1,5,1],[4,2,1]]
        Output: 7
        Explanation: Because the path 1 → 3 → 1 → 1 → 1 minimizes the sum.

        Example 2:
        Input: grid = [[1,2,3],[4,5,6]]
        Output: 12

        Constraints:
        - m == grid.length
        - n == grid[i].length
        - 1 <= m, n <= 200
        - 0 <= grid[i][j] <= 100

        Time Complexity: O(m * n)
        Space Complexity: O(n) space-optimized

        Hint: Similar to unique paths but track minimum sum. dp[i][j] = min(dp[i-1][j], dp[i][j-1]) + grid[i][j]
        """

        # 2D DP version
        m, n = len(grid), len(grid[0])
        dp = [[0] * n for _ in range(m)]

        dp[0][0] = grid[0][0]
        for j in range(1, n):
            dp[0][j] = dp[0][j - 1] + grid[0][j]
        for i in range(1, m):
            dp[i][0] = dp[i - 1][0] + grid[i][0]
        for i in range(1, m):
            for j in range(1, n):
                dp[i][j] = min(dp[i - 1][j], dp[i][j - 1]) + grid[i][j]

        return dp[m - 1][n - 1]

        # 1D space-optimized version
        # m, n = len(grid), len(grid[0])
        # dp = [float("inf")] * n
        # dp[0] = 0

        # for i in range(m):
        #     dp[0] += grid[i][0]
        #     for j in range(1, n):
        #         dp[j] = min(dp[j], dp[j - 1]) + grid[i][j]

        # return int(dp[-1])

        # --- Solution (commented out) ---
        # m, n = len(grid), len(grid[0])
        # dp = [10**18] * n
        # dp[0] = 0
        #
        # for i in range(m):
        #     dp[0] += grid[i][0]
        #     for j in range(1, n):
        #         dp[j] = min(dp[j], dp[j - 1]) + grid[i][j]
        #
        # return dp[n - 1]

    # Q6 ⭐⭐⭐
    def edit_distance(self, word1: str, word2: str) -> int:
        """
        PROBLEM: Edit Distance (Hard)

        Given two strings word1 and word2, return the minimum number of operations required
        to convert word1 to word2.

        You have the following three operations permitted on a word:
        - Insert a character
        - Delete a character
        - Replace a character

        Example 1:
        Input: word1 = "horse", word2 = "ros"
        Output: 3
        Explanation:
        horse -> rorse (replace 'h' with 'r')
        rorse -> rose (remove 'r')
        rose -> ros (remove 'e')

        Example 2:
        Input: word1 = "intention", word2 = "execution"
        Output: 5
        Explanation:
        intention -> inention (remove 't')
        inention -> enention (replace 'i' with 'e')
        enention -> exention (replace 'n' with 'x')
        exention -> exection (replace 'n' with 'c')
        exection -> execution (insert 'u')

        Constraints:
        - 0 <= word1.length, word2.length <= 500
        - word1 and word2 consist of lowercase English letters

        Time Complexity: O(m * n)
        Space Complexity: O(min(m, n)) space-optimized

        Hint: Classic 2D DP. If characters match, no operation needed. Otherwise, take minimum of insert, delete, replace.
        """

        # 1D space-optimized DP version
        m, n = len(word1), len(word2)
        if m < n:
            word1, word2 = word2, word1
            m, n = n, m

        prev = list(range(n + 1))
        for i in range(1, m + 1):
            curr = [i] + [0] * n
            for j in range(1, n + 1):
                if word1[i - 1] == word2[j - 1]:
                    curr[j] = prev[j - 1]
                else:
                    delete_cost = prev[j] + 1
                    insert_cost = curr[j - 1] + 1
                    replace_cost = prev[j - 1] + 1
                    curr[j] = min(delete_cost, insert_cost, replace_cost)
            prev = curr

        return prev[n]  # return prev[n] not curr[n]

    def edit_distance_2d(self, word1: str, word2: str) -> int:
        """Edit distance using classic 2D DP table.

        Builds a (m+1) x (n+1) table where dp[i][j] is the edit distance between
        word1[:i] and word2[:j].

        Time Complexity: O(m * n)
        Space Complexity: O(m * n)
        """

        m, n = len(word1), len(word2)
        dp = [[0] * (n + 1) for _ in range(m + 1)]

        for i in range(m + 1):
            dp[i][0] = i
        for j in range(n + 1):
            dp[0][j] = j

        for i in range(1, m + 1):
            for j in range(1, n + 1):
                if word1[i - 1] == word2[j - 1]:
                    dp[i][j] = dp[i - 1][j - 1]
                else:
                    dp[i][j] = 1 + min(
                        dp[i - 1][j],  # delete
                        dp[i][j - 1],  # insert
                        dp[i - 1][j - 1],  # replace
                    )

        return dp[m][n]

    # Q7 ⭐⭐
    def longest_common_subsequence(self, text1: str, text2: str) -> int:
        """
        PROBLEM: Longest Common Subsequence (Medium)

        Given two strings text1 and text2, return the length of their longest common subsequence.
        If there is no common subsequence, return 0.

        A subsequence of a string is a new string generated from the original string with some
        characters (can be none) deleted without changing the relative order of the remaining characters.

        Example 1:
        Input: text1 = "abcde", text2 = "ace"
        Output: 3
        Explanation: The longest common subsequence is "ace" and its length is 3.

        Example 2:
        Input: text1 = "abc", text2 = "abc"
        Output: 3
        Explanation: The longest common subsequence is "abc" and its length is 3.

        Example 3:
        Input: text1 = "abc", text2 = "def"
        Output: 0
        Explanation: There is no such common subsequence, so the result is 0.

        Constraints:
        - 1 <= text1.length, text2.length <= 1000
        - text1 and text2 consist of only lowercase English characters

        Time Complexity: O(m * n)
        Space Complexity: O(min(m, n)) space-optimized

        Hint: If characters match, add 1 to LCS of remaining. Otherwise, take max of skipping either character.
        """

        ## version 1
        # m = len(text1)
        # n = len(text2)

        # # define recursion function for i, j
        # @lru_cache(maxsize=None)
        # def recursion(i, j):
        #     if i == m or j == n:
        #         return 0
        #     if text1[i] == text2[j]:
        #         return 1 + recursion(i+1, j+1)
        #     else:
        #         return max(recursion(i+1, j), recursion(i, j+1))

        # return recursion(0, 0)

        ## version 2
        # m = len(text1)
        # n = len(text2)
        # dp = [[-1]*n for _ in range(m)]

        # def recursion(i, j):

        #     if i == m or j == n:
        #         return 0

        #     if dp[i][j] != -1:
        #         return dp[i][j]

        #     if text1[i] == text2[j]:
        #         result = 1+recursion(i+1, j+1)
        #     else:
        #         result = max(recursion(i+1, j), recursion(i, j+1))
        #     dp[i][j] = result
        #     return result
        # return recursion(0, 0)

        # --- Solution (commented out) ---
        m, n = len(text1), len(text2)
        if m < n:
            text1, text2 = text2, text1
            m, n = n, m

        prev = [0] * (n + 1)
        for i in range(1, m + 1):
            curr = [0] * (n + 1)
            for j in range(1, n + 1):
                if text1[i - 1] == text2[j - 1]:
                    curr[j] = prev[j - 1] + 1
                else:
                    curr[j] = max(prev[j], curr[j - 1])
            prev = curr

        return prev[n]

    # Q8 ⭐⭐
    def longest_palindromic_substring(self, s: str) -> str:
        """
        PROBLEM: Longest Palindromic Substring (Medium)

        Given a string s, return the longest palindromic substring in s.
        A palindrome is a string that reads the same forward and backward.

        Example 1:
        Input: s = "babad"
        Output: "bab"
        Explanation: "aba" is also a valid answer since both are palindromes of length 3.

        Example 2:
        Input: s = "cbbd"
        Output: "bb"
        Explanation: The longest palindromic substring is "bb" with length 2.

        Example 3:
        Input: s = "racecar"
        Output: "racecar"
        Explanation: The entire string is a palindrome.

        Example 4:
        Input: s = "ac"
        Output: "a"
        Explanation: Both "a" and "c" are palindromes of length 1, either can be returned.

        Constraints:
        - 1 <= s.length <= 1000
        - s consists of only lowercase and uppercase English letters and digits
        - If multiple palindromes of the same maximum length exist, return any one of them

        Approaches & Complexity:
        1. Expand Around Centers: O(n²) time, O(1) space (recommended)
        2. Dynamic Programming: O(n²) time, O(n²) space
        3. Manacher's Algorithm: O(n) time, O(n) space (advanced)

        Hint: For each character position, expand around center to find palindromes.
        Consider both odd-length (center at character) and even-length (center between characters) palindromes.
        Track the longest palindrome found and its starting position.
        """

        # # --- Dynamic Programming Solution ---
        # n = len(s)
        # if n == 0:
        #     return ""

        # # dp[i][j] = True if s[i:j+1] is a palindrome
        # dp = [[False] * n for _ in range(n)]

        # start = 0  # Starting index of longest palindrome
        # max_len = 1  # Length of longest palindrome found

        # # Every single character is a palindrome
        # for i in range(n):
        #     dp[i][i] = True

        # # Check for 2-character palindromes
        # for i in range(n - 1):
        #     if s[i] == s[i + 1]:
        #         dp[i][i + 1] = True
        #         start = i
        #         max_len = 2

        # # Check for palindromes of length 3 and above
        # # length = current palindrome length we're checking
        # for length in range(3, n + 1):
        #     # i = starting index of current substring
        #     for i in range(n - length + 1):
        #         j = i + length - 1  # ending index of current substring

        #         # Check if s[i:j+1] is palindrome
        #         # It's palindrome if: first and last chars match AND inner substring is palindrome
        #         if s[i] == s[j] and dp[i + 1][j - 1]:
        #             dp[i][j] = True
        #             start = i
        #             max_len = length

        # return s[start:start + max_len]

        # # --- Expand around centers ---
        def expand_around_center(left: int, right: int) -> int:
            while left >= 0 and right < len(s) and s[left] == s[right]:
                left -= 1
                right += 1
            return right - left - 1

        start = 0
        max_len = 0
        for i in range(len(s)):
            len1 = expand_around_center(i, i)
            len2 = expand_around_center(i, i + 1)
            current_max = max(len1, len2)
            if current_max > max_len:
                max_len = current_max
                start = i - (current_max - 1) // 2
        return s[start : start + max_len]

        # # --- Manacher's Algorithm (O(n) time, O(n) space) ---
        # # Preprocess string: "abba" -> "#a#b#b#a#"
        # def preprocess(s):
        #     if not s:
        #         return "#"
        #     result = "#"
        #     for char in s:
        #         result += char + "#"
        #     return result
        #
        # processed = preprocess(s)
        # n = len(processed)
        # P = [0] * n  # P[i] = radius of palindrome centered at i
        # center = 0   # Center of rightmost palindrome
        # right = 0    # Right boundary of rightmost palindrome
        #
        # max_len = 0
        # center_index = 0
        #
        # for i in range(n):
        #     # Mirror of i with respect to center
        #     mirror = 2 * center - i
        #
        #     # If i is within right boundary, use symmetry
        #     if i < right:
        #         P[i] = min(right - i, P[mirror])
        #
        #     # Try to expand palindrome centered at i
        #     try:
        #         while (i + P[i] + 1 < n and i - P[i] - 1 >= 0 and
        #                processed[i + P[i] + 1] == processed[i - P[i] - 1]):
        #             P[i] += 1
        #     except IndexError:
        #         pass
        #
        #     # If palindrome centered at i extends past right, update center and right
        #     if i + P[i] > right:
        #         center = i
        #         right = i + P[i]
        #
        #     # Update maximum length palindrome found
        #     if P[i] > max_len:
        #         max_len = P[i]
        #         center_index = i
        #
        # # Extract result from original string
        # start = (center_index - max_len) // 2
        # return s[start:start + max_len]

    # Q9 ⭐⭐
    def longest_increasing_subsequence(self, nums: List[int]) -> int:
        """
        PROBLEM: Longest Increasing Subsequence (Medium)

        Given an integer array nums, return the length of the longest strictly increasing subsequence.
        A subsequence is a sequence that can be derived from the array by deleting some or no elements
        without changing the order of the remaining elements.

        Example 1:
        Input: nums = [10,9,2,5,3,7,101,18]
        Output: 4
        Explanation: The longest increasing subsequence is [2,3,7,101], therefore the length is 4.

        Example 2:
        Input: nums = [0,1,0,3,2,3]
        Output: 4
        Explanation: The longest increasing subsequence is [0,1,2,3].

        Example 3:
        Input: nums = [7,7,7,7,7,7,7]
        Output: 1
        Explanation: The longest increasing subsequence is any single element.

        Constraints:
        - 1 <= nums.length <= 2500
        - -10^4 <= nums[i] <= 10^4

        Time Complexity: O(n log n) with binary search (optimal)
        Space Complexity: O(n)

        Hint: Maintain array of smallest tail elements for each length. Use binary search to find position.
        """

        if not nums:
            return 0

        def find_position(nums, target):
            # find left most position to insert target to maintain sorted order
            left, right = 0, len(nums)
            while left < right:
                mid = (left + right) // 2
                mid_val = nums[mid]
                if target > mid_val:
                    left = mid + 1  # tend to advance to right
                else:
                    right = mid
            return left

        tails = []
        for num in nums:
            pos = find_position(tails, num)
            if pos == len(tails):
                tails.append(num)
            else:
                tails[pos] = num
        return len(tails)

        # if not nums:
        #     return 0

        # def binary_search_left(arr: List[int], target: int) -> int:
        #     """Find leftmost position to insert target to maintain sorted order."""
        #     left, right = 0, len(arr)
        #     while left < right:
        #         mid = (left + right) // 2
        #         if arr[mid] < target:
        #             left = mid + 1
        #         else:
        #             right = mid
        #     return left

        # tails = []  # tails[i] = smallest ending element of increasing subsequence of length i+1

        # for num in nums:
        #     # Find position to insert/replace using binary search
        #     pos = binary_search_left(tails, num)
        #     if pos == len(tails):
        #         # num is larger than all elements, extend sequence
        #         tails.append(num)
        #     else:
        #         # Replace element to keep smallest possible ending
        #         tails[pos] = num

        # return len(tails)

    # Q10 ⭐⭐
    def coin_change(self, coins: List[int], amount: int) -> int:
        """
        PROBLEM: Coin Change (Medium)

        You are given an integer array coins representing coins of different denominations and an
        integer amount representing a total amount of money.

        Return the fewest number of coins that you need to make up that amount. If that amount
        of money cannot be made up by any combination of the coins, return -1.

        You may assume that you have an infinite number of each kind of coin.

        Example 1:
        Input: coins = [1,3,4], amount = 6
        Output: 2
        Explanation: 6 = 3 + 3

        Example 2:
        Input: coins = [2], amount = 3
        Output: -1

        Example 3:
        Input: coins = [1], amount = 0
        Output: 0

        Constraints:
        - 1 <= coins.length <= 12
        - 1 <= coins[i] <= 231 - 1
        - 0 <= amount <= 104

        Time Complexity: O(amount * len(coins))
        Space Complexity: O(amount)

        Hint: Classic unbounded knapsack. For each amount, try all coins and take minimum.
        """

        dp = [float("inf")] * (amount + 1)  # from dollar 0 to dollar amount
        dp[0] = 0

        for amt in range(1, amount + 1):
            for coin in coins:
                if coin <= amt:
                    dp[amt] = min(dp[amt], dp[amt - coin] + 1)

        return int(dp[amount]) if dp[amount] != float("inf") else -1

        # if amount == 0:
        #     return 0
        #
        # dp = [amount + 1] * (amount + 1)
        # dp[0] = 0
        #
        # for target in range(1, amount + 1):
        #     best = dp[target]
        #     for coin in coins:
        #         if coin <= target:
        #             candidate = dp[target - coin] + 1
        #             if candidate < best:
        #                 best = candidate
        #     dp[target] = best
        #
        # return dp[amount] if dp[amount] <= amount else -1

    # Q11 ⭐⭐
    def knapsack_01(self, weights: List[int], values: List[int], capacity: int) -> int:
        """
        PROBLEM: 0/1 Knapsack (Medium)

        You are given weights and values of n items, put these items in a knapsack of capacity W
        to get the maximum total value in the knapsack. Each item can be taken at most once.

        Example 1:
        Input: weights = [1,3,4,5], values = [1,4,5,7], capacity = 7
        Output: 9
        Explanation: Take items with weights [3,4] and values [4,5] for total value 9

        Example 2:
        Input: weights = [2,3,4,5], values = [3,4,5,6], capacity = 5
        Output: 7
        Explanation: Take items with weights [2,3] and values [3,4] for total value 7

        Constraints:
        - 1 <= n <= 1000
        - 1 <= weights[i], values[i] <= 1000
        - 1 <= capacity <= 1000

        Time Complexity: O(n * capacity)
        Space Complexity: O(capacity) space-optimized

        Hint: Let dp[w] represent the best value achievable with capacity w after considering the
        current prefix of items. For each item, compare the value of skipping it (dp[w]) versus taking
        it (dp[w - weight] + value). Update capacities from high to low so an item is counted at most
        once and you don't reuse the freshly updated values in the same iteration.
        """

        # 1D version
        # n = len(weights)
        # dp = [0] * (capacity + 1)

        # for i in range(n):
        #     # Iterate backwards to avoid using updated values
        #     for w in range(capacity, weights[i] - 1, -1):
        #         dp[w] = max(dp[w], dp[w - weights[i]] + values[i])

        # return dp[capacity]

        # 2D version
        n = len(weights)
        dp = [[0] * (capacity + 1) for _ in range(n + 1)]
        for i in range(1, n + 1):  # outerloop, for each item, increase items one by one
            for w in range(capacity + 1):
                if weights[i - 1] <= w:  # if current weight <= current capacity
                    dp[i][w] = max(
                        dp[i - 1][w], dp[i - 1][w - weights[i - 1]] + values[i - 1]
                    )
                else:
                    dp[i][w] = dp[i - 1][w]  # don't include
        return dp[n][capacity]

    # Q12 ⭐⭐
    def can_partition(self, nums: List[int]) -> bool:
        """
        PROBLEM: Partition Equal Subset Sum (Medium)

        Given a non-empty array nums containing only positive integers, find if the array
        can be partitioned into two subsets such that the sum of elements in both subsets is equal.

        Example 1:
        Input: nums = [1,5,11,5]
        Output: true
        Explanation: The array can be partitioned as [1, 5, 5] and [11].

        Example 2:
        Input: nums = [1,2,3,5]
        Output: false
        Explanation: The array cannot be partitioned into equal sum subsets.

        Constraints:
        - 1 <= nums.length <= 200
        - 1 <= nums[i] <= 100

        Time Complexity: O(n * sum)
        Space Complexity: O(sum)

        Hint: This is subset sum problem. If total sum is odd, return false. Otherwise find subset with sum = total_sum // 2
        """

        total_sum = sum(nums)
        if total_sum % 2 == 1:
            return False

        half_sum = total_sum // 2

        # find subset equal to half_sum

        # dp[i][j] = can we make sum j using first i numbers
        dp = [[False] * (half_sum + 1) for _ in range(len(nums) + 1)]

        # Base case: we can always make sum 0 (empty subset)
        for i in range(len(nums) + 1):
            dp[i][0] = True

        for i in range(1, len(nums) + 1):
            for j in range(1, half_sum + 1):
                if nums[i - 1] <= j:
                    # Can include: check both skip OR include
                    dp[i][j] = dp[i - 1][j] or dp[i - 1][j - nums[i - 1]]
                else:
                    # Can't include: must skip
                    dp[i][j] = dp[i - 1][j]

        return dp[len(nums)][half_sum]

        # --- Solution (commented out) ---
        # total_sum = sum(nums)
        #
        # if total_sum % 2 != 0:
        #     return False
        #
        # target = total_sum // 2
        # dp = [False] * (target + 1)
        # dp[0] = True
        #
        # for num in nums:
        #     for j in range(target, num - 1, -1):
        #         dp[j] = dp[j] or dp[j - num]
        #
        # return dp[target]


# ========== DEMONSTRATION & TESTING ==========


def demonstrate_patterns():
    """Demonstrate comprehensive test cases for the 12 essential DP problems."""

    print("=== COMPREHENSIVE DP PROBLEMS TESTING ===\n")

    # Q1: Fibonacci Number
    print("1. Fibonacci Number (Easy ⭐):")
    test_cases_1 = [
        (0, 0),  # Base case
        (1, 1),  # Base case
        (2, 1),  # Basic case
        (5, 5),  # Medium case
        (10, 55),  # Larger case
        (15, 610),  # Edge of constraint
    ]

    for i, (n, expected) in enumerate(test_cases_1):
        patterns = DynamicProgrammingPatterns()
        result = patterns.fibonacci(n)
        status = "✅" if result == expected else "❌"
        print(f"   Test {i + 1}: fib({n}) → {result} (expected: {expected}) {status}")

    # Q2: Climbing Stairs
    print("\n2. Climbing Stairs (Easy ⭐):")
    test_cases_2 = [
        (1, 1),  # Single step
        (2, 2),  # Basic case
        (3, 3),  # Basic case
        (4, 5),  # Fibonacci pattern
        (5, 8),  # Larger case
        (10, 89),  # Edge case
    ]

    for i, (n, expected) in enumerate(test_cases_2):
        patterns = DynamicProgrammingPatterns()
        result = patterns.climbing_stairs(n)
        status = "✅" if result == expected else "❌"
        print(
            f"   Test {i + 1}: stairs({n}) → {result} (expected: {expected}) {status}"
        )

    # Q3: House Robber
    print("\n3. House Robber (Medium ⭐⭐):")
    test_cases_3 = [
        ([1, 2, 3, 1], 4),  # Basic case
        ([2, 7, 9, 3, 1], 12),  # Example from problem
        ([1], 1),  # Single house
        ([2, 1], 2),  # Two houses
        ([5, 1, 3, 9], 14),  # Non-adjacent optimal
        ([1, 2, 1, 1], 3),  # Multiple choices
    ]

    for i, (houses, expected) in enumerate(test_cases_3):
        patterns = DynamicProgrammingPatterns()
        result = patterns.house_robber(houses)
        status = "✅" if result == expected else "❌"
        print(
            f"   Test {i + 1}: houses={houses} → {result} (expected: {expected}) {status}"
        )

    # Q4: Unique Paths
    print("\n4. Unique Paths (Medium ⭐⭐):")
    test_cases_4 = [
        (3, 7, 28),  # Example from problem
        (3, 2, 3),  # Example from problem
        (1, 1, 1),  # Single cell
        (2, 2, 2),  # Small grid
        (3, 3, 6),  # Square grid
        (7, 3, 28),  # Symmetric case
    ]

    for i, (m, n, expected) in enumerate(test_cases_4):
        patterns = DynamicProgrammingPatterns()
        result = patterns.unique_paths(m, n)
        status = "✅" if result == expected else "❌"
        print(
            f"   Test {i + 1}: grid({m}x{n}) → {result} (expected: {expected}) {status}"
        )

    # Q5: Minimum Path Sum
    print("\n5. Minimum Path Sum (Medium ⭐⭐):")
    test_cases_5 = [
        ([[1, 3, 1], [1, 5, 1], [4, 2, 1]], 7),  # Example from problem
        ([[1, 2, 3], [4, 5, 6]], 12),  # Example from problem
        ([[1]], 1),  # Single cell
        ([[1, 2], [1, 1]], 3),  # Small grid
        ([[5, 0, 1], [2, 2, 1]], 7),  # With zeros (corrected)
    ]

    for i, (grid, expected) in enumerate(test_cases_5):
        patterns = DynamicProgrammingPatterns()
        result = patterns.min_path_sum(grid)
        status = "✅" if result == expected else "❌"
        print(
            f"   Test {i + 1}: grid={grid} → {result} (expected: {expected}) {status}"
        )

    # Q6: Edit Distance
    print("\n6. Edit Distance (Hard ⭐⭐⭐):")
    test_cases_6 = [
        ("horse", "ros", 3),  # Example from problem
        ("intention", "execution", 5),  # Example from problem
        ("", "abc", 3),  # Empty string
        ("abc", "", 3),  # To empty string
        ("abc", "abc", 0),  # Identical strings
        ("a", "b", 1),  # Single character replace
    ]

    for i, (word1, word2, expected) in enumerate(test_cases_6):
        patterns = DynamicProgrammingPatterns()
        result = patterns.edit_distance(word1, word2)
        status = "✅" if result == expected else "❌"
        print(
            f"   Test {i + 1}: '{word1}' → '{word2}' = {result} (expected: {expected}) {status}"
        )

    # Q7: Longest Common Subsequence
    print("\n7. Longest Common Subsequence (Medium ⭐⭐):")
    test_cases_7 = [
        ("abcde", "ace", 3),  # Example from problem
        ("abc", "abc", 3),  # Identical strings
        ("abc", "def", 0),  # No common subsequence
        ("", "abc", 0),  # Empty string
        ("abcdgh", "aedfhr", 3),  # Multiple matches
        ("aggtab", "gxtxayb", 4),  # Complex case
    ]

    for i, (text1, text2, expected) in enumerate(test_cases_7):
        patterns = DynamicProgrammingPatterns()
        result = patterns.longest_common_subsequence(text1, text2)
        status = "✅" if result == expected else "❌"
        print(
            f"   Test {i + 1}: '{text1}' & '{text2}' → {result} (expected: {expected}) {status}"
        )

    # Q8: Longest Palindromic Substring
    print("\n8. Longest Palindromic Substring (Medium ⭐⭐):")
    test_cases_8 = [
        ("babad", {"bab", "aba"}),  # Example (either valid)
        ("cbbd", {"bb"}),  # Example from problem
        ("a", {"a"}),  # Single character
        ("ac", {"a", "c"}),  # No palindrome > 1
        ("racecar", {"racecar"}),  # Full string palindrome
        ("abcdef", {"a", "b", "c", "d", "e", "f"}),  # All single chars
    ]

    for i, (s, expected_set) in enumerate(test_cases_8):
        patterns = DynamicProgrammingPatterns()
        result = patterns.longest_palindromic_substring(s)
        status = "✅" if result in expected_set else "❌"
        print(
            f"   Test {i + 1}: '{s}' → '{result}' (expected: {expected_set}) {status}"
        )

    # Q9: Longest Increasing Subsequence
    print("\n9. Longest Increasing Subsequence (Medium ⭐⭐):")
    test_cases_9 = [
        ([10, 9, 2, 5, 3, 7, 101, 18], 4),  # Example from problem
        ([0, 1, 0, 3, 2, 3], 4),  # Example from problem
        ([7, 7, 7, 7, 7, 7, 7], 1),  # All same elements
        ([1, 3, 6, 7, 9, 4, 10, 5, 6], 6),  # Complex case
        ([1], 1),  # Single element
        ([], 0),  # Empty array
    ]

    for i, (nums, expected) in enumerate(test_cases_9):
        patterns = DynamicProgrammingPatterns()
        result = patterns.longest_increasing_subsequence(nums)
        status = "✅" if result == expected else "❌"
        print(f"   Test {i + 1}: {nums} → {result} (expected: {expected}) {status}")

    # Q10: Coin Change
    print("\n10. Coin Change (Medium ⭐⭐):")
    test_cases_10 = [
        ([1, 3, 4], 6, 2),  # Example from problem
        ([2], 3, -1),  # Impossible case
        ([1], 0, 0),  # Zero amount
        ([1, 2, 5], 11, 3),  # Standard case
        ([2, 3, 5], 1, -1),  # Another impossible
        ([1, 4, 5], 8, 2),  # Multiple solutions
    ]

    for i, (coins, amount, expected) in enumerate(test_cases_10):
        patterns = DynamicProgrammingPatterns()
        result = patterns.coin_change(coins, amount)
        status = "✅" if result == expected else "❌"
        print(
            f"   Test {i + 1}: coins={coins}, amount={amount} → {result} (expected: {expected}) {status}"
        )

    # Q11: 0/1 Knapsack
    print("\n11. 0/1 Knapsack (Medium ⭐⭐):")
    test_cases_11 = [
        ([1, 3, 4, 5], [1, 4, 5, 7], 7, 9),  # Example from problem
        ([2, 3, 4, 5], [3, 4, 5, 6], 5, 7),  # Example from problem
        ([1], [1], 1, 1),  # Single item
        ([2, 3], [1, 4], 3, 4),  # Choose optimal
        ([1, 2, 3], [6, 10, 12], 5, 22),  # All items fit
    ]

    for i, (weights, values, capacity, expected) in enumerate(test_cases_11):
        patterns = DynamicProgrammingPatterns()
        result = patterns.knapsack_01(weights, values, capacity)
        status = "✅" if result == expected else "❌"
        print(
            f"   Test {i + 1}: W={weights}, V={values}, C={capacity} → {result} (expected: {expected}) {status}"
        )

    # Q12: Partition Equal Subset Sum
    print("\n12. Partition Equal Subset Sum (Medium ⭐⭐):")
    test_cases_12 = [
        ([1, 5, 11, 5], True),  # Example from problem
        ([1, 2, 3, 5], False),  # Example from problem
        ([1], False),  # Single odd element
        ([2], False),  # Single even element
        ([1, 1], True),  # Equal elements
        ([1, 1, 1, 1], True),  # All same (even count)
        ([1, 2, 5], False),  # Odd sum
    ]

    for i, (nums, expected) in enumerate(test_cases_12):
        patterns = DynamicProgrammingPatterns()
        result = patterns.can_partition(nums)
        status = "✅" if result == expected else "❌"
        print(f"   Test {i + 1}: {nums} → {result} (expected: {expected}) {status}")

    print("\n" + "=" * 80)
    print("COMPREHENSIVE TEST SUMMARY:")
    print("- ✅ All implementations tested with edge cases")
    print("- 🎯 Covers: empty inputs, single elements, duplicates, negatives, extremes")
    print("- 🚀 Ready for Google/Meta/DeepMind interviews!")
    print("- 📚 12 Essential DP patterns mastered (3 Easy + 8 Medium + 1 Hard)")


if __name__ == "__main__":
    demonstrate_patterns()
