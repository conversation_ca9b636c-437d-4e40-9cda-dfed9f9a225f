"""
Matrices - Essential 2D Array Problems for Coding Interviews

This module contains 4 carefully selected matrix problems that cover all essential
patterns frequently tested at top tech companies (Google, Facebook, Amazon, etc.).

Problem Difficulty Levels:
- Medium (4): Spiral Matrix, Rotate Image, Set Matrix Zeroes, Search Matrix

Each problem includes detailed question descriptions, constraints, examples,
and hints for optimal solutions.
"""

from typing import List


class MatrixPatterns:
    """Essential matrix algorithms for coding interviews."""

    # Q1 ⭐⭐
    def spiral_order(self, matrix: List[List[int]]) -> List[int]:
        """
        PROBLEM: Spiral Matrix (Medium)

        Given an m x n matrix, return all elements of the matrix in spiral order.

        Example 1:
        Input: matrix = [[1,2,3],[4,5,6],[7,8,9]]
        Output: [1,2,3,6,9,8,7,4,5]
        Explanation: Start from top-left, go right → down → left → up

        Example 2:
        Input: matrix = [[1,2,3,4],[5,6,7,8],[9,10,11,12]]
        Output: [1,2,3,4,8,12,11,10,9,5,6,7]

        Constraints:
        - m == matrix.length
        - n == matrix[i].length
        - 1 <= m, n <= 10
        - -100 <= matrix[i][j] <= 100

        Time Complexity: O(m * n)
        Space Complexity: O(1) not counting output

        Hints:
        1. Use four boundaries: top, bottom, left, right
        2. Move in spiral: right → down → left → up
        3. Shrink boundaries after each direction
        4. Check boundaries before moving in each direction
        """
        if not matrix or not matrix[0]:
            return []

        top, bottom = 0, len(matrix) - 1
        left, right = 0, len(matrix[0]) - 1
        result = []

        while left <= right and top <= bottom:
            # Go right
            for c in range(left, right + 1):
                result.append(matrix[top][c])
            top += 1

            # Go down
            for r in range(top, bottom + 1):
                result.append(matrix[r][right])
            right -= 1

            # Go left (if we still have rows)
            if top <= bottom:
                for c in range(right, left - 1, -1):
                    result.append(matrix[bottom][c])
                bottom -= 1

            # Go up (if we still have columns)
            if left <= right:
                for r in range(bottom, top - 1, -1):
                    result.append(matrix[r][left])
                left += 1

        return result

    # Q2 ⭐⭐
    def rotate_image(self, matrix: List[List[int]]) -> None:
        """
        PROBLEM: Rotate Image (Medium)

        You are given an n x n 2D matrix representing an image, rotate the image by 90 degrees (clockwise).
        You have to rotate the image in-place, which means you have to modify the input 2D matrix directly.
        DO NOT allocate another 2D matrix and do the rotation.

        Example 1:
        Input: matrix = [[1,2,3],[4,5,6],[7,8,9]]
        Output: [[7,4,1],[8,5,2],[9,6,3]]

        Example 2:
        Input: matrix = [[5,1,9,11],[2,4,8,10],[13,3,6,7],[15,14,12,16]]
        Output: [[15,13,2,5],[14,3,4,1],[12,6,8,9],[16,7,10,11]]

        Constraints:
        - n == matrix.length == matrix[i].length
        - 1 <= n <= 20
        - -1000 <= matrix[i][j] <= 1000

        Time Complexity: O(n^2)
        Space Complexity: O(1)

        Hints:
        1. Two-step approach: transpose + reverse each row
        2. Alternative: rotate four elements at a time in layers
        3. Transpose: matrix[i][j] ↔ matrix[j][i]
        4. Then reverse each row to complete 90° rotation
        """
        n = len(matrix)

        # Step 1: Transpose the matrix
        for i in range(n):
            for j in range(i + 1, n):
                matrix[i][j], matrix[j][i] = matrix[j][i], matrix[i][j]

        # Step 2: Reverse each row
        for row in matrix:
            row.reverse()

    # Q3 ⭐⭐
    def set_zeroes(self, matrix: List[List[int]]) -> None:
        """
        PROBLEM: Set Matrix Zeroes (Medium)

        Given an m x n integer matrix matrix, if an element is 0, set its entire row and column to 0's.
        You must do it in place.

        Example 1:
        Input: matrix = [[1,1,1],[1,0,1],[1,1,1]]
        Output: [[1,0,1],[0,0,0],[1,0,1]]

        Example 2:
        Input: matrix = [[0,1,2,0],[3,4,5,2],[1,3,1,5]]
        Output: [[0,0,0,0],[0,4,5,0],[0,3,1,0]]

        Constraints:
        - m == matrix.length
        - n == matrix[0].length
        - 1 <= m, n <= 200
        - -2^31 <= matrix[i][j] <= 2^31 - 1

        Follow up: A straightforward solution using O(mn) space is probably a bad idea.
        A simple improvement uses O(m + n) space, but still not the best.
        Could you devise a constant space solution?

        Time Complexity: O(m * n)
        Space Complexity: O(1)

        Hints:
        1. Use first row and first column as markers
        2. Use separate variables for first row/column zeros
        3. Process markers after setting interior zeros
        """
        if not matrix or not matrix[0]:
            return

        m, n = len(matrix), len(matrix[0])

        # Check if first row/column should be zero: O(m+n)
        first_row_zero = any(matrix[0][c] == 0 for c in range(n))
        first_col_zero = any(matrix[r][0] == 0 for r in range(m))

        # Use first row and column as markers: O(mxn)
        for r in range(1, m):
            for c in range(1, n):
                if matrix[r][c] == 0:
                    matrix[r][0] = 0
                    matrix[0][c] = 0

        # Set zeros based on markers # O(mxn)
        for r in range(1, m):
            if matrix[r][0] == 0:
                for c in range(n):
                    matrix[r][c] = 0

        for c in range(1, n):  # O(mxn)
            if matrix[0][c] == 0:
                for r in range(m):
                    matrix[r][c] = 0

        # Handle first row and column
        if first_row_zero:
            for c in range(n):
                matrix[0][c] = 0

        if first_col_zero:
            for r in range(m):
                matrix[r][0] = 0

    # Q4 ⭐⭐
    def search_matrix(self, matrix: List[List[int]], target: int) -> bool:
        """
        PROBLEM: Search a 2D Matrix (Medium)

        Write an efficient algorithm that searches for a value target in an m x n integer matrix.
        This matrix has the following properties:
        - Integers in each row are sorted from left to right
        - The first integer of each row is greater than the last integer of the previous row

        Example 1:
        Input: matrix = [[1,3,5,7],[10,11,16,20],[23,30,34,60]], target = 3
        Output: true

        Example 2:
        Input: matrix = [[1,3,5,7],[10,11,16,20],[23,30,34,60]], target = 13
        Output: false

        Constraints:
        - m == matrix.length
        - n == matrix[i].length
        - 1 <= m, n <= 100
        - -10^4 <= matrix[i][j], target <= 10^4

        Time Complexity: O(log(m * n))
        Space Complexity: O(1)

        Hints:
        1. Treat the 2D matrix as a 1D sorted array
        2. Use binary search with index conversion
        3. Convert 1D index to 2D: row = mid // n, col = mid % n
        """
        if not matrix or not matrix[0]:
            return False

        m, n = len(matrix), len(matrix[0])
        left, right = 0, m * n - 1

        while left <= right:
            mid = (left + right) // 2
            mid_value = matrix[mid // n][mid % n]

            if mid_value == target:
                return True
            elif mid_value < target:
                left = mid + 1
            else:
                right = mid - 1

        return False


def demonstrate_patterns():
    """Demonstrate comprehensive test cases for the 4 essential matrix problems."""

    print("=== COMPREHENSIVE MATRIX PROBLEMS TESTING ===\n")

    # Q1: Spiral Matrix
    print("1. Spiral Matrix (Medium ★★):")
    test_cases_1 = [
        ([[1, 2, 3], [4, 5, 6], [7, 8, 9]], [1, 2, 3, 6, 9, 8, 7, 4, 5]),  # 3x3 square
        (
            [[1, 2, 3, 4], [5, 6, 7, 8], [9, 10, 11, 12]],
            [1, 2, 3, 4, 8, 12, 11, 10, 9, 5, 6, 7],
        ),  # 3x4 rectangle
        ([[1, 2, 3]], [1, 2, 3]),  # Single row
        ([[1], [2], [3]], [1, 2, 3]),  # Single column
        ([[1]], [1]),  # Single element
        ([[1, 2], [3, 4], [5, 6]], [1, 2, 4, 6, 5, 3]),  # 3x2 rectangle
        ([], []),  # Empty matrix
    ]

    for i, (matrix, expected) in enumerate(test_cases_1):
        patterns = MatrixPatterns()
        result = patterns.spiral_order(matrix)
        status = "✅" if result == expected else "❌"
        print(
            f"   Test {i + 1}: matrix={matrix} → {result} (expected: {expected}) {status}"
        )

    # Q2: Rotate Image
    print("\n2. Rotate Image (Medium ★★):")
    test_cases_2 = [
        ([[1, 2, 3], [4, 5, 6], [7, 8, 9]], [[7, 4, 1], [8, 5, 2], [9, 6, 3]]),  # 3x3
        (
            [[5, 1, 9, 11], [2, 4, 8, 10], [13, 3, 6, 7], [15, 14, 12, 16]],
            [[15, 13, 2, 5], [14, 3, 4, 1], [12, 6, 8, 9], [16, 7, 10, 11]],
        ),  # 4x4
        ([[1, 2], [3, 4]], [[3, 1], [4, 2]]),  # 2x2
        ([[1]], [[1]]),  # 1x1
        (
            [
                [1, 2, 3, 4, 5],
                [6, 7, 8, 9, 10],
                [11, 12, 13, 14, 15],
                [16, 17, 18, 19, 20],
                [21, 22, 23, 24, 25],
            ],
            [
                [21, 16, 11, 6, 1],
                [22, 17, 12, 7, 2],
                [23, 18, 13, 8, 3],
                [24, 19, 14, 9, 4],
                [25, 20, 15, 10, 5],
            ],
        ),  # 5x5
    ]

    for i, (matrix_input, expected) in enumerate(test_cases_2):
        patterns = MatrixPatterns()
        # Make a copy since rotate modifies in-place
        matrix_copy = [row[:] for row in matrix_input]
        patterns.rotate_image(matrix_copy)
        status = "✅" if matrix_copy == expected else "❌"
        print(
            f"   Test {i + 1}: {matrix_input} → {matrix_copy} (expected: {expected}) {status}"
        )

    # Q3: Set Matrix Zeroes
    print("\n3. Set Matrix Zeroes (Medium ★★):")
    test_cases_3 = [
        (
            [[1, 1, 1], [1, 0, 1], [1, 1, 1]],
            [[1, 0, 1], [0, 0, 0], [1, 0, 1]],
        ),  # Single zero
        (
            [[0, 1, 2, 0], [3, 4, 5, 2], [1, 3, 1, 5]],
            [[0, 0, 0, 0], [0, 4, 5, 0], [0, 3, 1, 0]],
        ),  # Multiple zeros
        (
            [[1, 2, 3, 4], [5, 0, 7, 8], [0, 10, 11, 12]],
            [[0, 0, 3, 4], [0, 0, 0, 0], [0, 0, 0, 0]],
        ),  # Edge zeros
        ([[0]], [[0]]),  # Single zero element
        ([[1]], [[1]]),  # Single non-zero element
        ([[1, 0], [0, 1]], [[0, 0], [0, 0]]),  # Diagonal zeros
        ([[1, 2, 3], [4, 5, 6]], [[1, 2, 3], [4, 5, 6]]),  # No zeros
    ]

    for i, (matrix_input, expected) in enumerate(test_cases_3):
        patterns = MatrixPatterns()
        # Make a copy since set_zeroes modifies in-place
        matrix_copy = [row[:] for row in matrix_input]
        patterns.set_zeroes(matrix_copy)
        status = "✅" if matrix_copy == expected else "❌"
        print(
            f"   Test {i + 1}: {matrix_input} → {matrix_copy} (expected: {expected}) {status}"
        )

    # Q4: Search a 2D Matrix
    print("\n4. Search a 2D Matrix (Medium ★★):")
    test_cases_4 = [
        ([[1, 3, 5, 7], [10, 11, 16, 20], [23, 30, 34, 60]], 3, True),  # Found
        ([[1, 3, 5, 7], [10, 11, 16, 20], [23, 30, 34, 60]], 13, False),  # Not found
        ([[1]], 1, True),  # Single element found
        ([[1]], 2, False),  # Single element not found
        ([[1, 3, 5, 7], [10, 11, 16, 20], [23, 30, 34, 60]], 1, True),  # First element
        ([[1, 3, 5, 7], [10, 11, 16, 20], [23, 30, 34, 60]], 60, True),  # Last element
        ([], 1, False),  # Empty matrix
        (
            [
                [1, 4, 7, 11, 15],
                [2, 5, 8, 12, 19],
                [3, 6, 9, 16, 22],
                [10, 13, 14, 17, 24],
                [18, 21, 23, 26, 30],
            ],
            5,
            True,
        ),  # 5x5
    ]

    for i, (matrix, target, expected) in enumerate(test_cases_4):
        patterns = MatrixPatterns()
        result = patterns.search_matrix(matrix, target)
        status = "✅" if result == expected else "❌"
        print(
            f"   Test {i + 1}: matrix={matrix}, target={target} → {result} (expected: {expected}) {status}"
        )

    print("\n" + "=" * 80)
    print("COMPREHENSIVE TEST SUMMARY:")
    print("- ✅ All implementations tested with edge cases")
    print("- 🎯 Covers: empty inputs, single elements, edge boundaries, invalid cases")
    print("- 🚀 Ready for Google/Meta/Amazon interviews!")
    print("- 📚 4 Medium matrix patterns mastered")
    print("- 🔥 Essential 2D array manipulation techniques covered")


if __name__ == "__main__":
    demonstrate_patterns()
