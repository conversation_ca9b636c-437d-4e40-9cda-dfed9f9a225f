"""
Sorting - Core Algorithms for Coding Interviews (Practice Mode)

This module includes the most important sorting algorithms you should know.
Implement each using the TODO stubs; compare with the commented solutions.
"""

from typing import List


class SortingPatterns:
    """Common sorting algorithms with practice stubs."""

    def merge_sort(self, nums: List[int]) -> List[int]:
        """
        Merge Sort (stable, O(n log n) time, O(n) space).
        """

        def sort(arr):
            if len(arr) <= 1:
                return arr
            mid = len(arr) // 2
            left = sort(arr[:mid])
            right = sort(arr[mid:])
            return merge(left, right)

        def merge(a, b):
            i = j = 0
            out = []
            while i < len(a) and j < len(b):
                if a[i] <= b[j]:
                    out.append(a[i])
                    i += 1
                else:
                    out.append(b[j])
                    j += 1
            out.extend(a[i:])
            out.extend(b[j:])
            return out

        return sort(nums)

    def quick_sort(self, nums: List[int]) -> List[int]:
        """
        Quick Sort (average O(n log n), worst O(n^2), in-place possible).
        Uses Hoare partition scheme with inclusive bounds.
        """
        arr = nums[:]

        def qs(left, right):  # sort range [left, right] inclusive
            if left >= right:
                return

            pivot = arr[(left + right) // 2]
            i, j = left, right

            while i <= j:
                while arr[i] < pivot:
                    i += 1
                while arr[j] > pivot:
                    j -= 1
                if i <= j:
                    arr[i], arr[j] = arr[j], arr[i]
                    i += 1
                    j -= 1

            qs(left, j)
            qs(i, right)

        qs(0, len(arr) - 1)
        return arr

    def counting_sort_nonnegative(self, nums: List[int]) -> List[int]:
        """
        Counting Sort for non-negative integers (O(n + k) time, O(k) space).

        A non-comparison based sorting algorithm that works by counting occurrences
        of each distinct element, then using arithmetic to determine positions.

        Algorithm Steps:
        1. Find the maximum value in the input array to determine range [0, max]
        2. Create a count array of size (max + 1) initialized to zeros
        3. Count occurrences: iterate through input, increment count[value]
        4. Reconstruct sorted array: for each count[i] > 0, add i to output count[i] times

        Example:
        Input:  [4, 2, 2, 8, 3, 3, 1]
        Max:    8, so count array size = 9
        Count:  [0, 1, 2, 2, 1, 0, 0, 0, 1]  (indices 0-8)
        Output: [1, 2, 2, 3, 3, 4, 8]

        When to Use:
        - Small range of non-negative integers (k ≪ n² where k = max_value)
        - When stability is not required (this version is not stable)
        - Linear time sorting is needed and range constraint is acceptable

        Time Complexity:  O(n + k) where n = length, k = max_value
        Space Complexity: O(k) for the counting array

        Constraints:
        - Only works with non-negative integers
        - Efficient only when k (range) is not significantly larger than n (size)
        - Not suitable for floating-point numbers or negative values

        Args:
            nums: List of non-negative integers to sort

        Returns:
            New sorted list (original list unchanged)
        """
        if not nums:
            return []

        max_val = max(nums)
        count = [0] * (max_val + 1)

        # Count occurrences of each value
        for val in nums:
            count[val] += 1

        # Reconstruct sorted array
        result = []
        for val, freq in enumerate(count):
            if freq > 0:
                result.extend([val] * freq)

        return result


def test_sorting_algorithms():
    """Comprehensive test suite for sorting algorithms with edge cases."""
    sorter = SortingPatterns()

    # Test cases covering various edge cases and scenarios
    test_cases = [
        ("Empty array", []),
        ("Single element", [42]),
        ("Already sorted", [1, 2, 3, 4, 5]),
        ("Reverse sorted", [5, 4, 3, 2, 1]),
        ("Basic unsorted", [5, 2, 8, 1, 9, 3]),
        ("With duplicates", [3, 1, 4, 1, 5, 9, 2, 6, 5, 3]),
        ("All same elements", [7, 7, 7, 7, 7]),
        ("Negative numbers", [-3, -1, -4, -1, -5]),
        ("Mixed pos/neg", [-2, 5, -8, 3, 0, -1, 4]),
        ("Two elements", [9, 1]),
        ("Large array", list(range(20, 0, -1))),  # [20, 19, 18, ..., 1]
    ]

    print("=" * 80)
    print("COMPREHENSIVE SORTING ALGORITHM TESTS")
    print("=" * 80)

    for test_name, test_array in test_cases:
        print(f"\n🧪 Test: {test_name}")
        print(
            f"Input:  {test_array if len(test_array) <= 15 else str(test_array[:10]) + '...' + str(test_array[-5:])}"
        )

        # Initialize results
        merge_result = None
        quick_result = None
        count_result = None

        # Test merge sort
        try:
            merge_result = sorter.merge_sort(test_array.copy())
            print(
                f"Merge:  {merge_result if len(merge_result) <= 15 else str(merge_result[:10]) + '...' + str(merge_result[-5:])}"
            )
        except Exception as e:
            print(f"Merge:  ERROR - {e}")

        # Test quick sort
        try:
            quick_result = sorter.quick_sort(test_array.copy())
            print(
                f"Quick:  {quick_result if len(quick_result) <= 15 else str(quick_result[:10]) + '...' + str(quick_result[-5:])}"
            )
        except Exception as e:
            print(f"Quick:  ERROR - {e}")

        # Test counting sort (only for non-negative integers)
        if test_array and all(isinstance(x, int) and x >= 0 for x in test_array):
            try:
                count_result = sorter.counting_sort_nonnegative(test_array.copy())
                print(
                    f"Count:  {count_result if len(count_result) <= 15 else str(count_result[:10]) + '...' + str(count_result[-5:])}"
                )
            except Exception as e:
                print(f"Count:  ERROR - {e}")
        else:
            print("Count:  N/A (contains negative numbers or non-integers)")

        # Verify correctness
        if test_array:
            expected = sorted(test_array)
            merge_correct = (
                merge_result == expected if merge_result is not None else False
            )
            quick_correct = (
                quick_result == expected if quick_result is not None else False
            )

            status = "✅ PASS" if merge_correct and quick_correct else "❌ FAIL"
            print(f"Status: {status}")

    print("\n" + "=" * 80)
    print("Test Summary: All major edge cases covered")
    print("- Empty arrays, single elements, sorted/unsorted arrays")
    print("- Duplicates, negatives, mixed values, large arrays")
    print("=" * 80)


def demonstrate_patterns():
    """Legacy demo function - use test_sorting_algorithms() for comprehensive testing."""
    print("Running comprehensive tests instead...")
    test_sorting_algorithms()


if __name__ == "__main__":
    demonstrate_patterns()
