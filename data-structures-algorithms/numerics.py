"""
Numerical Methods - Online Statistics and Optimization (Practice Mode)
"""

from __future__ import annotations

from dataclasses import dataclass
from typing import Iterable, <PERSON>ple
import random
import math


@dataclass
class WelfordStats:
    """PROBLEM: Online Statistics with Welford's Algorithm (Medium)

    Implement online computation of mean and variance using <PERSON><PERSON><PERSON>'s algorithm.
    This algorithm is numerically stable and avoids catastrophic cancellation
    that can occur with naive variance calculation methods.

    The algorithm maintains running statistics without storing all values,
    making it memory-efficient for streaming data scenarios.

    Example Usage:
    stats = WelfordStats()
    stats.update(2.0)  # mean=2.0, variance=0.0
    stats.update(4.0)  # mean=3.0, variance=2.0
    stats.update(4.0)  # mean=3.33, variance=1.33

    Mathematical Foundation:
    - Uses recurrence relations to update mean and variance incrementally
    - M2 tracks sum of squared differences from current mean
    - Variance = M2 / (count - 1) for sample variance

    Constraints:
    - Works with any floating-point numbers
    - Handles edge cases (count < 2 for variance)
    - Numerically stable even for large datasets

    Time Complexity: O(1) per update operation
    Space Complexity: O(1) - constant memory usage

    Hints:
    1. Update count first, then mean using: new_mean = old_mean + (x - old_mean) / count
    2. Update M2 using: M2 += (x - old_mean) * (x - new_mean)
    3. Sample variance = M2 / (count - 1), population variance = M2 / count
    4. Return 0.0 for variance when count < 2 (undefined sample variance)
    """

    count: int = 0
    mean: float = 0.0
    M2: float = 0.0  # Sum of squared differences from the current mean

    def update(self, x: float) -> None:
        """Incorporate one new sample into running statistics.

        Args:
            x: New data point to incorporate into running statistics

        Updates count, mean, and M2 (sum of squared differences) using
        Welford's numerically stable recurrence relations.
        """

        self.count += 1
        new_mean = self.mean + (x - self.mean) / self.count
        self.M2 += (x - self.mean) * (x - new_mean)
        self.mean = new_mean

    def variance(self) -> float:
        """Return sample variance; 0.0 if count < 2.

        Returns:
            Sample variance (M2 / (count - 1)) or 0.0 if insufficient data

        Note:
            Sample variance divides by (n-1) for unbiased estimation.
            Population variance would divide by n.
        """
        return self.M2 / (self.count - 1) if self.count >= 2 else 0.0


def gradient_descent_logistic(
    X: Iterable[Iterable[float]], y: Iterable[int], *, lr: float, epochs: int
) -> Tuple[list, float]:
    """PROBLEM: Logistic Regression with Gradient Descent (Medium)

    Implement batch gradient descent optimization for binary logistic regression.
    This is a fundamental machine learning algorithm that learns to classify
    binary outcomes using the sigmoid function and maximum likelihood estimation.

    Mathematical Foundation:
    - Sigmoid function: σ(z) = 1 / (1 + e^(-z)) where z = w·x + b
    - Cost function: J = -1/m * Σ[y*log(σ(z)) + (1-y)*log(1-σ(z))]
    - Gradients: ∂J/∂w = 1/m * X^T * (σ(z) - y), ∂J/∂b = 1/m * Σ(σ(z) - y)

    Example:
    X = [[1, 2], [2, 3], [3, 1], [4, 3]]  # Feature vectors
    y = [0, 0, 1, 1]                       # Binary labels
    weights, bias = gradient_descent_logistic(X, y, lr=0.01, epochs=1000)
    # Returns trained parameters for binary classification

    Args:
        X: Iterable of feature vectors (m samples × n features)
        y: Iterable of binary labels {0, 1} (m samples)
        lr: Learning rate (step size for gradient updates)
        epochs: Number of training iterations

    Returns:
        (weights, bias): Tuple of learned weight vector and bias term

    Constraints:
    - Labels must be binary (0 or 1)
    - Learning rate should be small (typically 0.001 to 0.1)
    - Features may need normalization for best convergence

    Time Complexity: O(epochs x m x n) where m=samples, n=features
    Space Complexity: O(n) for weights storage

    Hints:
    1. Initialize weights randomly (small values) and bias to 0
    2. Convert iterables to lists/arrays for multiple passes
    3. Implement sigmoid function: 1 / (1 + exp(-z))
    4. For each epoch: compute predictions, calculate gradients, update parameters
    5. Use numerical stability tricks (clip extreme values in sigmoid)
    6. Consider adding regularization (L1/L2) to prevent overfitting
    """
    # Convert to concrete lists for multiple passes
    X_list = [list(row) for row in X]
    y_list = list(y)

    if not X_list:
        raise ValueError("X must contain at least one sample")
    if len(X_list) != len(y_list):
        raise ValueError("X and y must have the same number of samples")
    if epochs <= 0:
        raise ValueError("epochs must be a positive integer")
    if lr <= 0.0:
        raise ValueError("lr (learning rate) must be positive")

    num_features = len(X_list[0])
    for i, row in enumerate(X_list):
        if len(row) != num_features:
            raise ValueError(
                f"All rows in X must have the same length; row {i} has length {len(row)} vs {num_features}"
            )
    for i, label in enumerate(y_list):
        if label not in (0, 1):
            raise ValueError(f"y must be binary (0 or 1); got y[{i}]={label}")

    # Initialize weights (small random values) and bias
    weight_init_scale = 1e-2
    weights = [
        random.uniform(-weight_init_scale, weight_init_scale)
        for _ in range(num_features)
    ]
    bias = 0.0

    def dot(u: list[float], v: list[float]) -> float:
        """Compute dot product between two equal-length lists."""
        total = 0.0
        for a, b in zip(u, v):
            total += a * b
        return total

    def sigmoid(z: float) -> float:
        """Numerically stable sigmoid."""
        # Clip to avoid overflow in exp for large |z|
        if z > 60.0:
            return 1.0
        if z < -60.0:
            return 0.0
        return 1.0 / (1.0 + math.exp(-z))

    m = len(X_list)
    for _ in range(epochs):
        grad_w = [0.0] * num_features
        grad_b = 0.0

        for xi, yi in zip(X_list, y_list):
            z = dot(weights, xi) + bias
            pi = sigmoid(z)
            error = pi - float(yi)
            grad_b += error
            for j in range(num_features):
                grad_w[j] += error * xi[j]

        inv_m = 1.0 / float(m)
        for j in range(num_features):
            weights[j] -= lr * (grad_w[j] * inv_m)
        bias -= lr * (grad_b * inv_m)

    return weights, bias


def demo_welford_stats() -> Tuple[float, float]:
    """Demonstrate using WelfordStats on a small stream.

    Returns:
        (mean, variance): The sample mean and variance after processing the data.
    """
    data = [2.0, 4.0, 4.0]
    stats = WelfordStats()
    for value in data:
        stats.update(value)
    return stats.mean, stats.variance()


def demo_gradient_descent_logistic() -> Tuple[list, float]:
    """Demonstrate list-based logistic regression training on a toy dataset.

    Uses a fixed random seed for reproducibility and trains on the dataset
    shown in the function's docstring example.

    Returns:
        (weights, bias): Learned parameters from gradient descent.
    """
    # Toy dataset from the docstring
    X = [[1, 2], [2, 3], [3, 1], [4, 3]]
    y = [0, 0, 1, 1]

    # Fixed seed for deterministic initialization
    random.seed(42)
    return gradient_descent_logistic(X, y, lr=0.05, epochs=1000)


if __name__ == "__main__":
    # Demo and assertions for WelfordStats
    mean, var = demo_welford_stats()
    print(f"WelfordStats demo -> mean={mean:.6f}, variance={var:.6f}")
    expected_mean = (2.0 + 4.0 + 4.0) / 3.0
    expected_var = 4.0 / 3.0  # sample variance for [2,4,4]
    assert abs(mean - expected_mean) < 1e-9, (
        f"Mean incorrect: {mean} vs {expected_mean}"
    )
    assert abs(var - expected_var) < 1e-9, (
        f"Variance incorrect: {var} vs {expected_var}"
    )

    # Demo and assertions for Logistic Regression
    weights, bias = demo_gradient_descent_logistic()
    print(f"LogisticRegression demo -> weights={weights}, bias={bias:.6f}")

    # Evaluate on training set to confirm correctness
    X = [[1, 2], [2, 3], [3, 1], [4, 3]]
    y = [0, 0, 1, 1]

    def _sigmoid(z: float) -> float:
        if z > 60.0:
            return 1.0
        if z < -60.0:
            return 0.0
        return 1.0 / (1.0 + math.exp(-z))

    correct = 0
    for xi, yi in zip(X, y):
        z = sum(w * x for w, x in zip(weights, xi)) + bias
        pred = 1 if _sigmoid(z) >= 0.5 else 0
        correct += int(pred == yi)
    acc = correct / len(y)
    print(f"Training accuracy: {acc:.3f} ({correct}/{len(y)})")
    assert acc == 1.0, "Training accuracy should be 1.0 on the toy dataset"
