# DeepMind Research Engineer Interview Prep Plan

## Topics & Patterns

## Arrays & Strings
- [x] **`arrays_and_strings.py`** ⭐ 🔥 - Universal patterns (13)
- [x] **`strings_math_edge.py`** ⭐⭐ - (optional) KMP/Z, Rolling Hash, Bits, Combinatorics (9)

## Hashing
- [x] **`hash_table.py`** ⭐⭐ 🔥 - Custom implementation (6)
- [x] **`hash_maps_sets.py`** ⭐ 🔥 - Hash-based solutions (9)

## Sorting & Intervals
- [x] **`sorting.py`** ⭐ 🔥 - Comparison algorithms (3)
- [x] **`intervals.py`** ⭐⭐ - Range-based problems (8)

## Linked Lists
- [x] **`linked_lists.py`** ⭐ 🔥 - Pointer manipulation (10)

## Stacks, Queues & Heaps
- [x] **`stacks_queues.py`** ⭐ 🔥 - Linear data structures (10)
- [x] **`heaps.py`** ⭐⭐ 🔥 - Priority queues, k-way problems (8)

## Trees, BST & Trie
- [x] **`trees_bst.py`** ⭐⭐ 🔥 - Tree traversals, BST operations (14)
- [x] **`trie.py`** ⭐⭐ 🔥 - String processing, autocomplete (4)

## Graphs (CRITICAL)
- [x] **`graphs.py`** ⭐⭐⭐⭐ 🔥 **HIGHEST PRIORITY** (15)
  - Graph traversals (BFS/DFS)
  - Shortest path (Dijkstra)
  - Topological sort (Kahn)
  - Union-Find & connected components
  - Minimum spanning tree (Kruskal)
  - Bipartite check (graph coloring)
  - Cycle detection (undirected)
  - Grid DFS (Number of Islands)

## Dynamic Programming (CRITICAL)
- [x] **`dynamic_programming.py`** ⭐⭐⭐⭐ 🔥 **CRITICAL** (12 comprehensive)
  - **Comprehensive Learning**: 1D DP (Fibonacci, Climbing Stairs, House Robber), 2D Grid DP (Unique Paths, Minimum Path Sum), String DP (Edit Distance, LCS, Palindromic Substring), Sequence DP (LIS), Knapsack & Partition (Coin Change, 0/1 Knapsack, Partition Equal Subset Sum)
  - **Practice Module**: Clean templates for hands-on implementation of core patterns
  - **Unified Structure**: Both detailed examples and practice templates in one organized module

## Recursion & Backtracking
- [x] **`recursion_backtracking.py`** ⭐⭐ - Factorial, Subsets, Permutations, Combination Sum (4)

## Greedy
- [x] **`greedy.py`** ⭐⭐ - Jump Game; Assign Cookies (2)

## Matrices & Grids
- [x] **`matrices.py`** ⭐⭐⭐⭐ (4)
  - Spiral order traversal
  - In-place rotate image
  - Set matrix zeroes (O(1) space)
  - Binary search in sorted matrix

## Numerics
- [x] **`numerics.py`** ⭐⭐ - Statistical algorithms (2)

## Streaming / Caching
- [x] **`streaming.py`** ⭐⭐ - Large-scale systems (1)
  - LRU Cache (O(1) get/put via hashmap + doubly linked list)

## Game Search (AI specialization)
- [x] **`game_search.py`** ⭐⭐⭐⭐⭐ (1)
  - Minimax, alpha-beta pruning
  - Multi-agent systems
  - Strategic AI

 

**Legend:**
- 🔥 = Critical for DeepMind
- ⭐ = Difficulty level
- [x] = Completed
- [ ] = To do
