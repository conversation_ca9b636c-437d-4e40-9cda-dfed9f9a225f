"""
Greedy Algorithms - Essential Coding Interview Problems

This module contains a curated, high-signal set of greedy problems frequently
tested in top tech interviews. Each problem includes a clear statement,
constraints, examples, time/space complexity, and a hint for an optimal greedy
approach.

Included Problems:
- Jump Game (Medium)
- Assign Cookies (Easy)
"""

from typing import List


class GreedyPatterns:
    """Essential greedy algorithms for coding interviews."""

    def jump_game(self, nums: List[int]) -> bool:
        """
        PROBLEM: Jump Game (Medium)

        Given an integer array nums where each element represents your maximum jump
        length from that position, return True if you can reach the last index, or
        False otherwise.

        Example 1:
        Input: nums = [2,3,1,1,4]
        Output: True
        Explanation: 0 -> 1 -> 4

        Example 2:
        Input: nums = [3,2,1,0,4]
        Output: False
        Explanation: You cannot move past index 3 (value 0).

        Constraints:
        - 1 <= nums.length <= 10^5
        - 0 <= nums[i] <= 10^5

        Time Complexity: O(n)
        Space Complexity: O(1)

        Hint:
        Track the farthest index you can reach so far. If your current index ever
        exceeds this farthest reach, you cannot proceed and the answer is False.
        At each position i, update the farthest reach to max(current_farthest, i + nums[i]).
        The greedy insight: we only care about the maximum reachable position, not the path.
        """

        far = 0
        for i, step in enumerate(nums):
            if i > far:
                return False
            far = max(far, i + step)
        return True

    def assign_cookies(self, children: List[int], cookies: List[int]) -> int:
        """
        PROBLEM: Assign Cookies (Easy)

        You are given two integer arrays children and cookies where children[i] is the
        minimum size of a cookie that the i-th child will be content with, and
        cookies[j] is the size of the j-th cookie. Each child can receive at most
        one cookie and each cookie can be assigned to at most one child.
        Return the maximum number of content children.

        Example 1:
        Input: children = [1,2], cookies = [1,2,3]
        Output: 2

        Example 2:
        Input: children = [1,2,3], cookies = [1,1]
        Output: 1

        Constraints:
        - 0 <= len(children), len(cookies) <= 3 * 10^4
        - 1 <= children[i], cookies[j] <= 2^31 - 1

        Time Complexity: O(n log n + m log m) due to sorting
        Space Complexity: O(1) extra

        Hint:
        Sort both arrays. Use two pointers to greedily assign the smallest cookie
        that satisfies the current child.
        """

        children.sort()
        cookies.sort()
        i = j = 0

        while i < len(children) and j < len(cookies):
            if cookies[j] >= children[i]:
                i += 1
                j += 1
            else:
                j += 1  # move to next cookie
        return i


def demonstrate_patterns():
    """Demonstrate curated test runs for the 2 essential greedy problems."""
    g = GreedyPatterns()
    print("=== GREEDY PROBLEMS: COMPREHENSIVE TESTING ===\n")

    # Jump Game
    print("1. Jump Game (Medium ★★):")
    test_cases_jg = [
        ([2, 3, 1, 1, 4], True),  # Basic reachable
        ([3, 2, 1, 0, 4], False),  # Blocked by zero
        ([0], True),  # Single element
        ([2, 0], True),  # Simple reach
        ([1, 0, 1, 0], False),  # Early stop
        ([2, 0, 2, 0, 1], True),  # Reach with gaps
        ([1, 2, 0, 1, 0, 2, 0], False),  # Stuck before end
    ]
    for i, (nums, expected) in enumerate(test_cases_jg):
        result = g.jump_game(nums)
        status = "✅" if result == expected else "❌"
        print(
            f"   Test {i + 1}: nums={nums} → {result} (expected: {expected}) {status}"
        )

    # Assign Cookies
    print("\n2. Assign Cookies (Easy ★):")
    test_cases_ac = [
        ([1, 2], [1, 2, 3], 2),  # Enough cookies
        ([1, 2, 3], [1, 1], 1),  # Not enough large cookies
        ([], [1, 2], 0),  # No children
        ([2, 2, 3], [], 0),  # No cookies
        ([1, 2, 2, 3], [1, 1, 2], 2),  # Mixed sizes
        ([1, 1, 1], [1, 1], 2),  # Duplicates
        ([1, 2, 3], [3, 3, 3], 3),  # All satisfied
    ]
    for i, (children, cookies, expected) in enumerate(test_cases_ac):
        result = g.assign_cookies(children, cookies)
        status = "✅" if result == expected else "❌"
        print(
            f"   Test {i + 1}: children={children}, cookies={cookies} → {result} (expected: {expected}) {status}"
        )

    print("\n" + "=" * 80)


if __name__ == "__main__":
    demonstrate_patterns()
