"""
Trie (Prefix Tree) - Essential Coding Interview Problems

This module contains 3 carefully selected trie problems that cover all essential
patterns frequently tested at top tech companies (Google, Facebook, Amazon, etc.).

Problem Difficulty Levels:
- Medium (3): Implement Trie, Word Search, Add and Search Word

Each problem includes detailed question descriptions, constraints, examples,
and hints for optimal solutions.
"""

from typing import Dict, List


class TrieNode:
    """Node for Trie structure."""

    def __init__(self):
        self.children: Dict[str, "TrieNode"] = {}
        self.is_end: bool = False


class TriePatterns:
    """Essential trie algorithms for coding interviews."""

    def __init__(self):
        """Initialize the trie data structure."""
        self.root = TrieNode()

    # Q1 ⭐⭐
    def insert(self, word: str) -> None:
        """
        PROBLEM: Implement Trie (Prefix Tree) - Insert (Medium)

        A trie (pronounced as "try") or prefix tree is a tree data structure used to efficiently
        store and retrieve keys in a dataset of strings. There are various applications of this
        data structure, such as autocomplete and spellchecker.

        Implement the Trie class:
        - <PERSON>e() Initializes the trie object.
        - void insert(String word) Inserts the string word into the trie.
        - boolean search(String word) Returns true if the string word is in the trie.
        - boolean startsWith(String prefix) Returns true if there is a previously inserted string
          that has the prefix prefix.

        Example 1:
        Input: ["Trie", "insert", "search", "search", "startsWith", "insert", "search"]
               [[], ["apple"], ["apple"], ["app"], ["app"], ["app"], ["app"]]
        Output: [null, null, true, false, true, null, true]
        Explanation:
        Trie trie = new Trie();
        trie.insert("apple");
        trie.search("apple");   // return True
        trie.search("app");     // return False
        trie.startsWith("app"); // return True
        trie.insert("app");
        trie.search("app");     // return True

        Constraints:
        - 1 <= word.length, prefix.length <= 2000
        - word and prefix consist only of lowercase English letters
        - At most 3 * 10^4 calls in total will be made to insert, search, and startsWith

        Time Complexity: O(m) where m is the key length
        Space Complexity: O(ALPHABET_SIZE * N * M) where N is number of keys, M is average length

        Hint: Use a tree where each node contains links to its children and a boolean flag for end of word
        """
        # Guard: ignore empty string inserts to avoid marking root as a word

        if word == "":
            return

        node = self.root

        for ch in word:
            if ch not in node.children:
                node.children[ch] = TrieNode()
            node = node.children[ch]

        node.is_end = True

    # Q2 ⭐⭐
    def search(self, word: str) -> bool:
        """
        PROBLEM: Implement Trie (Prefix Tree) - Search (Medium)

        Returns true if the string word is in the trie (i.e., was inserted before),
        and false otherwise.

        Example:
        Input: word = "apple" (after inserting "apple")
        Output: true

        Input: word = "app" (after inserting "apple" but not "app")
        Output: false

        Time Complexity: O(m) where m is the word length
        Space Complexity: O(1)

        Hint: Traverse the trie following the word characters, check if final node has is_end = True
        """

        node = self.root

        for ch in word:
            nxt = node.children.get(ch)
            if nxt is None:
                return False
            node = nxt

        return node.is_end

    # Q3 ⭐⭐
    def starts_with(self, prefix: str) -> bool:
        """
        PROBLEM: Implement Trie (Prefix Tree) - StartsWith (Medium)

        Returns true if there is a previously inserted string that has the prefix prefix,
        and false otherwise.

        Example 1:
        Input: prefix = "app" (after inserting "apple")
        Output: true
        Explanation: "apple" starts with "app"

        Example 2:
        Input: prefix = "appl" (after inserting "apple")
        Output: true

        Example 3:
        Input: prefix = "apx" (after inserting "apple")
        Output: false

        Time Complexity: O(m) where m is the prefix length
        Space Complexity: O(1)

        Hint: Similar to search but don't check is_end flag, just verify path exists
        """

        node = self.root
        for ch in prefix:
            nxt = node.children.get(ch)
            if nxt is None:
                return False
            node = nxt
        return True

    def query(self, prefix: str, k: int) -> List[str]:
        """Return up to k lexicographically smallest words with the given prefix.

        Problem: Autocomplete Query (Medium)

        Given a prefix, return up to k lexicographically smallest words that start with that
        prefix. If fewer than k words exist, return all of them. If the prefix does not exist
        in the trie, return an empty list. If k <= 0, return an empty list.

        Approach:
        1) Traverse to the node representing the prefix.
        2) DFS from that node, exploring children in sorted order to generate words in
           lexicographic order, short-circuiting once k words are collected.

        Time Complexity: O(m + R) where m is len(prefix) to locate the node and R is the
        number of characters visited to collect up to k words. In practice, R ~ O(k * L)
        where L is the average additional length of returned words.
        Space Complexity: O(L) for recursion depth/stack and building results.
        """
        if k <= 0:
            return []

        # 1) Walk down to the end of the prefix
        node = self.root
        for ch in prefix:
            nxt = node.children.get(ch)
            if nxt is None:
                return []
            node = nxt

        # 2) DFS in lexicographic order from the prefix node
        results: List[str] = []

        def dfs(cur: TrieNode, path: List[str]) -> None:
            if len(results) >= k:
                return
            if cur.is_end:
                results.append(prefix + "".join(path))
                if len(results) >= k:
                    return
            for ch in sorted(cur.children.keys()):
                path.append(ch)
                dfs(cur.children[ch], path)
                path.pop()

        dfs(node, [])
        return results


def demonstrate_patterns():
    """Demonstrate comprehensive test cases for the 3 essential trie problems."""

    print("=== COMPREHENSIVE TRIE PROBLEMS TESTING ===\n")

    # Test basic trie operations
    print("1. Basic Trie Operations (Medium ★★):")
    trie = TriePatterns()

    # Test case 1: Basic insert and search
    test_words = ["apple", "app", "application", "apply"]
    for word in test_words:
        trie.insert(word)
        print(f"   Insert '{word}' → search('{word}'): {trie.search(word)}")

    # Test case 2: Search non-existent words
    non_existent = ["appl", "banana", "ap"]
    for word in non_existent:
        result = trie.search(word)
        print(f"   Search '{word}' (not inserted): {result}")

    # Test case 3: Prefix testing
    print("\n2. Prefix Operations (Medium ★★):")
    prefixes = ["app", "appl", "apple", "applic", "xyz", ""]
    for prefix in prefixes:
        result = trie.starts_with(prefix)
        print(f"   StartsWith '{prefix}': {result}")

    # Autocomplete query
    print("\n2b. Autocomplete Query:")
    print(f"   query('app', 3): {trie.query('app', 3)}")
    print(f"   query('ap', 10): {trie.query('ap', 10)}")
    print(f"   query('xyz', 5): {trie.query('xyz', 5)}")
    print(f"   query('', 3): {trie.query('', 3)}")
    print(f"   query('app', 0): {trie.query('app', 0)}")

    # Test case 4: Edge cases
    print("\n3. Edge Cases:")
    edge_trie = TriePatterns()

    # Single character
    edge_trie.insert("a")
    print(f"   Single char - insert('a'), search('a'): {edge_trie.search('a')}")
    print(f"   Single char - starts_with('a'): {edge_trie.starts_with('a')}")

    # Empty prefix
    print(f"   Empty prefix - starts_with(''): {edge_trie.starts_with('')}")

    # Nested words
    nested_trie = TriePatterns()
    nested_words = ["cat", "cats", "caterpillar"]
    for word in nested_words:
        nested_trie.insert(word)

    print("\n4. Nested Words Test:")
    for word in nested_words:
        print(f"   Search '{word}': {nested_trie.search(word)}")

    nested_prefixes = ["ca", "cat", "cats", "cate"]
    for prefix in nested_prefixes:
        print(f"   StartsWith '{prefix}': {nested_trie.starts_with(prefix)}")

    print("\n" + "=" * 80)
    print("COMPREHENSIVE TEST SUMMARY:")
    print("- ✅ All trie operations tested with edge cases")
    print("- 🎯 Covers: empty inputs, single chars, nested words, non-existent lookups")
    print("- 🚀 Ready for Google/Meta/Amazon interviews!")
    print("- 📚 3 Medium problem patterns mastered")


if __name__ == "__main__":
    demonstrate_patterns()
